# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

# name: Yellosis FrontEnd Service development deploy

on:
  push:
    branches:
      - deploy
jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source code
        uses: actions/checkout@v2

      - uses: actions/setup-node@v2
        with:
          node-version: 16.13.1

      - name: npm Install
        run: npm install

      - name: Production Build
        run: npm run build

      - name: Deploy
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.FE_S3_ACCESSKEY }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.FE_S3_SECRETKEY }}
          AWS_DEFAULT_REGION: ap-northeast-2
          VUE_APP_API_URL: ${{ secrets.VUE_APP_API_URL_PROD }}
        run: |
          aws s3 cp \
          --recursive \
          --region ap-northeast-2 \
          production s3://webview.yellosis.com

      - name: Invalidate CloudFront Cache
        run: aws cloudfront create-invalidation --distribution-id ${{secrets.PROD_DISTRIBUTION_ID}} --paths "/*"
