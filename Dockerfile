# Build stage
FROM node:16 AS build
WORKDIR /app
COPY package*.json ./
RUN npm install --legacy-peer-deps
COPY . .
ARG VUE_APP_API_URL
ENV VUE_APP_API_URL=$VUE_APP_API_URL
RUN npm run build

# Production stage
FROM nginx:alpine
COPY --from=build /app/production /usr/share/nginx/html
COPY --from=build /app/production/index.html /usr/share/nginx/html/index.html
COPY --from=build /app/production/favicon.ico /usr/share/nginx/html/favicon.ico
COPY --from=build /app/production/fonts /usr/share/nginx/html/fonts
COPY --from=build /app/production/img /usr/share/nginx/html/img
COPY --from=build /app/production/js /usr/share/nginx/html/js
COPY --from=build /app/production/media /usr/share/nginx/html/media
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
