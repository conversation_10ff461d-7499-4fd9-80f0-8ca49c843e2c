<!DOCTYPE html>
<html lang="ko">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>혈압 차트 - 개선판</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&family=Noto+Sans+KR:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Noto Sans KR', 'Inter', sans-serif;
        }
        /* Tailwind CSS 기본 스타일 확장 */
        .chart-container canvas {
            width: 100%;
            height: 100%;
        }
    </style>
</head>

<body class="bg-slate-50 flex items-center justify-center min-h-screen p-4">
    <div class="w-full max-w-4xl mx-auto bg-white rounded-2xl shadow-lg p-6">
        <!-- 상단 정보 표시 영역: 배경색과 테두리 관련 클래스 제거 -->
        <div id="info-header" class="text-center mb-5 p-4 rounded-xl transition-all duration-300 min-h-[84px]">
            <div id="pressureInfo" class="text-2xl md:text-3xl font-bold text-slate-700 mb-1">&nbsp;</div>
            <div id="dateInfo" class="text-sm md:text-base text-slate-500 h-6"></div>
        </div>
        
        <!-- 차트가 그려질 컨테이너 -->
        <div class="chart-container relative w-full h-[400px] md:h-[450px]">
            <canvas id="chartCanvas" class="cursor-crosshair"></canvas>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- 전역 변수 및 상태 관리 ---
            const canvas = document.getElementById('chartCanvas');
            const ctx = canvas.getContext('2d');

            // 정보 표시 DOM 요소
            const infoHeaderEl = document.getElementById('info-header');
            const pressureInfoEl = document.getElementById('pressureInfo');
            const dateInfoEl = document.getElementById('dateInfo');
            
            // 차트 데이터 (기존 데이터 유지)
            const bloodPressureData = {
                '06.19': { systolic: 140, diastolic: 90 },
                '06.20': { systolic: 130, diastolic: 88, range: [118, 142] },
                '06.21': { systolic: 150, diastolic: 70 },
                '06.22': { systolic: 120, diastolic: 80 },
                '06.23': { systolic: 130, diastolic: 85 },
                '06.24': { systolic: 120, diastolic: 80 },
                '06.25': { systolic: 140, diastolic: 95, range: [118, 148] }
            };

            let chartData = {
                labels: [],
                systolicData: [],
                diastolicData: [],
                ranges: []
            };

            // 차트 상호작용 상태
            let hoveredIndex = -1;
            let selectedIndex = -1;
            
            // 차트 레이아웃 설정 (Y축 범위 조정)
            const chartLayout = {
                padding: { top: 40, right: 40, bottom: 50, left: 50 },
                yMin: 60,
                yMax: 160
            };

            // --- 데이터 처리 함수 ---

            function getWeekDates() {
                const dates = [];
                const today = new Date('2024-06-25');
                for (let i = 6; i >= 0; i--) {
                    const d = new Date(today);
                    d.setDate(d.getDate() - i);
                    const month = String(d.getMonth() + 1).padStart(2, '0');
                    const day = String(d.getDate()).padStart(2, '0');
                    dates.push(`${month}.${day}`);
                }
                return dates;
            }

            function prepareChartData() {
                chartData.labels = getWeekDates();
                chartData.systolicData = chartData.labels.map(date => bloodPressureData[date]?.systolic ?? null);
                chartData.diastolicData = chartData.labels.map(date => bloodPressureData[date]?.diastolic ?? null);
                chartData.ranges = chartData.labels.map(date => bloodPressureData[date]?.range ?? null);
            }


            // --- 차트 그리기 함수 ---

            function resizeCanvas() {
                const dpr = window.devicePixelRatio || 1;
                const rect = canvas.getBoundingClientRect();
                
                canvas.width = rect.width * dpr;
                canvas.height = rect.height * dpr;
                
                ctx.scale(dpr, dpr);
                
                drawChart();
            }

            function drawChart() {
                const { width, height } = canvas.getBoundingClientRect();
                ctx.clearRect(0, 0, width, height);

                const chartArea = {
                    x: chartLayout.padding.left,
                    y: chartLayout.padding.top,
                    width: width - chartLayout.padding.left - chartLayout.padding.right,
                    height: height - chartLayout.padding.top - chartLayout.padding.bottom
                };

                drawGrid(chartArea);
                drawAxisLabels(chartArea);
                drawReferenceLines(chartArea);
                drawRangeBars(chartArea);
                
                drawDataPoints(chartArea, chartData.systolicData, '#ef4444', true);
                drawDataPoints(chartArea, chartData.diastolicData, '#f97316', false);
                
                if (hoveredIndex !== -1) {
                    drawHoverIndicator(chartArea, hoveredIndex);
                }
            }
            
            function drawGrid(area) {
                ctx.strokeStyle = '#e2e8f0';
                ctx.lineWidth = 1;
                ctx.font = '12px Inter';
                ctx.fillStyle = '#64748b';
                ctx.textAlign = 'right';

                const yRange = chartLayout.yMax - chartLayout.yMin;
                const gridInterval = 20;
                const gridCount = yRange / gridInterval;

                ctx.setLineDash([3, 3]);

                for (let i = 0; i <= gridCount; i++) {
                    const value = chartLayout.yMin + i * gridInterval;
                    const y = area.y + area.height - ((value - chartLayout.yMin) / yRange) * area.height;
                    
                    ctx.beginPath();
                    ctx.moveTo(area.x, y);
                    ctx.lineTo(area.x + area.width, y);
                    ctx.stroke();

                    ctx.fillText(value.toString(), area.x - 8, y + 4);
                }

                ctx.setLineDash([]);
            }

            function drawAxisLabels(area) {
                ctx.fillStyle = '#64748b';
                ctx.font = '12px Inter';
                ctx.textAlign = 'center';
                const xStep = area.width / (chartData.labels.length - 1);

                chartData.labels.forEach((label, index) => {
                    const x = area.x + index * xStep;
                    ctx.fillText(label, x, area.y + area.height + 25);
                });
            }

            function drawReferenceLines(area) {
                const values = [
                    { value: 120, color: '#ef4444' },
                    { value: 80, color: '#f97316' }
                ];

                ctx.lineWidth = 1;
                ctx.setLineDash([5, 5]);

                values.forEach(({ value, color }) => {
                    const y = area.y + area.height - ((value - chartLayout.yMin) / (chartLayout.yMax - chartLayout.yMin)) * area.height;
                    ctx.strokeStyle = color;
                    
                    ctx.beginPath();
                    ctx.moveTo(area.x, y);
                    ctx.lineTo(area.x + area.width, y);
                    ctx.stroke();
                });

                ctx.setLineDash([]);
            }

            function drawRoundedRect(x, y, width, height, radius) {
                ctx.beginPath();
                if (height < 2 * radius) radius = height / 2;
                if (width < 2 * radius) radius = width / 2;
                ctx.moveTo(x + radius, y);
                ctx.arcTo(x + width, y, x + width, y + height, radius);
                ctx.arcTo(x + width, y + height, x, y + height, radius);
                ctx.arcTo(x, y + height, x, y, radius);
                ctx.arcTo(x, y, x + width, y, radius);
                ctx.closePath();
                ctx.fill();
            }

            function drawRangeBars(area) {
                const xStep = area.width / (chartData.labels.length - 1);
                const barWidth = 16;
                const borderRadius = 8;
                ctx.fillStyle = '#C85C5C'; 

                chartData.ranges.forEach((range, index) => {
                    if (range) {
                        const x = area.x + index * xStep;
                        const yTop = area.y + area.height - ((range[1] - chartLayout.yMin) / (chartLayout.yMax - chartLayout.yMin)) * area.height;
                        const yBottom = area.y + area.height - ((range[0] - chartLayout.yMin) / (chartLayout.yMax - chartLayout.yMin)) * area.height;
                        const barHeight = yBottom - yTop;
                        drawRoundedRect(x - barWidth / 2, yTop, barWidth, barHeight, borderRadius);
                    }
                });
            }

            function drawDataPoints(area, data, color, isSystolic = false) {
                const xStep = area.width / (data.length - 1);
                
                data.forEach((value, index) => {
                    if (isSystolic && chartData.ranges[index] !== null) {
                        return;
                    }
                    if (value !== null) {
                        const x = area.x + index * xStep;
                        const y = area.y + area.height - ((value - chartLayout.yMin) / (chartLayout.yMax - chartLayout.yMin)) * area.height;
                        let radius = (hoveredIndex === index || selectedIndex === index) ? 8 : 5;
                        
                        ctx.beginPath();
                        ctx.fillStyle = color;
                        ctx.arc(x, y, radius, 0, Math.PI * 2);
                        ctx.fill();

                        ctx.beginPath();
                        ctx.strokeStyle = 'white';
                        ctx.lineWidth = 2;
                        ctx.arc(x, y, radius, 0, Math.PI * 2);
                        ctx.stroke();
                    }
                });
            }
            
            function drawHoverIndicator(area, index) {
                const xStep = area.width / (chartData.labels.length - 1);
                const x = area.x + index * xStep;
                ctx.strokeStyle = '#94a3b8';
                ctx.lineWidth = 1;
                ctx.setLineDash([3, 3]);
                ctx.beginPath();
                ctx.moveTo(x, area.y);
                ctx.lineTo(x, area.y + area.height);
                ctx.stroke();
                ctx.setLineDash([]);
            }


            // --- 상호작용 및 이벤트 처리 ---

            function findNearestPoint(mouseX, mouseY) {
                const { width, height } = canvas.getBoundingClientRect();
                 const chartArea = {
                    x: chartLayout.padding.left,
                    y: chartLayout.padding.top,
                    width: width - chartLayout.padding.left - chartLayout.padding.right,
                    height: height - chartLayout.padding.top - chartLayout.padding.bottom
                };
                const xStep = chartArea.width / (chartData.labels.length - 1);
                const rawIndex = (mouseX - chartArea.x) / xStep;
                let index = Math.round(rawIndex);
                
                if (index < 0 || index >= chartData.labels.length) {
                    return -1;
                }
                
                if (chartData.systolicData[index] === null && chartData.diastolicData[index] === null) {
                    return -1;
                }
                return index;
            }

            function handleMouseMove(event) {
                const rect = canvas.getBoundingClientRect();
                const mouseX = event.clientX - rect.left;
                const mouseY = event.clientY - rect.top;

                const index = findNearestPoint(mouseX, mouseY);
                
                if (index !== hoveredIndex) {
                    hoveredIndex = index;
                    drawChart();
                }
            }
            
            function handleMouseLeave() {
                hoveredIndex = -1;
                drawChart();
            }

            function handleChartClick(event) {
                 const rect = canvas.getBoundingClientRect();
                 const mouseX = event.clientX - rect.left;
                 const mouseY = event.clientY - rect.top;
                 
                 const index = findNearestPoint(mouseX, mouseY);
                 
                 if (index !== -1) {
                     selectedIndex = index;
                     updateInfoHeader(selectedIndex);
                 } else {
                     selectedIndex = -1;
                     resetInfoHeader();
                 }
                 drawChart();
            }
            
            /**
             * 상단 정보 헤더를 업데이트하고 배경을 표시합니다.
             */
            function updateInfoHeader(index) {
                if (index === -1) {
                    resetInfoHeader();
                    return;
                }

                const date = chartData.labels[index];
                const data = bloodPressureData[date];

                if (data) {
                    pressureInfoEl.textContent = `수축기 ${data.systolic} / 이완기 ${data.diastolic} mmHg`;
                    const currentYear = new Date().getFullYear();
                    dateInfoEl.textContent = `${currentYear}.${date}`;
                    // 배경과 테두리 클래스 추가
                    infoHeaderEl.classList.add('bg-slate-100', 'border', 'border-slate-200');
                } else {
                   resetInfoHeader();
                }
            }

            /**
             * 정보 헤더를 초기 상태로 리셋하고 배경을 숨깁니다.
             */
            function resetInfoHeader() {
                pressureInfoEl.innerHTML = '&nbsp;';
                dateInfoEl.textContent = '';
                // 배경과 테두리 클래스 제거
                infoHeaderEl.classList.remove('bg-slate-100', 'border', 'border-slate-200');
            }


            // --- 초기화 ---
            function init() {
                prepareChartData();
                resizeCanvas();
                
                window.addEventListener('resize', resizeCanvas);
                canvas.addEventListener('mousemove', handleMouseMove);
                canvas.addEventListener('mouseleave', handleMouseLeave);
                canvas.addEventListener('click', handleChartClick);
            }

            init();
        });
    </script>
</body>
</html>
