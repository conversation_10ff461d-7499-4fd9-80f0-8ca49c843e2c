<template>
  <div>
    <background>
      <div class="xs-mobile height-100">
        <div class="notice-modal__wrapper">
          <div class="notice-modal">
            <div class="notice-modal__title" :class="lang === 'ko' ? '' : 'en-title'">
              {{ $t("permission_notice") }}
            </div>
            <div class="notice-modal__content" v-html="$t('permission_notice2')"></div>
            <div class="notice-modal__items">
              <div v-show="isIos" class="items__content" v-for="(item, idx) in iosContent" :key="idx">
                <div class="item__img">
                  <img :src="item.img" />
                </div>
                <div class="item__txt">
                  <div class="item-txt__title">{{ item.title }}</div>
                  <div class="item-txt__subtitle">
                    {{ item.subtitle }}
                  </div>
                </div>
              </div>
              <div v-show="!isIos" class="items__content" v-for="(item, idx) in aosContent" :key="idx">
                <div class="item__img">
                  <img :src="item.img" />
                </div>
                <div class="item__txt">
                  <div class="item-txt__title">{{ item.title }}</div>
                  <div class="item-txt__subtitle">
                    {{ item.subtitle }}
                  </div>
                </div>
              </div>
            </div>
            <div class="notice-footer__txt">
              {{ $t("permission_description") }}
            </div>
          </div>
        </div>
        <LastButton :path="path" :color="color" :title="name" />
      </div>
    </background>
  </div>
</template>

<script>
import LastButton from "@/components/Common/LastButton.vue";

export default {
  components: {
    LastButton,
  },
  data() {
    return {
      path: "/intro",
      color: "#41D8E6",
      name: this.$i18n.t("next_btn"),
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
      iosContent: [
        {
          img: require("@/assets/images/perm_smartphone.png"),
          title: this.$i18n.t("perm_record"),
          subtitle: this.$i18n.t("perm_record_description"),
        },
        // {
        //   img: require("@/assets/images/healthkit_icon.png"),
        //   title: this.$i18n.t("perm_healthKit"),
        //   subtitle: this.$i18n.t("perm_healthKit_permission"),
        // },
        {
          img: require("@/assets/images/perm_camera.png"),
          title: this.$i18n.t("perm_camera"),
          subtitle: this.$i18n.t("perm_camera_description"),
        },
        {
          img: require("@/assets/images/perm_folder.png"),
          title: this.$i18n.t("perm_media"),
          subtitle: this.$i18n.t("perm_media_description"),
        },
        {
          img: require("@/assets/images/perm_bell.png"),
          title: this.$i18n.t("perm_noti"),
          subtitle: this.$i18n.t("perm_noti_description"),
        },
        {
          img: require("@/assets/images/perm_toilet.png"),
          title: this.$i18n.t("perm_ble"),
          subtitle: this.$i18n.t("perm_ble_description"),
        },
      ],
      aosContent: [
        {
          img: require("@/assets/images/perm_smartphone.png"),
          title: this.$i18n.t("perm_record"),
          subtitle: this.$i18n.t("perm_record_description"),
        },
        {
          img: require("@/assets/images/location.png"),
          title: this.$i18n.t("perm_location"),
          subtitle: this.$i18n.t("perm_location_description"),
        },
        {
          img: require("@/assets/images/perm_camera.png"),
          title: this.$i18n.t("perm_camera"),
          subtitle: this.$i18n.t("perm_camera_description"),
        },
        {
          img: require("@/assets/images/perm_folder.png"),
          title: this.$i18n.t("perm_media"),
          subtitle: this.$i18n.t("perm_media_description"),
        },
        {
          img: require("@/assets/images/perm_bell.png"),
          title: this.$i18n.t("perm_noti"),
          subtitle: this.$i18n.t("perm_noti_description"),
        },
        {
          img: require("@/assets/images/perm_toilet.png"),
          title: this.$i18n.t("perm_ble"),
          subtitle: this.$i18n.t("perm_ble_description"),
        },
      ],
      lang: "",
    };
  },
  methods: {
    hideModal() {
      this.$router.push({ path: "/intro" });
    },
  },
  mounted() {
    this.lang = this.$i18n.locale === "ko" ? "ko" : "en";
  },
};
</script>

<style lang="scss" scoped>
.notice-modal__wrapper {
  width: 100%;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: scroll;
  padding: 0 30px 5vh;
}

.notice-modal {
  display: flex;
  flex-direction: column;
  padding: 15vh 0 15vh;
  height: 100%;
}

.notice-modal__title {
  font-weight: 700;
  font-size: 26px;
  line-height: 26px;
  width: 100%;
  text-align: center;
  letter-spacing: -0.03em;
  color: #000000;
}

.notice-modal__content {
  font-size: 18px;
  letter-spacing: -0.03em;
  line-height: 20px;
  text-align: justify;
  margin: 2vh 0 4vh 0;
  color: #000000;
}

.items__content {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  justify-content: space-between;
}

.item__img {
  width: 15%;
  height: 40px;
  text-align: left;
  display: flex;
  align-items: center;
  img {
    width: 50px;
    height: 50px;
  }
}

.item__txt {
  text-align: left;
  width: 80%;
}

.item-txt__title {
  font-size: 16px;
  font-weight: 400;
  color: #000000;
}

.item-txt__subtitle {
  font-size: 14px;
  color: $yellosis-gray-3;
  font-weight: 400;
}

.notice-footer__txt {
  font-size: 14px;
  line-height: 19px;
  color: $yellosis-gray-3;
  text-align: justify;
  font-weight: 400;
}
</style>
