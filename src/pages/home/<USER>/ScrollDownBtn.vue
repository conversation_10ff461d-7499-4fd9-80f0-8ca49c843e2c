<template>
  <div v-if="isLoading" class="scroll-down-sign">
    <!-- <div class="scroll-down-sign__txt">{{ $t("write") }}</div> -->
    <div class="finger">
      <div class="scroll-down-sign__txt">{{ $t("write") }}</div>
      <!-- <img src="@/assets/images_assets/icons/point-up.png" /> -->
      <img src="@/assets/images_assets/icons/scroll-up.png" />
    </div>
    <!-- <img :src="scrollDownBtn" alt="scroll-down" class="scroll-down-sign__img" /> -->
  </div>
</template>

<script>
export default {
  props: {
    showCompleteModal: Boolean,
  },
  data() {
    return {
      // scrollDownBtn: require("@/assets/images_assets/icons/arrow-down-circle-ic.png"),
      isLoading: false,
    };
  },
  watch: {
    showCompleteModal: function(newVal, oldVal) {
      if (!newVal) {
        const activeVal = document.querySelector(".scroll-down-sign");
      }
    },
  },
  mounted() {
    this.showCompleteModal = this.$store.state.showCompleteModal;
    setTimeout(() => {
      this.isLoading = true;
    }, 1500);
  },
};
</script>

<style lang="scss" scoped>
.scroll-down-sign {
  position: fixed;
  bottom: 60px;
  width: 100%;
  left: 0;
  display: flex;
  justify-content: center;
  // opacity: 0;
  // transform: translateX(-50%);
}

.scroll-down-sign__txt {
  font-weight: bold;
  font-size: 15px;
  line-height: 20px;
  letter-spacing: -0.05em;
  color: #000000;
  padding-bottom: 5px;
}
.scroll-down-sign__img {
  position: relative;
  width: 35px;
}

.btn-active {
  /* opacity: 1; */
  // animation: swipe-up 1s ease-in-out infinite;
  opacity: 0;
  animation: bounce-in-top 0.7s infinite;
  animation-delay: 1.5s;
}

.finger {
  position: absolute;
  bottom: 0;
  opacity: 0;
  animation: swipe-up .6s ease-in-out infinite alternate;
  img {
    width: 30px;
    // transform: rotate(-30deg);
  }
}

// .finger::before {
//   content: "";
//   display: block;
//   position: absolute;
//   top: 25px;
//   left: 40%;
//   transform: translateX(-50%);
//   width: 15px;
//   height: 15px;
//   background-color: #fff;
//   box-shadow: 0 0 10px rgba(0, 0, 0, 0.8);
//   border-radius: 50%;
//   opacity: 0;
//   // animation-delay: 1.5s;
//   animation: disappear 1.2s infinite;
// }

@keyframes swipe-up {
  // 0% {
  //   transform: translateY(-20px);
  //   opacity: 1;
  // }
  // 100% {
  //   transform: translateY(-70px);
  //   opacity: 1;
  // }
  from {
    transform: translateY(-20px);
    opacity: 1;
  }
  to {
    transform: translateY(-60px);
    opacity: 1;
  }
}

@keyframes disappear {
  0% {
    opacity: 1;
  }
  // 60% {
  //   opacity: 0.4;
  // }
  // 80% {
  //   opacity: 0.2;
  // }
  100% {
    opacity: 0;
  }
}

@keyframes bounce-in-top {
  0% {
    transform: translateY(0);
    animation-timing-function: ease-in;
    opacity: 1;
  }
  50% {
    transform: translateY(-10px);
    animation-timing-function: ease-out;
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    animation-timing-function: ease-out;
    opacity: 1;
  }
}

.circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #000;
  cursor: pointer;
  animation: swipe-up 0.5s ease-in-out infinite alternate;
}
</style>
