<template>
  <div>
    <AlertSign :position="'result'" v-if="showAlert" />
    <div class="cym-main-score-box">
      <!-- 🔥 date==================== -->
      <div class="urineTest-table-row date">
        <div
          class="col-item date-col"
          v-for="(item, idx) in dateArr"
          :key="idx"
          :class="idx === activeDropletIdx && !nonTestResult ? 'date-active' : ''"
        >
          {{ item.date }}
        </div>
        <div class="col-item btn__wrapper">
          <img :src="prevIcon" alt="link" @click="prevHandler" />
          <img :src="nextIcon" alt="link" @click="nextHandler" />
        </div>
      </div>
      <div class="result-table__wrapper">
        <!-- 🔥 잠혈 =================== -->
        <TableRowItem
          type="blood"
          :nonTestResult="nonTestResult"
          :urineTestItems="blood"
          :urineTestState="recentBloodState"
          :urineTestCompleted="showCompleteModal"
        />
        <!-- 🔥 Ph ===================== -->
        <TableRowItem
          type="ph"
          :nonTestResult="nonTestResult"
          :urineTestItems="ph"
          :urineTestState="recentPhState"
          :urineTestCompleted="showCompleteModal"
        />
        <!-- 🔥 단백질 ================== -->
        <TableRowItem
          type="protein"
          :nonTestResult="nonTestResult"
          :urineTestItems="protein"
          :urineTestState="recentProteinState"
          :urineTestCompleted="showCompleteModal"
        />
        <!-- 🔥 포도당 ================== -->
        <TableRowItem
          type="glucose"
          :nonTestResult="nonTestResult"
          :urineTestItems="glucose"
          :urineTestState="recentGlucoseState"
          :urineTestCompleted="showCompleteModal"
        />
        <!-- 🔥 ketone ================= -->
        <TableRowItem
          type="ketone"
          :nonTestResult="nonTestResult"
          :ketoneMode="ketoneMode"
          :urineTestItems="ketone"
          :urineTestState="recentKetoneState"
          :urineTestCompleted="showCompleteModal"
        />
      </div>
    </div>
  </div>
</template>
<script>
import TableRowItem from "@/components/Home/UrineTestRowItem.vue";
import {
  bloodScoring,
  proteinAndGlucoseScoring,
  pHScoring,
  ketoneScoring,
} from "@/utils/ResultTableView.utils.js";
import AlertSign from "@/components/Common/AlertSign.vue";

import { mapGetters } from "vuex";
import dataProcessing from "@/assets/data/manufacturing/cym.js";
import { createFiveLengthArray } from "@/utils/ResultTableView.utils.js";

export default {
  props: {
    historyData: Array,
    totalCount: Number,
    ketoneMode: Boolean,
  },
  components: {
    TableRowItem,
    AlertSign,
  },
  data() {
    return {
      nonTestResult: true,
      blood: [],
      glucose: [],
      protein: [],
      ph: [],
      ketone: [],
      dateArr: [],
      page: 1,
      totalPage: 1,
      isPrevData: false,
      isNextData: false,
      showAlert: false,
    };
  },

  watch: {
    historyData(newVal) {
      // console.log(newVal);
      this.getUrineTestResults();
    },
    page(newVal) {
      // console.log(newVal);
      this.isNextData = newVal <= 1 ? false : true;
      this.isPrevData = this.totalPage > newVal ? true : false;
    },
  },
  computed: {
    prevIcon() {
      return this.isPrevData
        ? require("@/assets/images_assets/icons/prev_chevron.png")
        : require("@/assets/images_assets/icons/prev_btn.png");
    },
    nextIcon() {
      return this.isNextData
        ? require("@/assets/images_assets/icons/next_chevron.png")
        : require("@/assets/images_assets/icons/next_btn.png");
    },
    ...mapGetters(["activeDropletIdx", "showCompleteModal"]),
    setTestDateArray() {
      const datelist = this.blood.map((item) => {
        // console.log(item.createdAt);
        const YYMMDD = item.createdAt.split("T")[0].split("-");
        const MM = YYMMDD[1];
        const DD = YYMMDD[2];
        return `${MM}.${DD}`;
      });
      return datelist;
    },

    recentBloodState() {
      if (!this.blood[this.activeDropletIdx]) {
        return;
      }
      return bloodScoring(this.blood[this.activeDropletIdx].level);
    },
    recentProteinState() {
      if (!this.protein[this.activeDropletIdx]) {
        return;
      }
      return proteinAndGlucoseScoring(this.protein[this.activeDropletIdx].level);
    },
    recentGlucoseState() {
      if (!this.glucose[this.activeDropletIdx]) {
        return;
      }
      return proteinAndGlucoseScoring(this.glucose[this.activeDropletIdx].level);
    },
    recentPhState() {
      if (!this.ph[this.activeDropletIdx]) {
        return;
      }
      return pHScoring(this.ph[this.activeDropletIdx].level);
    },
    recentKetoneState() {
      if (!this.ketone[this.activeDropletIdx]) {
        return;
      }
      return ketoneScoring(this.ketone[this.activeDropletIdx].level, this.ketoneMode);
    },
  },
  methods: {
    setPrevData() {
      this.isPrevData = Math.floor(this.totalCount / 5) > 0 ? true : false;
    },
    prevHandler() {
      if (this.page > 0 && this.totalPage > this.page) {
        this.page += 1;
        this.$emit("pageHandler", this.page);
      }
    },
    nextHandler() {
      if (this.page > 1) {
        this.page -= 1;
        this.$emit("pageHandler", this.page);
      }
    },
    sessionStorageHandler() {
      sessionStorage.removeItem("savedBookmarkList");
      sessionStorage.removeItem("scrollbookmarkPosition");
      sessionStorage.removeItem("savedBookmarkDataPage");
    },
    getUrineTestResults() {
      const urineTestResult = dataProcessing.FETCH_CYM_DATA(this.historyData);
      // console.log("urineTestResult:", urineTestResult);
      // console.log(urineTestResult.blood);
      this.blood = urineTestResult.blood;
      this.glucose = urineTestResult.glucose;
      this.protein = urineTestResult.protein;
      this.ph = urineTestResult.ph;
      this.ketone = urineTestResult.ketone;
      this.dateArr = createFiveLengthArray("blood", this.blood);
      // console.log("urineTestResult arr:", this.dateArr);
      this.haveUrineTestReulst();
    },
    haveUrineTestReulst() {
      if (this.blood.length > 0) {
        this.nonTestResult = false;
      } else {
        this.nonTestResult = true;
      }
    },
    showAlertHandler() {
      this.showAlert = false;
    },
  },
  mounted() {
    // console.log("this.historyData:", this.historyData);
    // console.log("ketoneMode", this.ketoneMode);
    this.totalPage = Math.ceil(this.totalCount / 5);
    // console.log(this.totalPage);
    if (this.totalPage > 1) this.isPrevData = true;
    this.getUrineTestResults();
    setTimeout(() => {
      this.showAlertHandler();
    }, 2000);
  },
};
</script>

<style lang="scss" scoped>
.urineTest-table-row {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  align-items: center;
  width: 100%;
  max-width: 640px;
  &:first-child {
    padding-left: 13%;
  }
}

.col-item {
  font-family: GilroyMedium !important;
  flex: 1;
  text-align: center;
  padding-right: 4px;
  /* padding: 5px 10px 0 5px; */
  width: 100%;
  align-content: center;
  display: flex;
  justify-content: center;
  img {
    width: 12px;
  }
}

.btn__wrapper {
  width: 100%;
  display: flex;
  gap: 25px;
  padding: 0 !important;
}

.cym-main-score-box {
  padding: 25px 20px;
}

.skip-btn__wrapper {
  width: 80%;
  display: flex;
  justify-content: space-between;
}

.cym-main-score-title {
  font-size: 18px;
  font-weight: 700;
  text-align: left;
  color: #000;
  margin-bottom: 8px;
  img {
    margin-left: 3px;
    width: 8px;
  }
}

.date {
  font-size: 12px;
  color: #ededed;
}

.date-active {
  font-size: 15px;
  font-weight: 500;
  color: #646464;
  letter-spacing: -0.05em;
}

.result-table__wrapper {
  margin-top: 15px;
}
</style>
