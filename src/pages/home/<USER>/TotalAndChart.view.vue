<template>
  <section>
    <div class="cym-total-info__box">
      <router-link :to="{ path: '/home/<USER>' }">
        <div class="cym-emblem__wrapper">
          <CymEmblem />
          <div class="total-score__block">
            <span v-if="haveTotalScore" class="cym_score">{{ setTotalScore }}</span>
            <span v-else class="cym_score_none">{{ $t("try_test") }}</span>
          </div>
        </div>
      </router-link>

      <div class="cym-chart__wrapper">
        <router-link :to="{ path: '/home/<USER>' }">
          <div class="history-link__block">
            <span class="detail-txt">
              {{ username.length > 8 ? username.slice(0, 8) + "..." : username }} {{ $t("name") }}
            </span>
            <img src="@/assets/images_assets/icons/arrow-ic.png" alt="link" />
          </div>
        </router-link>
        <div class="cym-chart__block">
          <HomeLineChart :chartData="setGraphData" @setTotalScore="setTotalScore" />
        </div>
        <!-- <div class="user-name__block">
          <span>{{ username }}</span> <span>{{ $t("name") }}</span>
        </div> -->
      </div>
    </div>
  </section>
</template>

<script>
import CymEmblem from "@/assets/svg/cym_emblem.svg";
import HomeLineChart from "@/components/Home/HomeLineChart.vue";

export default {
  components: {
    CymEmblem,
    HomeLineChart,
  },
  props: {
    username: String,
    graphData: Array,
  },
  data() {
    return {
      totalScore: 0,
      childComponentloaded: false,
      chartData: [],
    };
  },

  computed: {
    haveTotalScore() {
      if (this.graphData.length === 0) {
        return false;
      }
      return true;
    },
    setGraphData() {
      const graphData = this.graphData.map((item) => item.value);
      return graphData;
    },
    setTotalScore() {
      const activeIdx =
        this.graphData.length < 5
          ? this.$store.state.activeDropletIdx - (5 - this.graphData.length)
          : this.$store.state.activeDropletIdx;
      // this.$store.state.activeDropletIdx - 1
      // console.log(this.$store.state.activeDropletIdx);
      return this.haveTotalScore ? this.graphData[activeIdx].value : 0;
    },
  },
  methods: {
    //   this.totalScore = this.graphData[dataPointIdx].value;
    initialSet() {
      const totalScore = this.graphData[this.graphData.length - 1].value;
      this.totalScore = totalScore;
      this.$store.commit("GET_TOTAL_SCORE", totalScore);
    },
  },
  mounted() {
    this.graphData.length !== 0 ? this.initialSet() : null;
  },
};
</script>

<style lang="scss" scoped>
.history-link__block {
  width: 100%;
  text-align: right;
  height: 25px;
  img {
    width: 7px;
  }
}
.detail-txt {
  width: 100%;
  line-height: 25px;
  font-size: 18px;
  font-weight: 500;
  padding-right: 6px;
}

.cym-total-info__box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // padding: 25px 1.875rem 35px 1.875rem;
  padding: 25px 30px 40px;
}

.cym-emblem__wrapper {
  position: relative;
}

.total-score__block {
  position: absolute;
  width: 100%;
  top: 48%;
  left: 55%;
  transform: translate(-50%, -50%);
}

.cym-chart__wrapper {
  position: relative;
  display: flex;
  width: 100%;
}

.cym-chart__block {
  // margin-top: 10px;
  height: 100%;
  width: 100%;
}
.cym_score {
  font-family: GilroyBold;
  // font-weight: 800;
  font-size: 40px;
  line-height: 40px;
  text-align: center;
  color: #000000;
}

.history-link__block {
  position: absolute;
  top: 0;
  z-index: 1;
}

.cym_score_none {
  font-family: Noto Sans KR;
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 44px;
  text-align: center;
  letter-spacing: -0.05em;
  color: #646464;
}

.user-name__block {
  font-family: Noto Sans KR;
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  text-align: right;
  letter-spacing: -0.05em;
  margin-right: 4px;
}
</style>
