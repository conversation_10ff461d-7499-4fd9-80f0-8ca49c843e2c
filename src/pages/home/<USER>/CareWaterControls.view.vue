<template>
  <div>
    <div>
      <router-link :to="{ path: '/home/<USER>/water' }">
        <div class="card__wrapper">
          <div class="left-container">
            <div class="img-container"><img src="@/assets/images/care/main_water.png" alt="water img" /></div>
            <div :class="isKo ? 'water-title' : 'water-title_en'">
              {{ $t("water_title") }}<span class="water-title_en"> Care</span>
            </div>
          </div>
          <div class="right-arrow"><img src="@/assets/images/right_chevron.png" /></div>
        </div>
      </router-link>
    </div>

    <div class="snackbar">
      <v-snackbar v-model="snackbar" timeout="2000">{{ succesContent }}</v-snackbar>
      <v-snackbar v-model="overTen" timeout="2000">{{ failContent }}</v-snackbar>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      page: "water",
      path: "home",
      succesContent: this.$i18n.t("save_success"),
      failContent: "연속 10회 입력 불가능",
      snackbar: false,
      overTen: false,
      isKo: false,
    };
  },

  mounted() {
    this.isKo = this.$i18n.locale === "ko";
  },
};
</script>

<style lang="scss" scoped>
.card__wrapper {
  width: 100%;
  display: flex;
  padding: 40px 25px 0 30px;
  justify-content: space-between;
  align-items: center;
}

.left-container {
  display: flex;
  align-items: center;
  color: #646464;
  gap: 20px;
  img {
    height: 70px;
    object-fit: contain;
  }
}

.img-container {
  width: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.right-arrow {
  img {
    height: 15px;
    object-fit: contain;
  }
}

.water-title {
  font-size: 18px;
  line-height: 22px;
  font-weight: 700;
}
.water-title_en {
  font-size: 21.5px;
  line-height: 22px;
  font-family: GilroyBold;
}
</style>
