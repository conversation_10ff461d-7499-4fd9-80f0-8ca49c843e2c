<template>
  <div>
    <router-link :to="{ path: '/home/<USER>/pee' }">
      <div class="card__wrapper">
        <div class="left-container">
          <div class="img-container">
            <img src="@/assets/images/care/main_pee.png" />
          </div>
          <div :class="isKo ? 'pee-title' : 'pee-title_en'">
            {{ $t("pee_title") }}<span class="pee-title_en"> Care</span>
          </div>
        </div>
        <div class="right-arrow">
          <img src="@/assets/images/right_chevron.png" />
        </div>
      </div>
    </router-link>

    <div class="snackbar">
      <v-snackbar v-model="snackbar" timeout="2000">{{
        succesContent
      }}</v-snackbar>
      <v-snackbar v-model="overOnce" timeout="2000" color="#EE0000">{{
        failContent
      }}</v-snackbar>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      succesContent: this.$i18n.t("save_success"),
      failContent: this.$i18n.t("pee_error"),
      snackbar: false,
      overOnce: false,
      isKo: false,
    };
  },
  mounted() {
    this.isKo = this.$i18n.locale === "ko";
  },
};
</script>

<style lang="scss" scoped>
.card__wrapper {
  width: 100%;
  display: flex;
  padding: 40px 25px 0 30px;
  justify-content: space-between;
  align-items: center;
}

.left-container {
  display: flex;
  align-items: center;
  color: #646464;
  gap: 20px;
  img {
    height: 70px;
    object-fit: contain;
  }
}

.img-container {
  width: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.right-arrow {
  img {
    height: 15px;
    object-fit: contain;
  }
}
.pee-title {
  font-size: 18px;
  line-height: 22px;
  font-weight: 700;
}

.pee-title_en {
  font-size: 21.5px;
  line-height: 22px;
  font-family: GilroyBold;
}
</style>
