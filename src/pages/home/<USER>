<template>
  <Background>
    <AlertModal
      v-if="showAlertModal"
      @nextHandler="surveyHandler"
      :btnText="goToSurvey"
      :error="error"
      @closeBtnHandler="closeBtnHandler"
    />
    <!-- Notice Modal -->
    <ModalTwoBtn v-if="isPopupVisible" @closeHandler="noticeModalHandler" />
    <!-- header section -->
    <Header />
    <!-- chart and total score view -->
    <TotalAndChartView
      v-if="loaded"
      :graphData="graphData"
      :username="username"
    />
    <!-- <div class="pt-21"></div> -->

    <!-- 5종 검사결과 -->
    <white-card>
      <UrineTestResultView
        @pageHandler="pageHandler"
        v-if="loaded"
        :ketoneMode="ketoneMode"
        :historyData="historyData"
        :totalCount="totalCount"
      />
    </white-card>
    <div class="pt-40"></div>

    <!-- 체중, 수분, 배뇨케어 컨트롤 버튼 -->
    <white-card class="pb-60" v-show="showCare">
      <CareItem
        v-for="(item, idx) in careList"
        :key="item.title"
        :title="item.title"
        :routeLink="item.routeLink"
        :isLast="idx === careList.length - 1"
      />
    </white-card>

    <!-- set functional components -->
    <Navigation :path="path" />
    <CompleteAlert
      v-if="showCompleteModal"
      :totalScore="totalScore"
      :username="username"
      @closeOverlayHandler="closeOverlayHandler"
    />
    <ScrollDownBtn
      v-if="showScrollDownSign && !showCompleteModal && showCare && loaded"
      :showCompleteModal="showCompleteModal"
    />
    <Loading v-if="loading" />
    <div class="snackbar">
      <v-snackbar v-model="snackbar" timeout="2000">{{
        successContent
      }}</v-snackbar>
      <!-- <v-snackbar v-model="overOnce" timeout="2000" color="#EE0000">{{ failContent }}</v-snackbar> -->
    </div>
  </Background>
</template>

<script>
import AlertModal from "@/components/Common/AlertModal.vue";
import ModalTwoBtn from "@/components/Common/ModalTwoBtn.vue";

// page section components
import TotalAndChartView from "./sections/TotalAndChart.view.vue";
import UrineTestResultView from "./sections/UrineTestResult.view.vue";
import CareItem from "@/components/Home/CareItem.vue";
// functional components
import Header from "@/components/Home/Header.vue";
import Navigation from "@/components/Common/Navigation.vue";
import WhiteCard from "@/components/Common/WhiteCard.vue";
import Background from "@/components/Common/Background.vue";
import CompleteAlert from "@/components/Home/CompleteAlert.vue";
import ScrollDownBtn from "./sections/ScrollDownBtn.vue";
import Loading from "@/components/Common/Loading.vue";

import API from "@/api/cym702/index";
// import CAREAPI from "@/api/care/index";
import dataProcessing from "@/assets/data/manufacturing/cym.js";
import { getSubjectId } from "@/components/Common/getSubjectId.js";

export default {
  components: {
    AlertModal,
    // ModalTwoBtn,
    TotalAndChartView,
    UrineTestResultView,
    Header,
    Navigation,
    WhiteCard,
    Background,
    CompleteAlert,
    ScrollDownBtn,
    Loading,
    ModalTwoBtn,
    CareItem,
  },

  data() {
    return {
      loaded: false,
      loading: false,
      ketoneMode: null,
      path: "/home",
      showScrollDownSign: true,
      totalScore: 0,
      graphData: [],
      historyData: [],
      username: "",
      weight: 0,
      water: 0,
      urine: 0,
      selectUser: 0,
      showCare: true,
      totalCount: 0,
      error: this.$i18n.t("need_survey_message"),
      goToSurvey: this.$i18n.t("go_to_survey"),
      showNoticeModal: false,
      // surveyStatus: "",
      content: this.$i18n.t("home_notice_content"),
      successContent: this.$i18n.t("success_content"),
      snackbar: false,
      careList: [
        {
          title: "weight",
          routeLink: "/home/<USER>/weight",
        },
        {
          title: "water",
          routeLink: "/home/<USER>/water",
        },
        {
          title: "pee",
          routeLink: "/home/<USER>/pee",
        },
        {
          title: "bloodPressure",
          routeLink: "/home/<USER>/bloodPressure",
        },
        {
          title: "step",
          routeLink: "/home/<USER>/step",
        },
        {
          title: "woman",
          routeLink: "/home/<USER>/woman",
        },
      ],
    };
  },
  computed: {
    showCompleteModal() {
      return this.$store.state.showCompleteModal;
    },
    showAlertModal() {
      return this.$store.state.surveyModalOpen;
    },
    surveyStatus() {
      return this.$store.state.surveyStatus;
    },
    isPortable() {
      return !this.$store.state.isBoat;
    },
    isPopupVisible() {
      return this.$store.state.homePopupVisible;
    },
  },
  watch: {
    showScrollDownSign() {
      document.addEventListener("scroll", () => {
        const currentScrollValue = document.documentElement.scrollTop;
        if (currentScrollValue > 0) {
          this.showScrollDownSign = false;
        }
      });
    },
    showCompleteModal(newVal) {
      newVal && this.isPortable ? this.getHistoryData(1) : null;
    },
  },
  methods: {
    noticeModalHandler(fromChild) {
      // console.log(fromChild);
      // fromChild ? (this.showNoticeModal = false) : null;
      if (fromChild) {
        this.$store.commit("SET_HOME_POPUP_STATE", false);
        return;
      }
      this.$store.commit("SET_HOME_POPUP_STATE", true);
    },
    todayModalHandler() {
      const today = new Date();
      const currentDate = today.toISOString().split("T")[0];
      const lastClosedDate = localStorage.lastClosedDate;

      if (currentDate === lastClosedDate) {
        this.$store.commit("SET_HOME_POPUP_STATE", false);
      }
    },
    isConfirmed() {
      // console.log("notice modal");
    },
    pageHandler(page) {
      // console.log(page);
      // if (this.totalCount / 5 > page) this.reloadCymData(page);
      this.reloadCymData(page);
    },
    closeBtnHandler(fromChild) {
      // console.log(fromChild);
      this.$store.commit("setSurveyModal", false);
    },
    surveyHandler(fromChild) {
      // console.log(fromChild);
      this.$router.push("/survey");
    },
    async reloadCymData(page) {
      const subjectId = getSubjectId();
      try {
        const { data, status, config } = await API.GetCymScoreData(
          subjectId,
          page,
          5
        );
        // console.log(data, config.url);
        const cym = data.result;
        const graphData = dataProcessing.FETCH_CYM_DATA(cym);
        this.$store.commit("GET_RECENT_CYMDATA", data.result);
        // console.log("graphData get:", graphData);
        this.historyData = data.result;
        this.graphData = graphData.cym;
        this.$store.commit("GET_ACTIVE_DROPLET_IDX", 4);
      } catch (error) {
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
        console.log(error);
      }
    },
    async getHistoryData(page) {
      const subjectId = getSubjectId();
      this.loaded = false;
      this.loading = true;
      try {
        // const { data, status, config } = await API.GetCymHome(subjectId, page);
        const { data, status, config } = await API.GetCymScoreData(
          subjectId,
          page,
          5
        );
        // console.log(res)
        // console.log(data, status, config.url);
        this.$store.commit("setSurveyStatus", data.accountStatus);
        this.username = data.nickname;
        localStorage.setItem("surveyStatus", data.accountStatus);
        localStorage.setItem("username", data.nickname);
        // store에 최근 5개 데이터 저장하고 history페이지에서 사용
        if (data) {
          // console.log("homeData get", data);
          // this.loading = false;
          if (localStorage.getItem("ketoneMode") === null) {
            localStorage.setItem("ketoneMode", data.ketoneMode);
            this.$router.go();
          }
          if (page === 1) {
            this.$store.commit("setLastAnalysisTime", data.result[0].createdAt);
            // console.log(data.result[0].createdAt);
          }
          this.totalCount = data.count;
          this.ketoneMode = data.ketoneMode;
          // console.log("data user name");
          // console.log(data.nickname);
          // this.$store.commit("setKetoneMode", data.ketoneMode);
          // this.$store.commit("getUsername", data.nickname);
          this.$store.commit("GET_RECENT_CYMDATA", data.result);

          if (data.result.length === 0) {
            // console.log("length 0");
            this.graphData = [];
            this.historyData = [];
          } else {
            this.$nextTick(() => {
              const cym = data.result;
              const graphData = dataProcessing.FETCH_CYM_DATA(cym);
              // console.log("graphData get:", graphData);
              this.graphData = graphData.cym;
              this.historyData = data.result;
            });
          }
          this.$nextTick(() => {
            this.getCareValueData();
          });
        } else {
          this.graphData = [];
          this.historyData = [];
          this.$nextTick(() => {
            this.getCareValueData();
          });
        }
      } catch (error) {
        this.loaded = true;
        this.loading = false;
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    formatDate() {
      // 호출하는 시점의 unix time 저장
      const unixTime = new Date().getTime();
      const curLocalTime = new Date(unixTime);
      const year = curLocalTime.getFullYear();
      const month = curLocalTime.getMonth();
      const day = curLocalTime.getDate();
      const hours = curLocalTime.getHours();
      const minutes = curLocalTime.getMinutes();
      const today = curLocalTime.toISOString();
      return [year, month, day, hours, minutes, today];
    },

    async getCareValueData() {
      // const data = this.formatDate();
      const subjectId = getSubjectId();
      // console.log(subjectId);
      let [y, m, d, hh, mm, today] = this.formatDate();
      const utcStart = new Date(y, m, d).toISOString();
      // console.log(utcStart);
      // console.log(y, m, d, hh, mm, today);
      try {
        const { data, config } = await API.GetHomeCareData(subjectId, utcStart);
        // console.log("care data:", data, config.url);
        if (data) {
          this.loading = false;
          this.loaded = true;

          if (data.water.length === 0) {
            this.water = 0;
          } else if (data.water.length === 1) {
            this.water = Number(data.water[0].value);
          } else {
            const waterData = data.water.map((item) => item.value);
            this.water = waterData.reduce((a, b) => a + b);
          }

          if (data.urine.length === 0) {
            this.urine = 0;
          } else {
            this.urine = data.urine.length;
          }

          if (data.weight === null) {
            this.weight = data.subject.initialWeight;
          } else {
            this.weight = Number(data.weight.value);
          }

          // this.weight = Number(data.weight.value);
          this.$store.commit("GET_CUR_WEIGHT", this.weight);
          this.loading = false;
          this.loaded = true;
        } else {
          this.water = 0;
          this.urine = 0;
          this.weight = 0;
          this.loading = false;
          this.loaded = true;
        }
      } catch (error) {
        this.loading = false;
        this.loaded = true;
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    scrollEvent(e) {
      if (window.scrollY > 0) {
        this.showScrollDownSign = false;
      }
    },
    reloadHome() {
      // this.getHistoryData(1);
      this.getCareValueData();
    },
    checkingSurveyStatus() {
      const status =
        this.surveyStatus === ""
          ? localStorage.getItem("surveyStatus")
          : this.surveyStatus;
      setTimeout(() => {
        if (status === "survey_ongoing" || status === "survey_ready") {
          this.$store.commit("setSurveyModal", true);
        }
      }, 1500);
    },

    closeOverlayHandler() {
      this.snackbar = true;
      // setTimeout(() => {
      //   this.snackbar = true;
      //   // this.showAlertHandler();
      // }, 4000);
    },

    samsungHealthFormatDate() {
      const USER_TIME_ZONE = Intl.DateTimeFormat().resolvedOptions().timeZone;
      return new Intl.DateTimeFormat("sv-SE", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        timeZone: USER_TIME_ZONE,
      })
        .format(new Date())
        .replace(/\./g, "-")
        .replace(/ /g, "")
        .slice(0, 10);
    },
  },
  created() {
    // this.$router.beforeEach((to, from, next) => {
    //   if (from.path === "/join") {
    //     this.$router.replace();
    //   }
    // });
    this.type = localStorage.getItem("type") ?? "human";
    this.getHistoryData(1);
    this.$store.commit("GET_ACTIVE_DROPLET_IDX", 4);
  },
  beforeMount() {
    this.loading = true;
  },
  mounted() {
    this.checkingSurveyStatus();
    window.scrollTo(0, 0);
    this.$store.commit("setBackBtnClicked", false);
    document.body.classList.remove("no-scroll");
    document.addEventListener("scroll", this.scrollEvent);
    this.todayModalHandler();

    const __TOKEN = localStorage.getItem("auth"); // 토큰
    const __NOW_SUBJECT_ID = getSubjectId(); // subject Id
    const HEALTH_DATA_DATE = this.samsungHealthFormatDate(); // 오늘 날짜

    /*global Webview*/
    /*eslint no-undef: "error"*/
    Webview.getSteps(__TOKEN, String(__NOW_SUBJECT_ID), HEALTH_DATA_DATE);
    /*eslint no-undef: "error"*/
    Webview.getBloodPressures(
      __TOKEN,
      String(__NOW_SUBJECT_ID),
      HEALTH_DATA_DATE
    );
  },
};
</script>

<style scoped></style>
