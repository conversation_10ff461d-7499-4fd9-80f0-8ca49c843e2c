<template>
  <div>
    <AlertModal
      v-if="showAlertModal"
      @nextHandler="surveyHandler"
      :btnText="goToSurvey"
      :error="error"
      @closeBtnHandler="closeBtnHandler"
    />
    <div class="bg-modal" v-show="showGuide">
      <div
        :class="isIos ? 'close-icon__wrapper' : 'close-icon__wrapper-android'"
        @click="cancelHandler"
      >
        <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
      </div>
      <div :class="isIos ? 'setting-icon__wrapper' : 'setting-icon__wrapper-android'">
        <img src="@/assets/images/guide_img/care_settings_ic.png" />
      </div>
      <div :class="isIos ? 'arrow-icon__wrapper' : 'arrow-icon__wrapper-android'">
        <img src="@/assets/images/guide_img/arrow-up.png" />
      </div>
      <div :class="isIos ? 'guide-contents__wrapper' : 'guide-contents__wrapper-android'">
        <div class="guide-title" :class="lang === 'ko' ? '' : 'en-title'">
          {{ $t("what_is_target_weight") }}
        </div>
        <div class="guide-contents" v-html="this.$i18n.t('target_weight_description')"></div>
        <div class="guide-desc__wrapper">
          <img :src="guideImg" />
        </div>
      </div>
    </div>

    <background>
      <Header
        :type="type"
        :avgScore="weight"
        :count="count"
        :noData="noData"
        :date="date"
        @openEditModalWindow="openEditModalWindow"
      />
      <div class="white-background pt-45">
        <template v-if="count === 'd' && loaded">
          <WeightController :weight="weight" @saveBtnHandler="saveBtnHandler" />
        </template>

        <template v-else>
          <LineChart
            v-if="loaded"
            :min="minWeight"
            :max="maxWeight"
            :target="targetWeight"
            :page="page"
            :count="count"
            :historyData="graphData"
            :noData="noData"
          />
          <!-- @getMonth="getMonth" -->
        </template>
        <ChartBtn :count="count" @changeCount="changeCount" />
      </div>

      <div
        v-if="loaded && count === 'd' && targetWeight !== 0"
        class="comment-txt__wrapper pt-21 pd-20"
      >
        <!-- {{ $t("until_now") }} <span class="weight-data">{{ weightDecreaseValue }}kg</span> {{ $t("decrease") }} -->
        <div v-if="weightDecreaseValue > 0">
          {{ $t("untile_target") }}
          <span class="weight-data">{{ weightDecreaseValue }}kg</span> {{ $t("need_decrese") }}
        </div>
        <div v-else-if="weightDecreaseValue === 0">
          <!-- {{ $t("untile_target") }}
          <span class="weight-data">{{ weightDecreaseValue }}kg</span> {{ $t("need_decrese") }} -->
        </div>
        <div v-else>
          {{ $t("untile_target") }}
          <span class="weight-data">{{ -weightDecreaseValue }}kg</span> {{ $t("need_over") }}
        </div>
      </div>
      <div v-else class="comment-txt__wrapper pt-21 pd-20"></div>
      <!-- <div class="comment-txt__wrapper pt-21 pd-20"></div> -->

      <!-- history list -->
      <CareHistoryTable
        v-if="!noData"
        :history="historyData"
        :noData="noData"
        :count="count"
        @reloadData="reloadData"
        type="weight"
      />
      <navigation :path="path"></navigation>
      <EditWaterValueModal
        :page="page"
        :targetValue="targetWeight"
        v-if="showEditModalWindow"
        @closeEditModalWindow="closeEditModalWindow"
        @saveBtnHandler="targetSaveBtnHandler"
      />
      <div class="snackbar">
        <v-snackbar v-model="saveSuccess" timeout="2000">{{ succesContent }}</v-snackbar>
        <v-snackbar v-model="saveFail" timeout="2000" color="#EE0000">{{ failContent }}</v-snackbar>
      </div>
    </background>
  </div>
</template>

<script>
import Background from "@/components/Common/Background.vue";

// care componets
import Header from "@/components/Care/Header.vue";
import ChartBtn from "@/components/Care/CareChartBtns.vue";
import CareHistoryTable from "@/components/Care/CareHistoryTable.vue";
import EditWaterValueModal from "@/components/Care/EditWaterValueModal.vue";
import AlertModal from "@/components/Common/AlertModal.vue";

// chart components
import LineChart from "@/components/Chart/LineChart.vue";

// dayCareController components
import WeightController from "@/components/DayCareControllers/WeightController.vue";

import API from "@/api/care/index.js";
import { updateSubjectInfo } from "@/api/user/index.js";
import DataProcessing from "@/assets/data/manufacturing/care.js";
import { getSubjectId } from "@/components/Common/getSubjectId.js";

export default {
  components: {
    Background,
    Header,
    ChartBtn,
    CareHistoryTable,
    WeightController,
    LineChart,
    EditWaterValueModal,
    AlertModal,
  },
  data() {
    return {
      path: "/home",
      historyData: [],
      graphData: [],
      count: "d",
      type: "weight",
      page: "weight",
      weight: 0,
      loaded: false,
      noData: false,
      saveSuccess: false,
      succesContent: this.$i18n.t("save_success"),
      saveFail: false,
      failContent: this.$i18n.t("save_fail"),
      date: "",
      weightDecreaseValue: 0,
      toTargetValue: 0,
      targetWeight: 0,
      maxWeight: 100,
      minWeight: 10,
      showEditModalWindow: false,
      isCalled: false,
      showGuide: false,
      lang: "ko",
      guideImg: "",
      error: this.$i18n.t("need_survey_message"),
      goToSurvey: this.$i18n.t("go_to_survey"),
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },

  computed: {
    backBtnClicked() {
      return this.$store.state.backBtnClicked;
    },
    showAlertModal() {
      return this.$store.state.surveyModalOpen;
    },
  },

  watch: {
    backBtnClicked(newVal) {
      newVal ? (this.showEditModalWindow = false) : null;
    },
  },

  methods: {
    reloadData() {
      this.getHistoryData(this.count);
    },
    changeCount(count) {
      // console.log(count);
      if (this.count !== count) {
        this.count = count;
        this.getHistoryData(count);
      } else {
        console.log("already loaded");
      }
    },
    openEditModalWindow() {
      this.$store.commit("setBackBtnClicked", false);
      this.showEditModalWindow = true;
    },
    closeEditModalWindow() {
      this.showEditModalWindow = false;
    },
    async targetSaveBtnHandler(inputValue) {
      // console.log(inputValue);
      try {
        const subjectId = getSubjectId();
        const changedTargetWeight = {
          targetWeight: Number(inputValue),
        };
        const res = await updateSubjectInfo(subjectId, changedTargetWeight);
        // console.log(res);
        if (res.status === 200) {
          // console.log("success");
          this.saveSuccess = true;
          this.getHistoryData(this.count);
        }
      } catch (error) {
        console.error(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        } else this.saveFail = true;
      }
    },
    async saveBtnHandler(type, value) {
      try {
        const weightValue = {
          weight: Number(value),
        };
        // console.log(weightValue);
        const subjectId = getSubjectId();
        const response = await API.FetchUpdateCareData(subjectId, type, weightValue);
        if (response.status === 201) {
          this.saveSuccess = true;
          this.getHistoryData(this.count);
        }
      } catch (error) {
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        } else {
          this.saveFail = true;
          console.log(error);
        }
      }
    },
    formatDate(count) {
      const [year, month, day] = this.curDate(count);
      switch (count) {
        case "d":
          return [year, month, day];
        case "w":
          return [year, month, day];
        case "m":
          return [year, month, 1];
        case "y":
          return [year, 0, 1];
      }
    },

    curDate(count) {
      const curLocalTime = new Date();
      const start = new Date();
      start.setDate(start.getDate() - start.getDay() + 1);
      const year = count === "w" ? start.getFullYear() : curLocalTime.getFullYear();
      const month = count === "w" ? start.getMonth() : curLocalTime.getMonth();
      const day = count === "w" ? start.getDate() : curLocalTime.getDate();
      return [year, month, day];
    },

    getDayDate(date) {
      const localTime = new Date(date);
      const year = localTime.getFullYear();
      const month = localTime.getMonth();
      const day = localTime.getDate();
      return `${String(year).slice(2)}.${month + 1}.${day}`;
    },

    getWeekPeriod() {
      const now = new Date();
      const start = new Date(now);
      start.setDate(now.getDate() - now.getDay() + 1);

      const end = new Date(start);
      end.setDate(start.getDate() + 6);
      // 해당 주의 월요일, 일요일 날짜 반환

      const monYear = start.getFullYear();
      const monMonth = start.getMonth() + 1;
      const monDay = start.getDate();
      const sunYear = end.getFullYear();
      const sunMonth = end.getMonth() + 1;
      const sunDay = end.getDate();
      return `${String(monYear).slice(2)}.${monMonth}.${monDay} ~ ${String(sunYear).slice(
        2
      )}.${sunMonth}.${sunDay}`;
    },

    getMonthPeriod() {
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth(), 1);
      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      // 해당 월의 시작일자와 말일 반환
      const year = start.getFullYear();
      const month = start.getMonth() + 1;
      const endDate = end.getDate();
      return `${String(year).slice(2)}.${month}.1 ~ ${String(year).slice(2)}.${month}.${endDate}`;
    },

    getYearPeriod() {
      const now = new Date();
      const yaer = now.getFullYear();
      return `${String(yaer).slice(2)}.01 ~ ${String(yaer).slice(2)}.12`;
    },

    getUtcOffset() {
      const now = new Date();
      const utcOffsetMinutes = now.getTimezoneOffset();
      const utcOffsetHours = -utcOffsetMinutes / 60;
      return utcOffsetHours;
    },

    async getHistoryData(count) {
      this.loaded = false;
      try {
        const [year, month, day] = this.formatDate(count);
        const utcStart = new Date(year, month, day, 0, 0, 0).toISOString();
        const [y, m, d] = this.curDate();
        const utcEnd = new Date(y, m, d + 1, 0, 0, 0).toISOString();
        const subjectId = getSubjectId();
        const utcOffset = this.getUtcOffset();
        const periodType = this.count === "y" ? `&periodType=year` : "";
        const { data, config } = await API.GetCareData(
          subjectId,
          this.type,
          utcStart,
          utcEnd,
          utcOffset,
          periodType
        );
        // console.log(count, data, config.url);
        this.makeData(data, count);
      } catch (error) {
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    makeData(data, count) {
      if (
        data?.records?.length === 0 ||
        data?.monthlyData?.length === 0 ||
        data?.dailyData?.length === 0
      ) {
        if (data.average === null) {
          switch (count) {
            case "d": {
              this.noData = false;
              const recentWeight = data.recentWeight;
              const initialWeight = data.subject.initialWeight;
              this.targetWeight = data.subject?.targetWeight;
              if (recentWeight === null || recentWeight === undefined) {
                this.weight = initialWeight;
                this.date = this.getDayDate(data.subject.createdAt);
              } else {
                this.weight = recentWeight.value;
                this.date = this.getDayDate(recentWeight.createdAt);
              }

              break;
            }
            case "w": {
              this.noData = true;
              this.date = this.getWeekPeriod();
              break;
            }
            case "m": {
              this.noData = true;
              this.date = this.getMonthPeriod();
              break;
            }
            case "y": {
              this.noData = true;
              this.date = this.getYearPeriod();
              break;
            }
          }
          this.historyData = [];
          this.loaded = true;
        }
        this.loaded = true;
      } else {
        switch (count) {
          case "d": {
            this.noData = false;
            this.weight = data.records[0].values[0].value;
            this.date = this.getDayDate(data.records[0].values[0].createdAt);
            const careData = DataProcessing.GET_WEIGHT_CARE_DATA(data, count);
            this.historyData = careData.historyData;
            break;
          }
          case "w":
            this.date = this.getWeekPeriod();
            break;
          case "m":
            this.date = this.getMonthPeriod();
            break;
          case "y":
            this.date = this.getYearPeriod();
            break;
        }
        const careData = DataProcessing.GET_WEIGHT_CARE_DATA(data, count);
        this.historyData = careData.historyData;
        this.graphData = careData.graphData;
        this.weight = data.average;
        this.targetWeight = data.subject?.targetWeight;
        this.toTargetValue = (data.subject?.targetWeight - this.weight).toFixed(1);
        this.maxWeight =
          count === "d"
            ? this.weight
            : careData.graphData.reduce((max, cur) => Math.max(max, cur.value), 0);
        this.minWeight =
          count === "d"
            ? this.targetWeight
            : careData.graphData.reduce((min, cur) => Math.min(min, cur.value), 100);

        this.noData = false;
        this.loaded = true;
      }
      // console.log(this.weight, this.targetWeight);
      this.weightDecreaseValue = Math.floor(this.weight - this.targetWeight);
      // console.log(this.weightDecreaseValue > 0);
    },
    cancelHandler() {
      this.showGuide = false;
      localStorage.setItem("weightGuide", false);
    },

    closeBtnHandler(fromChild) {
      // console.log(fromChild);
      this.$store.commit("setSurveyModal", false);
    },
    surveyHandler(fromChild) {
      // console.log(fromChild);
      this.$router.push("/survey");
    },
  },

  mounted() {
    // console.log(this.$i18n.locale);
    this.lang = this.$i18n.locale === "ko" ? "ko" : "en";
    this.guideImg = require(`@/assets/images/guide_img/target_weight_guide_${this.lang}.png`);
    this.getHistoryData(this.count);
    this.showGuide =
      !!JSON.parse(localStorage.getItem("weightGuide")) ||
      JSON.parse(localStorage.getItem("weightGuide")) === null
        ? true
        : false;
  },
};
</script>

<style lang="scss" scoped>
.white-background {
  background-color: #fff;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
}

.bg-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 450px;
  height: 100%;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.8);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.close-icon__wrapper {
  text-align: left;
  position: absolute;
  top: 65px;
  left: 30px;
  img {
    width: 26px;
  }
}
.close-icon__wrapper-android {
  text-align: left;
  position: absolute;
  top: 35px;
  left: 30px;
  img {
    width: 26px;
  }
}
.setting-icon__wrapper {
  position: absolute;
  top: 151px;
  right: 21px;
  img {
    width: 36px;
  }
}
.setting-icon__wrapper-android {
  position: absolute;
  top: 115px;
  right: 21px;
  img {
    width: 36px;
  }
}

.arrow-icon__wrapper {
  position: absolute;
  top: 220px;
  right: 30px;
  img {
    width: 18px;
  }
}
.arrow-icon__wrapper-android {
  position: absolute;
  top: 185px;
  right: 30px;
  img {
    width: 18px;
  }
}

.guide-contents__wrapper {
  display: flex;
  flex-direction: column;
  position: absolute;
  bottom: 200px;
  top: 445px;
}
.guide-contents__wrapper-android {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 350px;
}

.guide-title {
  color: #41d8e6;
  font-size: 20px;
  font-weight: 700;
}

.guide-contents {
  color: #ffffff;
  font-size: 16px;
  margin: 30px 50px;
}

.guide-desc__wrapper {
  // position: absolute;
  width: 100%;
  // left: 0;
  padding: 0 20px;
  img {
    width: 100%;
  }
}

.comment-txt__wrapper {
  font-weight: 500;
  font-size: 16px;
  line-height: 20px;
  letter-spacing: -0.03em;
  color: #646464;
  text-align: left;
  padding: 20px 30px;
}

.weight-data {
  font-weight: 700;
  color: #41d8e6;
}

.noData-txt__wrapper {
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.03em;
  color: #646464;
  text-align: left;
  text-indent: 30px;
}
</style>
