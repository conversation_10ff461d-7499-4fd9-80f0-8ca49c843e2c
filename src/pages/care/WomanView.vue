<template>
  <div>
    <background>
      <Woman />
    </background>
  </div>
</template>

<script>
import Background from "@/components/Common/Background.vue";
import Woman from "@/components/Care/Woman.vue";
export default {
  components: { Background, Woman },
};
</script>

<style lang="scss" scoped>
.white-background {
  background-color: #fff;
  height: 100%;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
  // padding-bottom: 10vh;
}
</style>
