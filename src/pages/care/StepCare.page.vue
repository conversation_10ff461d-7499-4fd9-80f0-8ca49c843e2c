<template>
  <background>
    <!-- Header Component -->
    <Header
      :type="careType"
      :avgScore="period === 'd' ? stepGoal : stepAvg"
      :count="period"
      :date="date"
      @openEditModalWindow="openEditModalWindow"
    />
    <div class="background">
      <!-- period 값이 'd' 일때  -->
      <template v-if="period === 'd'">
        <StepRadialChart
          v-if="period === 'd'"
          :currentStep="currentStep"
          :stepGoal="stepGoal"
          :stepDistance="stepDistance"
          :stepCalories="stepCalories"
        />
      </template>
      <!-- 위 조건이 성립이 안될때 -->
      <template v-else>
        <div class="white-background pt-30">
          <LineChart
            v-if="loaded && period !== 'd'"
            :page="careType"
            :count="period"
            :min="MIN_STEP"
            :max="MAX_STEP"
            :target="stepGoal"
            :historyData="stepGraphHistory"
          />
        </div>
      </template>
      <!-- 요일 선택  -->
      <CareChartBtns :count="period" @changeCount="changeCount" />
    </div>
    <div class="goal-wrapper">
      <p class="archived-status-step" v-html="formattedArchivedStep" />
    </div>
    <!-- 걸음 기록 리스트 -->
    <CareHistoryTable
      type="step"
      :history="stepHistory"
      :count="period"
      :total="currentStep"
    />
    <!-- 목표 걸음 수정 모달 -->
    <EditStepGoalModal
      v-if="showEditModal"
      :title="setEditTitle"
      :stepGoal="stepGoal"
      @handleCloseEditModalWindow="handleCloseEditModalWindow"
    />
    <!-- 스낵바 components -->
    <div class="snackbar">
      <v-snackbar
        v-model="showSnackbar"
        timeout="2000"
        :color="saveSuccess ? '' : '#EE0000'"
      >
        {{ saveSuccess ? successContent : failContent }}
      </v-snackbar>
    </div>
  </background>
</template>

<script>
// api & util function
import { step } from "@/api/care";
import { fetchGetSubjectInfo } from "@/api/user";
import getUtcOffset from "@/utils/getUtcOffset";
import { getPeriodRange } from "@/utils";

// ui components
import Header from "@/components/Care/Header";
import CareChartBtns from "@/components/Care/CareChartBtns.vue";
import CareHistoryTable from "@/components/Care/CareHistoryTable.vue";
import StepRadialChart from "@/components/Care/StepRadialChart.ui";
import EditStepGoalModal from "@/components/Care/EditStepGoalModal.ui.vue";

// chart components
import LineChart from "@/components/Chart/LineChart.vue";

// libs
import _ from "lodash";

export default {
  components: {
    Header,
    StepRadialChart,
    CareChartBtns,
    CareHistoryTable,
    EditStepGoalModal,
    LineChart,
  },

  data() {
    return {
      careType: "step",
      careText: "",
      period: "d",
      date: "",
      startDate: "",
      endDate: "",
      currentStep: 0,
      stepCalories: 0,
      stepDistance: 0,
      stepGoal: 0,
      stepHistory: [],
      stepGraphHistory: [],
      isKo: false,
      showEditModal: false,
      showSnackbar: false,
      saveSuccess: false,
      successContent: this.$i18n.t("save_success"),
      failContent: this.$i18n.t("save_fail"),
      MIN_STEP: 0,
      MAX_STEP: 50000,
      stepAvg: 0,
    };
  },

  computed: {
    setEditTitle() {
      return this.$i18n.t("setting_target_step");
    },
    getArchivedStep() {
      return (Number(this.stepGoal) - Number(this.currentStep)).toLocaleString(
        "ko-KR"
      );
    },
    formattedArchivedStep() {
      if (this.period === "d") {
        if (this.isArchivedStep()) return "";

        return `${this.$t("until_step")} <span class="step-number">${
          this.getArchivedStep
        }${this.$t("step")}</span> ${this.$t("remain_step")}`;
      }
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      const prevYear = currentMonth === 1 ? currentYear - 2 : currentYear - 1;
      const prevMonth = currentMonth === 1 ? 12 : currentMonth - 1;

      function isLeapYear(year) {
        return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
      }

      function getTotalDaysBetweenYears(
        startYear,
        startMonth,
        endYear,
        endMonth
      ) {
        let totalDays = 0;

        for (let year = startYear; year <= endYear; year++) {
          if (year === startYear) {
            for (let month = startMonth; month <= 12; month++) {
              totalDays += new Date(year, month, 0).getDate();
            }
          } else if (year === endYear) {
            for (let month = 1; month <= endMonth; month++) {
              totalDays += new Date(year, month, 0).getDate();
            }
          } else {
            totalDays += isLeapYear(year) ? 366 : 365;
          }
        }

        return totalDays;
      }

      const totalDays = {
        w: 7,
        m: new Date(currentYear, currentMonth, 0).getDate(),
        y: getTotalDaysBetweenYears(
          prevYear,
          prevMonth,
          currentYear,
          currentMonth
        ),
      };

      // return totalDays[this.period];
      return "";
    },
  },

  methods: {
    handleCloseEditModalWindow() {
      this.showEditModal = false;
      this.fetchStepGoal();
    },
    openEditModalWindow() {
      this.$store.commit("setBackBtnClicked", false);
      this.showEditModal = true;
    },
    isArchivedStep() {
      if (this.currentStep <= 0 || this.stepGoal <= 0) {
        return false;
      }

      return this.currentStep > this.stepGoal;
    },
    changeCount(count) {
      this.loaded = false;
      this.period = count;
      this.fetchStepHistory();
    },

    getSubjectId() {
      const subjectId = localStorage.getItem("subjectId");

      return subjectId;
    },

    getCurrentDate(period) {
      const currentLocalTime = new Date();
      const start = new Date();
      start.setDate(start.getDate() - start.getDay() + 1);

      let year = currentLocalTime.getFullYear();
      let month = currentLocalTime.getMonth();
      let day = currentLocalTime.getDate();

      if (period === "w") {
        year = start.getFullYear();
        month = start.getMonth();
        day = start.getDate();
      }

      return [year, month, day];
    },

    formatDate(period) {
      let [year, month, day] = this.getCurrentDate(period);

      if (period === "d" || period === "w") {
        return [year, month, day];
      }

      if (period === "m") {
        const lastMonth = new Date(year, month - 1, day);
        lastMonth.setMonth(lastMonth.getMonth() - 1);

        return [
          lastMonth.getFullYear(),
          lastMonth.getMonth() + 1,
          lastMonth.getDate(),
        ];
      }

      if (period === "y") {
        const lastYear = new Date(year, month - 1, day);
        lastYear.setFullYear(lastYear.getFullYear() - 1);

        return [
          lastYear.getFullYear(),
          lastYear.getMonth(),
          lastYear.getDate(),
        ];
      }
    },

    formatToString(date) {
      const [year, month, day] = date;

      return `${String(year).slice(2)}.${String(month + 1).padStart(
        2,
        "0"
      )}.${String(day).padStart(2, "0")}`;
    },

    async fetchStepHistory() {
      const subjectId = this.getSubjectId();
      const USER_TIME_ZONE = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const startDate = this.startDate;
      const endDate = this.endDate;
      const utcOffset = getUtcOffset();
      const { getStep } = step;

      this.loaded = false;
      this.stepAvg = 0; // 평균값 초기화

      try {
        this.loaded = true;
        await getStep(
          subjectId,
          startDate,
          endDate,
          utcOffset,
          this.period === "y"
        ).then(({ data }) => {
          switch (this.period) {
            case "d": {
              const stepData = data[0];
              if (_.isEmpty(stepData)) {
                break;
              }
              this.currentStep = stepData?.totalStepCount;
              this.stepCalories = stepData?.totalCalories;
              this.stepDistance = stepData?.totalDistance;
              this.stepHistory = this.formatHistoryData(stepData.steps);
              this.stepAvg = 0; // 일간은 평균 0
              break;
            }
            case "w": {
              const steps = data;
              const STEPS_LEN = steps.length;

              if (STEPS_LEN > 0) {
                const total = steps.reduce(
                  (sum, step) => sum + (step.totalStepCount || 0),
                  0
                );
                this.stepAvg = Math.round(total / STEPS_LEN);
              } else {
                this.stepAvg = 0;
              }

              this.stepGraphHistory = this.formatGraphHistoryData(steps);
              break;
            }
            case "m": {
              const steps = data;
              const STEPS_LEN = steps.length;
              if (STEPS_LEN > 0) {
                const total = steps.reduce(
                  (sum, step) => sum + (step.totalStepCount || 0),
                  0
                );
                this.stepAvg = Math.round(total / STEPS_LEN);
              } else {
                this.stepAvg = 0;
              }
              this.stepGraphHistory = this.formatGraphHistoryData(steps);
              break;
            }
            case "y": {
              const steps = data;
              const stepCounts = steps
                .map((step) =>
                  step.steps && step.steps[0] ? step.steps[0].totalStepCount : 0
                )
                .filter((v) => v > 0);
              const STEPS_LEN = stepCounts.length;
              if (STEPS_LEN > 0) {
                const total = stepCounts.reduce((sum, v) => sum + v, 0);
                this.stepAvg = Math.round(total / STEPS_LEN);
              } else {
                this.stepAvg = 0;
              }

              this.stepGraphHistory = this.formatGraphHistoryData(steps);
              break;
            }
          }
        });
      } catch (err) {
        console.error(err);
      }
    },

    formatGraphHistoryData(steps) {
      if (this.period === "y") {
        return steps.map((element) => {
          const stepObj = element.steps && element.steps[0];
          return {
            createdAt: element.date.replace("-", "."),
            value: stepObj ? stepObj.totalStepCount : 0,
          };
        });
      } else {
        // 기존 월/주/일 데이터
        return steps.map((element) => {
          return {
            createdAt: element.date.replace(/-/g, "."),
            value: element.totalStepCount,
          };
        });
      }
    },

    // params: { count: number, speed: number, calories: number, distance: number, startTime: number, endTime: number, time_offset: number }
    formatHistoryData(steps) {
      function formatDate(d) {
        const date = new Date(d);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");

        return `${year}.${month}.${day}`;
      }

      function formatTime(d, isShowMeridiem = false) {
        const date = new Date(d);
        let hours = date.getHours();
        const minutes = date.getMinutes();
        const timeMeridiem = hours >= 12 ? "PM" : "AM";

        hours = hours % 12 || 12; // 00시는 12로 변경

        return `${String(hours)}:${String(minutes).padStart(2, "0")}${
          isShowMeridiem ? ` ${timeMeridiem}` : ""
        }`;
      }

      function roundTo30Min(d) {
        const date = new Date(d);
        const minutes = date.getMinutes();
        const roundedMinutes = minutes < 30 ? 0 : 30; // 0~29분 → 0, 30~59분 → 30

        date.setMinutes(roundedMinutes, 0, 0); // 초와 밀리초는 0으로 초기화

        return date;
      }

      const groupedSteps = {};

      switch (this.period) {
        case "d": {
          steps.forEach((step) => {
            const startDate = roundTo30Min(step.start_time);
            const endDate = new Date(startDate);
            endDate.setMinutes(startDate.getMinutes() + 30);

            const periodKey = `${formatDate(startDate)} ${formatTime(
              startDate
            )}~${formatTime(endDate, true)}`;

            if (!groupedSteps[periodKey]) {
              groupedSteps[periodKey] = 0;
            }
            groupedSteps[periodKey] += step.count;
          });

          break;
        }
        case "m": {
          console.log(steps);
          break;
        }
      }

      return Object.entries(groupedSteps).map(([createdAt, value], idx) => ({
        createdAt,
        id: idx,
        value,
      }));
    },

    async fetchStepGoal() {
      try {
        const subjectId = this.getSubjectId();

        await fetchGetSubjectInfo(subjectId)
          .then(({ data }) => {
            const { subject } = data;

            this.stepGoal = subject.targetStep;
          })
          .catch((err) => {
            console.error(err);
          });
      } catch (err) {
        console.error(err);
      }
    },
  },

  watch: {
    period(newPeriod) {
      // period값 달라질 때마다 새 startDate, endDate 할당
      const { startDate, endDate } = getPeriodRange(newPeriod);

      this.startDate = startDate;
      this.endDate = endDate;

      this.fetchStepHistory();
    },
  },

  mounted() {
    this.date = this.formatToString(this.getCurrentDate("d"));
    this.isKo = this.$i18n.locale.includes("ko");

    const { startDate, endDate } = getPeriodRange(this.period);
    this.startDate = startDate;
    this.endDate = endDate;

    this.fetchStepHistory();
    this.fetchStepGoal();
  },
};
</script>

<style lang="scss" scoped>
.background {
  width: 100vw;
  max-width: 450px;
  background: #ffffff;
}

.goal-wrapper {
  width: 100vw;
  max-width: 450px;
  margin-top: 25px;
  margin-bottom: 25px;
}

.archived-status-step {
  padding: 0 30px;
  color: #646464;
  font-size: 14px;
  font-weight: 500;
  line-height: 16.8px;
  letter-spacing: -0.03em;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.white-background {
  width: 100%;
  max-height: 264px;
  margin-bottom: 30px;
}

::v-deep(.step-number) {
  color: #41d8e6;
  font-weight: 700;
}
</style>
