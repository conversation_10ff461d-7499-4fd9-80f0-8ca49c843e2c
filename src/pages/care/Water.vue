<template>
  <div>
    <AlertModal
      v-if="showAlertModal"
      @nextHandler="surveyHandler"
      :btnText="goToSurvey"
      :error="error"
      @closeBtnHandler="closeBtnHandler"
    />
    <div class="bg-modal" v-show="showGuide">
      <div
        :class="isIos ? 'close-icon__wrapper' : 'close-icon__wrapper-android'"
        @click="cancelHandler"
      >
        <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
      </div>
      <div
        :class="
          isIos
            ? 'water-guide-img__wrapper'
            : 'water-guide-img__wrapper-android'
        "
      >
        <img :src="guideImg" />
      </div>
      <div class="guide-contents__wrapper">
        <div class="guide-title" :class="lang === 'ko' ? '' : 'en-title'">
          {{ $t("what_is_target_water") }}
        </div>
        <div
          class="guide-contents"
          v-html="this.$i18n.t('target_water_desc')"
        ></div>
      </div>
      <div class="setting-btn__wrapper">
        <div class="setting-item__btn" @click="goToHealthInfo">
          {{ $t("health_info_settings") }}
        </div>
      </div>
    </div>

    <background>
      <Header
        type="water"
        :avgScore="count === 'd' ? targetWater / 1000 : water"
        :count="count"
        :date="date"
        @openEditModalWindow="openEditModalWindow"
      />
      <div class="white-background pt-45">
        <template v-if="count === 'd'">
          <WaterController
            v-if="loaded"
            :type="page"
            :volume="volume"
            :targetWater="targetWater"
            @addWaterHistoryList="addWaterHistoryList"
            @saveBtnHandler="saveBtnHandler"
          />
        </template>

        <template v-else-if="count === 'w'">
          <ScatterChart
            v-if="loaded"
            :page="page"
            :count="count"
            :scatterData="graphData"
            :weekDayData="weekDayData"
          />
        </template>

        <template v-else>
          <LineChart
            v-if="loaded"
            type="water"
            :page="page"
            :count="count"
            :historyData="graphData"
            :targetWater="targetWater"
            @showWeeksTrendGraph="showWeeksTrendGraph"
            @getMonth="getMonth"
            @getDate="getDate"
          />

          <WeekTrendModal
            :page="page"
            v-if="count === 'm' && showWeekTrendModal"
            :specificDate="specificDate"
            @closeWeekTrendModal="closeWeekTrendModal"
          />
        </template>
        <ChartBtn :count="count" @changeCount="changeCount" />
      </div>

      <div
        class="comment-txt__wrapper pt-21 pd-20"
        v-if="historyData.length === 0 && count === 'd'"
      >
        {{ $t("nodata_water") }}
      </div>
      <!-- <div class="comment-txt__wrapper pt-21 pd-20" v-else-if="count === 'd'">
        {{ targetWater / 1000 }}L {{ $t("until") }} <span>{{ remaining }}ml</span> {{ $t("remain_water") }}
      </div> -->
      <div class="comment-txt__wrapper pt-21 pd-20" v-else></div>

      <!-- history list -->
      <CareHistoryTable
        v-if="!noData"
        :history="historyData"
        :count="count"
        @reloadData="reloadData"
        :type="page"
      />
      <navigation :path="path"></navigation>
      <EditWaterValueModal
        :page="page"
        :targetValue="targetWater"
        v-if="showEditModalWindow"
        @closeEditModalWindow="closeEditModalWindow"
        @saveBtnHandler="targetSaveBtnHandler"
      />
      <div class="snackbar">
        <v-snackbar v-model="saveSuccess" timeout="2000">{{
          succesContent
        }}</v-snackbar>
        <v-snackbar v-model="saveFail" timeout="2000" color="#EE0000">{{
          failContent
        }}</v-snackbar>
      </div>
    </background>
  </div>
</template>

<script>
import Background from "@/components/Common/Background.vue";
import Header from "@/components/Care/Header.vue";
import AlertModal from "@/components/Common/AlertModal.vue";

import WaterController from "@/components/DayCareControllers/WaterController.vue";
import LineChart from "@/components/Chart/LineChart.vue";
import ScatterChart from "@/components/Chart/ScatterChart.vue";
import WeekTrendModal from "@/components/Care/WeekTrendModal.vue";
import ChartBtn from "@/components/Care/CareChartBtns.vue";
import CareHistoryTable from "@/components/Care/CareHistoryTable.vue";
import EditWaterValueModal from "@/components/Care/EditWaterValueModal.vue";

import API from "@/api/care/index.js";
import { updateSubjectInfo } from "@/api/user/index.js";
import DataProcessing from "@/assets/data/manufacturing/care.js";
import { getSubjectId } from "@/components/Common/getSubjectId.js";

export default {
  components: {
    Background,
    Header,
    ChartBtn,
    CareHistoryTable,
    WaterController,
    WeekTrendModal,
    ScatterChart,
    LineChart,
    EditWaterValueModal,
    AlertModal,
  },
  data() {
    return {
      path: "/home",
      historyData: [],
      graphData: [],
      scatterData: [],
      weekGraphModal: [],
      monthData: {},
      count: "d",
      page: "water",
      water: 0,
      volume: 0,
      targetWater: 0,
      remaining: 0,
      showWeekTrendModal: false,
      loaded: false,
      noData: true,
      showEditModalWindow: false,
      saveSuccess: false,
      succesContent: this.$i18n.t("save_success"),
      saveFail: false,
      failContent: this.$i18n.t("save_fail"),
      date: "",
      dateRange: "",
      specificDate: "",
      weekDayData: [],
      showGuide: true,
      subjectId: Number(localStorage.getItem("subjectId")),
      lang: "",
      guideImg: "",
      error: this.$i18n.t("need_survey_message"),
      goToSurvey: this.$i18n.t("go_to_survey"),
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },

  computed: {
    backBtnClicked() {
      return this.$store.state.backBtnClicked;
    },
    showAlertModal() {
      return this.$store.state.surveyModalOpen;
    },
  },

  watch: {
    backBtnClicked(newVal) {
      newVal ? (this.showEditModalWindow = false) : null;
    },
  },

  methods: {
    reloadData() {
      this.getHistoryData(this.count);
    },

    async targetSaveBtnHandler(inputValue) {
      // console.log(inputValue);
      try {
        const subjectId = getSubjectId();
        const changedTargetWeight = {
          targetWater: Number(inputValue),
        };
        const res = await updateSubjectInfo(subjectId, changedTargetWeight);
        if (res.status === 200) {
          // console.log("success");
          this.saveSuccess = true;
          this.getHistoryData(this.count);
        }
      } catch (error) {
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        } else {
          this.saveFail = true;
          console.log(error);
        }
      }
    },
    async saveBtnHandler(type, value) {
      try {
        const careValue = {
          water: value,
        };
        const subjectId = getSubjectId();
        // console.log(type, careValue);
        const response = await API.FetchUpdateCareData(
          subjectId,
          type,
          careValue
        );
        // console.log(response);
        if (response.status === 201) {
          this.getHistoryData(this.count);
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    formatDate(count) {
      const [year, month, day] = this.curDate(count);
      switch (count) {
        case "d":
          return [year, month, day];
        case "w":
          return [year, month, day];
        case "m":
          return [year, month, 1];
        case "y":
          return [year, 0, 1];
      }
    },

    curDate(count) {
      const curLocalTime = new Date();
      const start = new Date();
      start.setDate(start.getDate() - start.getDay() + 1);
      const year =
        count === "w" ? start.getFullYear() : curLocalTime.getFullYear();
      const month = count === "w" ? start.getMonth() : curLocalTime.getMonth();
      const day = count === "w" ? start.getDate() : curLocalTime.getDate();
      return [year, month, day];
    },

    getDayDate() {
      const localTime = new Date();
      const year = localTime.getFullYear();
      const month = localTime.getMonth();
      const day = localTime.getDate();
      return `${String(year).slice(2)}.${month + 1}.${day}`;
    },

    getWeekPeriod() {
      const now = new Date();
      const start = new Date(now);
      start.setDate(now.getDate() - now.getDay() + 1);

      const end = new Date(start);
      end.setDate(start.getDate() + 6);
      // 해당 주의 월요일, 일요일 날짜 반환

      const monYear = start.getFullYear();
      const monMonth = start.getMonth() + 1;
      const monDay = start.getDate();
      const sunYear = end.getFullYear();
      const sunMonth = end.getMonth() + 1;
      const sunDay = end.getDate();
      return `${String(monYear).slice(2)}.${monMonth}.${monDay} ~ ${String(
        sunYear
      ).slice(2)}.${sunMonth}.${sunDay}`;
    },

    getMonthPeriod() {
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth(), 1);
      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      // 해당 월의 시작일자와 말일 반환
      const year = start.getFullYear();
      const month = start.getMonth() + 1;
      const endDate = end.getDate();
      return `${String(year).slice(2)}.${month}.1 ~ ${String(year).slice(
        2
      )}.${month}.${endDate}`;
    },

    getYearPeriod() {
      const now = new Date();
      const yaer = now.getFullYear();
      return `${String(yaer).slice(2)}.01 ~ ${String(yaer).slice(2)}.12`;
    },

    getUtcOffset() {
      const now = new Date();
      const utcOffsetMinutes = now.getTimezoneOffset();
      const utcOffsetHours = -utcOffsetMinutes / 60;
      return utcOffsetHours;
    },

    async getHistoryData(count) {
      this.loaded = false;
      try {
        const [year, month, day] = this.formatDate(count);
        const utcStart = new Date(year, month, day, 0, 0, 0).toISOString();
        const [y, m, d] = this.curDate();
        const utcEnd = new Date(y, m, d + 1, 0, 0, 0).toISOString();
        const subjectId = getSubjectId();
        const utcOffset = this.getUtcOffset();
        const periodType = this.count === "y" ? `&periodType=year` : "";
        const { data, config } = await API.GetCareData(
          subjectId,
          this.page,
          utcStart,
          utcEnd,
          utcOffset,
          periodType
        );
        // console.log(count, data, config.url);
        this.makeData(data, count);
      } catch (error) {
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
        console.log(error);
      }
    },

    makeData(data, count) {
      this.waterDecreaseValue = (data.subject.targetWater - this.water).toFixed(
        1
      );
      this.toTargetValue =
        (this.water - data.subject.targetWater).toFixed(1) < 0
          ? 0
          : (this.water - data.subject.targetWater).toFixed(1);
      this.targetWater = data.subject.targetWater;
      if (
        data?.records?.length === 0 ||
        data?.monthlyData?.length === 0 ||
        data?.dailyData?.length === 0
      ) {
        if (data.average === null) {
          switch (count) {
            case "d": {
              this.noData = false;
              // const initialWeight = data.subject.initialWeight;
              this.volume = 0;
              this.date = this.getDayDate();
              break;
            }
            case "w": {
              this.noData = true;
              this.date = this.getWeekPeriod();
              break;
            }
            case "m": {
              this.noData = true;
              this.date = this.getMonthPeriod();
              break;
            }
            case "y": {
              this.noData = true;
              this.date = this.getYearPeriod();
              break;
            }
          }
          this.graphData = [];
          this.historyData = [];
          this.loaded = true;
        }
        this.loaded = true;
      } else {
        switch (count) {
          case "d": {
            this.noData = false;
            this.volume = data.dailyData[0].value;
            this.date = this.getDayDate();
            const careData = DataProcessing.GET_WATER_CARE_DATA(data, count);
            this.historyData = careData.historyData;
            break;
          }
          case "w":
            this.date = this.getWeekPeriod();
            break;
          case "m":
            this.date = this.getMonthPeriod();
            break;
          case "y":
            this.date = this.getYearPeriod();
            break;
        }
        const careData = DataProcessing.GET_WATER_CARE_DATA(data, count);
        const maxValue = careData.maxValue;
        this.historyData = careData.historyData;
        this.graphData = careData.graphData;
        this.weekDayData = careData.dayAvgData;
        this.water = data.average;
        this.volume = data.average;
        this.targetWater =
          maxValue < data.subject?.targetWater
            ? data.subject?.targetWater
            : maxValue;
        this.remaining = this.targetWater - this.volume;
        this.toTargetValue = (data.subject?.targetWeight - this.weight).toFixed(
          1
        );
        this.noData = false;
        this.loaded = true;
      }
    },

    getWaterDetailDate(fromChild) {
      this.loaded = true;
      // console.log(fromChild);
    },
    getMonth(fromChild) {
      this.getWaterDetailDate(fromChild);
      this.graphData = this.monthData[`${fromChild}`].map((item) => {
        const itemDate = item.createdAt.split("T")[0];
        const itemTime = item.createdAt.split("T")[1];
        const [year, month, date] = itemDate.split("-");
        const [hour, minute] = itemTime.split(":");

        return {
          value: item.value,
          createdAt: `${year}.${month}.${date} ${hour}:${minute}PM`,
        };
      });
    },
    getDate(fromChild) {
      // console.log(fromChild);
      this.loaded = true;
      this.specificDate = fromChild.createdAt;
    },
    addWaterHistoryList(fromChild) {
      this.historyData = [...this.historyData, fromChild];
    },
    changeCount(count) {
      if (this.count !== count) {
        this.count = count;
        this.getHistoryData(count);
      }
    },
    closeWeekTrendModal(fromChild) {
      this.showWeekTrendModal = fromChild;
    },
    openEditModalWindow() {
      this.$store.commit("setBackBtnClicked", false);
      this.showEditModalWindow = true;
    },
    closeEditModalWindow() {
      this.showEditModalWindow = false;
    },
    showWeeksTrendGraph() {
      this.showWeekTrendModal = true;
    },
    cancelHandler() {
      this.showGuide = false;
      localStorage.setItem("waterGuide", false);
    },
    goToHealthInfo() {
      this.$router.push("/profile/survey");
    },

    closeBtnHandler(fromChild) {
      this.$store.commit("setSurveyModal", false);
    },
    surveyHandler(fromChild) {
      this.$router.push("/survey");
    },
  },
  mounted() {
    this.lang = this.$i18n.locale === "ko" ? "ko" : "en";
    this.guideImg = require(`@/assets/images/guide_img/target_water_guide_${this.lang}.png`);
    this.getHistoryData(this.count);
    this.showGuide =
      !!JSON.parse(localStorage.getItem("waterGuide")) ||
      JSON.parse(localStorage.getItem("waterGuide")) === null
        ? true
        : false;
  },
};
</script>

<style lang="scss" scoped>
.white-background {
  background-color: #fff;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
}

.bg-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 450px;
  height: 100%;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.8);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.close-icon__wrapper {
  text-align: left;
  position: absolute;
  top: 65px;
  left: 30px;
  img {
    width: 26px;
  }
}
.close-icon__wrapper-android {
  text-align: left;
  position: absolute;
  top: 35px;
  left: 30px;
  img {
    width: 26px;
  }
}

.water-guide-img__wrapper {
  position: absolute;
  width: 100%;
  top: 145px;
  padding: 0 20px;
  img {
    width: 100%;
  }
}
.water-guide-img__wrapper-android {
  position: absolute;
  width: 100%;
  top: 115px;
  padding: 0 20px;
  img {
    width: 100%;
  }
}

.guide-contents__wrapper {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 240px;
}

.guide-title {
  color: #41d8e6;
  font-size: 20px;
  font-weight: 700;
}

.guide-contents {
  color: #ffffff;
  font-size: 16px;
  margin: 30px 50px;
}

.setting-btn__wrapper {
  width: 100%;
  height: 55px;
  position: absolute;
  bottom: 80px;
  left: 0;
  padding: 0 30px;
}

.setting-item__btn {
  margin-top: 15px;
  width: 100%;
  background-color: #41d8e6;
  border-radius: 10px;
  color: #fff;
  line-height: 55px;
  font-weight: 700;
  font-size: 22px;
  letter-spacing: -0.03em;
}

.comment-txt__wrapper {
  font-weight: 500;
  font-size: 16px;
  line-height: 20px;
  letter-spacing: -0.03em;
  color: #646464;
  text-align: left;
  padding: 20px 30px;
  span {
    color: #41d8e6;
  }
}
</style>
