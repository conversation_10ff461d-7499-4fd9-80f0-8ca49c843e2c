<template>
  <background>
    <Header
      :type="careType"
      :avgScore="0"
      :count="period"
      :date="date"
      :bloodPressure="headerBloodPressure"
      @openEditModalWindow="openEditModalWindow"
    />
    <div class="background">
      <template v-if="period === 'd'">
        <BloodPressureIndicator
          :systolic="lastSystolic"
          :diastolic="lastDiastolic"
        />
      </template>
      <template v-else>
        <BloodPressureChart
          :bloodPressureData="bloodPressureChartData"
          :period="period"
          @clickEventHandler="handleChartClick"
        />
      </template>
      <CareChartBtns :count="period" @changeCount="handleChangeCount" />
    </div>
    <div class="blood-pressure-summary">
      <template
        v-if="
          period !== 'd' &&
          summarySystolic !== null &&
          summaryDiastolic !== null
        "
      >
        <span class="systolic"
          >수축기
          {{ summarySystolic !== null ? summarySystolic : "0" }} mmHg</span
        >
        <span class="divider"> ㅣ </span>
        <span class="diastolic"
          >이완기
          {{ summaryDiastolic !== null ? summaryDiastolic : "0" }} mmHg</span
        >
      </template>
      <template v-else>
        <span class="guide-text"
          >목표 혈당에 도달할 수 있도록 관리해보세요!</span
        >
      </template>
    </div>
    <CareHistoryTable :type="careType" :count="period" :history="historyData" />

    <SettingBloodPressureGoalModal
      v-if="getIsShowBloodPressurePopup"
      :systolic="systolic"
      :diastolic="diastolic"
      @save-success="onSaveSuccess"
      @close="updateHeaderBloodPressureGoal"
    />

    <RecordBloodPressureModal
      v-if="getRecordModalState"
      @save-success="onSaveSuccess"
    />

    <div class="snackbar">
      <v-snackbar v-model="saveSuccess" timeout="2000">{{
        successContent
      }}</v-snackbar>
      <v-snackbar v-model="saveFail" timeout="2000" color="#EE0000">{{
        failContent
      }}</v-snackbar>
    </div>
  </background>
</template>

<script>
import { bloodPressure } from "@/api/care";

import {
  formatBloodPressure,
  formatBloodPressureHistory,
  getPeriodRange,
  calculateBloodPressureAverage,
} from "@/utils";

import Header from "@/components/Care/Header";
import BloodPressureIndicator from "@/components/Care/BloodPressureIndicator.ui.vue";
import CareChartBtns from "@/components/Care/CareChartBtns.vue";
import BloodPressureChart from "@/components/Chart/BloodPressureChart.vue";
import CareHistoryTable from "@/components/Care/CareHistoryTable.vue";
import SettingBloodPressureGoalModal from "@/components/Care/SettingBloodPressureGoalModal.ui.vue";
import RecordBloodPressureModal from "@/components/Care/RecordBloodPressureModal.ui.vue";

export default {
  components: {
    Header,
    BloodPressureIndicator,
    CareChartBtns,
    BloodPressureChart,
    CareHistoryTable,
    SettingBloodPressureGoalModal,
    RecordBloodPressureModal,
  },

  data() {
    return {
      careType: "bloodPressure",

      //  systolic, diastolic
      systolic: 0,
      diastolic: 0,
      headerBloodPressure: "",

      // period
      period: "d",
      date: "",

      // api parameter (startDate, endDate)
      startDate: "",
      endDate: "",

      historyData: [],
      scatterRangeData: [],
      bloodPressureChartData: {
        "06.19": { systolic: 140, diastolic: 90 },
        "06.20": { systolic: 130, diastolic: 88, range: [118, 142] },
        "06.21": { systolic: 150, diastolic: 70 },
        "06.22": { systolic: 120, diastolic: 80 },
        "06.23": { systolic: 130, diastolic: 85 },
        "06.24": { systolic: 120, diastolic: 80 },
        "06.25": { systolic: 140, diastolic: 95, range: [118, 148] },
      },

      saveSuccess: false,
      saveFail: false,
      successContent: this.$i18n.t("save_success"),
      failContent: this.$i18n.t("save_fail"),

      summarySystolic: null,
      summaryDiastolic: null,
    };
  },

  computed: {
    getRecordModalState() {
      return this.$store.state.care.recordModalVisible ?? false;
    },
    getIsShowBloodPressurePopup() {
      return this.$store.state.care.isShowBloodPressurePopup ?? false;
    },
    lastSystolic() {
      if (!this.historyData.length) return null;

      const bp = this.historyData.at(0).value.split(" ").at(0);
      const systolic = bp.split("/")[0];

      return systolic ? Number(systolic) : null;
    },
    lastDiastolic() {
      if (!this.historyData.length) return null;

      const bp = this.historyData.at(0).value.split(" ").at(0);
      const diastolic = bp.split("/")[1];

      return diastolic ? Number(diastolic) : null;
    },
  },

  methods: {
    handleChangeCount(count) {
      this.loaded = false;
      this.period = count;

      // api refetching
    },

    handleChartClick(value) {
      // value: { label, data } 형태
      console.log(value);
      if (!value || !value.data) {
        this.summarySystolic = 0;
        this.summaryDiastolic = 0;
        return;
      }
      this.summarySystolic = value.data.systolic;
      this.summaryDiastolic = value.data.diastolic;
    },
    chartDataLabelByIndex(index) {
      // chartData.labels와 동일하게 key 순서 반환
      return Object.keys(this.bloodPressureChartData)[index];
    },

    formatToString(date) {
      const [year, month, day] = date;

      return `${String(year).slice(2)}.${String(month + 1).padStart(
        2,
        "0"
      )}.${String(day).padStart(2, "0")}`;
    },

    getCurrentDate(period) {
      const currentLocalTime = new Date();
      const start = new Date();
      start.setDate(start.getDate() - start.getDay() + 1);

      let year = currentLocalTime.getFullYear();
      let month = currentLocalTime.getMonth();
      let day = currentLocalTime.getDate();

      if (period === "w") {
        year = start.getFullYear();
        month = start.getMonth();
        day = start.getDate();
      }

      return [year, month, day];
    },

    async fetchBloodPressureData() {
      try {
        const startDate = this.startDate;
        const endDate = this.endDate;
        // monthly option @params {boolean}
        const isYear = this.period === "y";

        // api 호출
        await bloodPressure
          .getBloodPressure(1, startDate, endDate, 9, isYear)
          .then(({ data }) => {
            const { systolic, diastolic } = calculateBloodPressureAverage(data);

            if (this.period === "d") {
              const { systolic: s, diastolic: d } = JSON.parse(
                localStorage.getItem("bloodPressureGoal") ?? "{}"
              );

              this.headerBloodPressure = `${s}/${d}`;
            } else {
              this.headerBloodPressure = `${systolic}/${diastolic}`;
            }

            // 그래프 데이터
            this.bloodPressureChartData = formatBloodPressure(
              data,
              this.period
            );

            // 하단 기록 데이터
            this.historyData = formatBloodPressureHistory(data, this.period);
          })
          .catch((err) => {
            // exception
            console.error(err);

            throw err;
          });
      } catch (_error) {
        // try-catch
        console.error(_error);
      }
    },

    openEditModalWindow() {
      this.$store.commit("setIsShowBloodPressurePopup", true);
    },

    onSaveSuccess() {
      this.saveSuccess = true;
      this.fetchBloodPressureData();
    },

    updateHeaderBloodPressureGoal() {
      // localStorage에서 목표 혈압을 읽어와서 headerBloodPressure에 반영
      const goal = localStorage.getItem("bloodPressureGoal");
      if (goal) {
        try {
          const { systolic, diastolic } = JSON.parse(goal);
          this.headerBloodPressure = `${systolic}/${diastolic}`;
        } catch (e) {
          this.headerBloodPressure = "";
        }
      }
    },
  },

  watch: {
    period(newPeriod) {
      // period값 달라질 때마다 새 startDate, endDate 할당
      const { startDate, endDate } = getPeriodRange(newPeriod);

      this.startDate = startDate;
      this.endDate = endDate;

      this.fetchBloodPressureData();
    },
  },

  mounted() {
    this.date = this.formatToString(this.getCurrentDate("d"));

    const { startDate, endDate } = getPeriodRange(this.period);
    this.startDate = startDate;
    this.endDate = endDate;

    this.fetchBloodPressureData();
  },
};
</script>

<style lang="scss" scoped>
.background {
  width: 100vw;
  max-width: 450px;
  max-height: 450px;
  background: #ffffff;
}

.blood-pressure-summary {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 30px;
  margin: 25px 0;
  color: #646464;
}

.systolic,
.diastolic,
.guide-text {
  font-weight: 500;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: -3%;
  vertical-align: middle;
}

.divider {
  padding: 0 4px;
}
</style>
