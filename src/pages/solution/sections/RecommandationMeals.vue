<template>
  <div>
    <Loading v-if="loading" />
    <div class="bg-white">
      <!-- 음식 리스트 -->
      <div>
        <!--Recommand Food -->
        <h3 class="food-recoomand-cym">Cym<sup>702</sup> 추천</h3>
        <div>
          <div class="food-card__wrapper">
            <MealKit
              v-for="(food, idx) in cymRecommandFoods"
              :key="idx"
              :page="'filteredFood'"
              :foodId="food.id"
              :foodImage="food.english"
              :foodName="isKo ? food.korean : food.english"
              :foodEnergy="food.calorie"
              :foodAmount="food.amount"
              :foodInfo="food"
              :selectedItemLength="selectedItemLength"
              @foodCardClickHandler="foodCardClickHandler"
              @getSelectedItem="getSelectedItem"
              :favorite="food.foodBookmark !== undefined"
            />
          </div>
        </div>

        <hr class="divide" />

        <button class="food-tag" v-for="(tag, idx) in selectedTag" :key="idx">
          {{ tag }}
        </button>
      </div>
      <div class="dp-20">
        <div class="select-filter-btns">
          <div class="btn__wrapper">
            <div
              :class="
                activeSelectionBtn ? ['select-btn', 'active'] : 'select-btn'
              "
            >
              <button @click="selectionBtnHandler">
                {{ $t("btn_select") }}
              </button>
            </div>
            <div
              :class="activeFilterBtn ? ['select-btn', 'active'] : 'select-btn'"
            >
              <button @click="filterBtnHandler">
                <div class="btn-text">{{ $t("btn_filter") }}</div>
                <div class="img__container">
                  <img
                    src="@/assets/solution/icons/filter.png"
                    alt="filter icon"
                  />
                </div>
              </button>
            </div>
          </div>
        </div>
        <div v-if="noData" class="no-data">
          <div class="no-data__title">{{ $t("no_result_food") }}</div>
          <div class="no-data__subtitle">{{ $t("try_filter_reset") }}</div>
        </div>
        <div
          :class="[active, isIos ? 'meal-kit-items' : 'meal-kit-items-android']"
          @scroll="handleFoodListScroll"
          ref="food-list"
        >
          <div class="food-card__wrapper">
            <MealKit
              v-for="(food, idx) in meals"
              :key="idx"
              :page="'filteredFood'"
              :foodId="food.id"
              :foodImage="food.english"
              :foodName="isKo ? food.korean : food.english"
              :foodEnerge="food.calorie"
              :foodAmount="food.amount"
              :foodInfo="food"
              :selectedItemLength="selectedItemLength"
              @foodCardClickHandler="foodCardClickHandler"
              @getSelectedItem="getSelectedItem"
              :favorite="food.foodBookmark[0] !== undefined"
            />
          </div>
        </div>
      </div>
    </div>
    <SelectBox
      v-if="showSelectModal"
      :tabTitle="tabName"
      :selectedItemCount="selectedItemLength"
      @bookmarkListHandler="bookmarkListHandler"
      @addToMyMealChecking="addToMyMealChecking"
    />
    <MealFilterModal
      v-if="showFilterModal"
      @closeFilterModal="closeFilterModal"
      @selectedFilterHandler="selectedFilterHandler"
    />
    <div class="snackbar">
      <v-snackbar v-model="successMsgOpen" timeout="2000">{{
        saveContent
      }}</v-snackbar>
    </div>
  </div>
</template>

<script>
import MealKit from "@/components/Solution/MealKit.vue";
import SelectBox from "@/components/Solution/SelectBox.vue";
import Loading from "@/components/Common/Loading.vue";
import MealFilterModal from "@/components/Solution/MealFilterModal.vue";

import EventBus from "@/event-bus.js";
import API from "@/api/solution/index.js";

export default {
  components: {
    MealKit,
    SelectBox,
    Loading,
    MealFilterModal,
  },
  data() {
    return {
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
      noData: false,
      loaded: false,
      loading: false,
      noMoreData: false,
      titleClicked: false,
      meals: [],
      showSelectBtn: false,
      tabName: "recommandation-meal",
      cymRecommandFoods: [],
      selectedItems: [],
      selectedFoodList: [],
      page: 1,
      diet: [],
      vegetarian: null,
      type: [],
      calorie: [],
      tagArr: [],
      selectedTag: [],
      showFilterModal: false,
      isSelectedFilter: false,
      isKo: true,
      scrollTop: 0,
      successMsgOpen: false,
      saveContent: "",
      subjectId: localStorage.getItem("subjectId"),
    };
  },
  computed: {
    showSelectModal() {
      return this.$store.state.showSelectModal;
    },
    activeSelectionBtn() {
      return this.$store.state.selectionBtn;
    },
    activeFilterBtn() {
      return this.$store.state.filterBtn;
    },
    selectedItemLength() {
      return this.selectedItems.length;
    },
    handleClickTitle() {
      return this.$store.state.clickTitle;
    },
    active() {
      return this.showSelectModal ? "active-select-box" : false;
    },
  },
  watch: {
    handleClickTitle(val) {
      this.scrollToTop();
    },
    meals(newVal) {
      newVal.length === 0 ? (this.noData = true) : (this.noData = false);
    },
  },
  methods: {
    // 음식 카드 클릭 시 세션스토리지에 정보 저장 후 뒤로가기시에 해당 위치, 데이터
    foodCardClickHandler(fromChild) {
      sessionStorage.setItem("savedDataPage", this.page);
      sessionStorage.setItem("savedFoodList", JSON.stringify(this.meals));
      sessionStorage.setItem(
        "savedCymRecommandFoods",
        JSON.stringify(this.cymRecommandFoods)
      );
      sessionStorage.setItem("scrollPosition", this.scrollTop);
      // console.log("this.scrollTop", this.scrollTop);
    },
    closeFilterModal() {
      this.showFilterModal = false;
    },
    selectedFilterHandler() {
      this.selectedTag = [];
      this.updateFilter();
      this.reloadFilteredFoodList();
    },
    makeTagArr(isSelectedFilter) {
      if (!isSelectedFilter) {
        this.diet.map((tag, idx) => {
          if (tag === 2)
            this.selectedTag.push(
              this.$i18n.t("tag_high_fat"),
              this.$i18n.t("tag_low_carb")
            );
          if (tag === 3) this.selectedTag.push(this.$i18n.t("tag_low_carb"));
          if (tag === 4) this.selectedTag.push(this.$i18n.t("tag_low_carl"));
          if (tag === 5) this.selectedTag.push(this.$i18n.t("tag_pesco"));
          if (tag === 6) this.selectedTag.push(this.$i18n.t("tag_lacto"));
          if (tag === 7) this.selectedTag.push(this.$i18n.t("tag_vegan"));
          if (tag === 8) this.selectedTag.push(this.$i18n.t("tag_low_sodium"));
          if (tag === 9) this.selectedTag.push(this.$i18n.t("tag_low_sugar"));
          if (tag === 11)
            this.selectedTag.push(this.$i18n.t("tag_high_protein"));
        });
      } else {
        this.diet !== ""
          ? this.diet.map((diet) => {
              if (diet === 0)
                this.selectedTag.push(this.$i18n.t("tag_high_fat"));
              if (diet === 1)
                this.selectedTag.push(this.$i18n.t("tag_low_carb"));
              if (diet === 2)
                this.selectedTag.push(this.$i18n.t("tag_low_carl"));
              if (diet === 3)
                this.selectedTag.push(this.$i18n.t("tag_high_protein"));
              if (diet === 4)
                this.selectedTag.push(this.$i18n.t("tag_low_sodium"));
              if (diet === 5)
                this.selectedTag.push(this.$i18n.t("tag_low_sugar"));
            })
          : null;

        if (this.vegetarian === 0)
          this.selectedTag.push(this.$i18n.t("tag_vegan"));
        if (this.vegetarian === 1)
          this.selectedTag.push(this.$i18n.t("tag_lacto"));
        if (this.vegetarian === 2)
          this.selectedTag.push(this.$i18n.t("tag_pesco"));

        this.type !== ""
          ? this.type.map((type) => {
              if (type === 0) this.selectedTag.push(this.$i18n.t("tag_korean"));
              if (type === 1)
                this.selectedTag.push(this.$i18n.t("tag_chinese"));
              if (type === 2)
                this.selectedTag.push(this.$i18n.t("tag_japanese"));
              if (type === 3)
                this.selectedTag.push(this.$i18n.t("tag_western"));
              if (type === 4) this.selectedTag.push(this.$i18n.t("tag_snack"));
              if (type === 5) this.selectedTag.push(this.$i18n.t("tag_salad"));
              if (type === 6) this.selectedTag.push(this.$i18n.t("tag_other"));
            })
          : null;
      }
      // console.log(this.selectedTag);
    },
    makeDietList(diet) {
      // console.log(diet);
      // console.log(diet.calorie_controlled_diet[0].choice);
      // console.log(diet.vegetarianism[0].choice);
      // console.log(diet.care_diet[0].choice);
      // diet.map((diet) => this.diet.push(diet.answerId));
    },

    async updateFilter() {
      const foodFilter = JSON.parse(localStorage.getItem("foodFilter"));
      this.isSelectedFilter = !!foodFilter;
      // console.log(foodFilter);
      this.page = 1;
      this.meals = [];
      if (foodFilter === null) {
        try {
          const { data } = await API.GetUserFilter(this.subjectId);
          // console.log(data);
          this.makeDietList(data);
          this.makeTagArr(this.isSelectedFilter);
        } catch (error) {
          console.error(error);
          if (error.response.status === 429) {
            this.$store.commit("setoverReqModal", true);
          }
        }
      } else {
        this.diet = foodFilter.diet ? foodFilter.diet.sort() : "";
        this.vegetarian = foodFilter.vege;
        // console.log(this.vegetarian);
        this.type = foodFilter.type ? foodFilter.type.sort() : "";
        const calorieArr = [];
        foodFilter.calorie
          ? foodFilter.calorie.map((calorie) => calorieArr.push(calorie * 100))
          : null;
        this.calorie = calorieArr;
        // console.log(calorieArr);
        this.makeTagArr(this.isSelectedFilter);
      }
    },
    makeQuery() {
      const page = `?page=${this.page}`;
      const dietArr = [];
      this.diet.map((diet) => {
        if (diet === 0) dietArr.push("high_fat");
        if (diet === 1) dietArr.push("low_carb");
        if (diet === 2) dietArr.push("low_calorie");
        if (diet === 3) dietArr.push("high_protein");
        if (diet === 4) dietArr.push("low_sodium");
        if (diet === 5) dietArr.push("low_sugar");
      });
      const dietString = dietArr.join();
      // console.log(dietString);
      const dietQuery = this.diet.length !== 0 ? `&diet=${dietString}` : "";

      let vegetarianString = "";
      if (this.vegetarian === 0) vegetarianString = "vegan";
      if (this.vegetarian === 1) vegetarianString = "lacto_ovo";
      if (this.vegetarian === 2) vegetarianString = "pesco";
      // console.log(vegetarianString);
      const vegetarianQuery =
        vegetarianString !== "" ? `&vegetarian=${vegetarianString}` : "";

      const typeArr = [];
      // console.log(this.type);
      this.type !== ""
        ? this.type.map((type) => {
            if (type === 0) typeArr.push("korean");
            if (type === 1) typeArr.push("chinese");
            if (type === 2) typeArr.push("japanese");
            if (type === 3) typeArr.push("western");
            if (type === 4) typeArr.push("snack");
            if (type === 5) typeArr.push("salad");
            if (type === 6) typeArr.push("etc");
          })
        : null;
      const typeString = typeArr.join();
      const typeQuery = this.type.length !== 0 ? `&type=${typeString}` : "";

      // console.log(this.calorie[0]);
      const calorieString =
        this.calorie.length === 0 || this.calorie[1] === 0
          ? ""
          : `${this.calorie[0]}-${this.calorie[1]}`;
      const calorie = calorieString !== "" ? `&calorie=${calorieString}` : "";
      return [page, dietQuery, vegetarianQuery, typeQuery, calorie];
    },
    async getFilteredFoodList() {
      // console.log(this.loading);
      const [page, diet, vegetarian, type, calorie] = this.makeQuery();
      if (this.loading || this.noMoreData) return;

      this.loaded = false;
      this.loading = true;
      try {
        const { config, data } = await API.GetFilteredFood(
          this.subjectId,
          page,
          diet,
          vegetarian,
          type,
          calorie,
          this.page === 1 ? true : false // 수정: this.page 사용
        );

        const foodList = this.page === 1 ? data.slice(2, data.length) : data;
        if (this.page === 1) {
          const cymRecommandFoods = [data[0], data[1]];
          this.cymRecommandFoods = cymRecommandFoods;
        }
        // console.log(config.url);
        // console.log(data);
        // console.log(data[0].foodBookmark[0]);
        foodList.length === 0 && this.page === 1
          ? (this.meals = [])
          : foodList?.map((foodData) => this.meals.push(foodData));
        if (foodList.length === 0) this.noMoreData = true;

        this.loading = false;
        this.loaded = true;
      } catch (error) {
        this.loading = false;
        console.error(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    async reloadFilteredFoodList() {
      const [page, diet, vegetarian, type, calorie] = this.makeQuery();
      this.loaded = false;
      this.loading = true;
      this.noMoreData = false;
      this.meals = [];
      this.page = 1;
      try {
        const { config, data } = await API.GetFilteredFood(
          this.subjectId,
          page,
          diet,
          vegetarian,
          type,
          calorie,
          this.page === 1 ? true : false // 수정: this.page 사용
        );
        // console.log(config.url);
        // console.log(data);

        const foodList = this.page === 1 ? data.slice(2, data.length) : data;
        if (this.page === 1) {
          const cymRecommandFoods = [data[0], data[1]];
          this.cymRecommandFoods = cymRecommandFoods;
        }
        // console.log(config.url);
        // console.log(data);
        // console.log(data[0].foodBookmark[0]);
        foodList.length === 0 && this.page === 1
          ? (this.meals = [])
          : foodList?.map((foodData) => this.meals.push(foodData));

        this.loading = false;
        this.loaded = true;
      } catch (error) {
        this.loading = false;
        console.error(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    scrollToTop() {
      this.$refs["food-list"].scroll({ top: 0, behavior: "smooth" });
    },

    handleFoodListScroll(e) {
      const { scrollHeight, scrollTop, clientHeight } = e.target;
      // console.log("scrollHeight",scrollHeight);
      // console.log("scrollTop", scrollTop);
      // console.log("clientHeight",clientHeight);
      this.scrollTop = scrollTop;
      if (this.loading || this.noMoreData) return;

      this.scrollToTop === 0
        ? this.$store.commit("clickTitle", true)
        : this.$store.commit("clickTitle", false);
      const isAtTheBottom = this.isIos
        ? scrollHeight === scrollTop + clientHeight ||
          scrollHeight === scrollTop + clientHeight + 0.5
        : scrollHeight <= scrollTop + clientHeight + 1;
      // 일정 한도 밑으로 내려오면 함수 실행
      if (isAtTheBottom) this.handleLoadMore();
    },

    // 내려오면 api 호출하여 아래에 더 추가, total값 최대이면 호출 안함
    handleLoadMore() {
      this.page += 1;
      this.getFilteredFoodList();
      // console.log("Need to Loading");
    },

    // 필터버튼 핸들러
    filterBtnHandler() {
      this.showFilterModal = true;
    },

    // 선택버튼 핸들러
    selectionBtnHandler() {
      if (this.$store.state.selectionBtn) {
        this.$store.commit("selectionBtn", false);
        this.$store.commit("hideSelectBtn");
        this.$store.commit("closeSelectModal");
      } else {
        this.selectedItems = [];
        this.$store.commit("selectionBtn", true);
        this.$store.commit("showSelectBtn");
      }
    },

    // mealkit.vue 컴포넌트에서 선택된 음식의 index값을 가져오는 함수
    getSelectedItem(fromChild) {
      // console.log(fromChild);
      const selectedItems = this.selectedItems;
      if (selectedItems.includes(fromChild)) {
        this.selectedItems = selectedItems.filter((idx) => {
          if (idx !== fromChild) {
            return idx;
          }
        });
      } else {
        this.selectedItems = [...this.selectedItems, fromChild];
      }
    },

    // 즐겨찾기 리스트에서 즐겨찾기 추가
    bookmarkListHandler() {
      this.saveBookmarkList();
    },

    // 식단추가 함수
    addToMyMealChecking(fromChild) {
      this.saveRecordList(fromChild);
    },

    async saveBookmarkList() {
      const selectedFood = { foodIds: this.selectedItems };
      // console.log(selectedFood);
      try {
        const res = await API.PostBookMark(this.subjectId, selectedFood);
        // console.log(res.status);
        if (res.status === 201 || res.status === 207) {
          EventBus.$emit("favoriteAdded");
          this.saveContent = this.$i18n.t("complete_success");
          this.successMsgOpen = true;
          let selectedFoodList = [];

          this.selectedItems.forEach((foodId) => {
            let foods = this.meals.find((food) => food.id === foodId);
            if (foods) {
              selectedFoodList.push(foods);
            }
          });
          selectedFoodList.forEach((food) => {
            let index = this.meals.findIndex((f) => f.id === food.id);
            if (index !== -1) {
              this.meals[index].foodBookmark = [true];
            }
          });
          sessionStorage.removeItem("savedBookmarkList");
          sessionStorage.removeItem("scrollbookmarkPosition");
          sessionStorage.removeItem("savedBookmarkDataPage");
        }
      } catch (error) {
        console.error(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    async saveRecordList(type) {
      const filteredArr = [];
      this.meals
        .filter((i) => this.selectedItems.includes(i.id))
        .map((food) => {
          filteredArr.push({ foodId: food.id, gram: food.amount });
        });
      const foodData = { records: filteredArr };
      // console.log(filteredArr);
      try {
        const res = await API.PostFoodRecord(this.subjectId, type, foodData);
        // console.log(res.status);
        if (res.status === 201) {
          EventBus.$emit("recordAdded");
          this.saveContent = this.$i18n.t("complete_add_record");
          this.successMsgOpen = true;
        }
      } catch (error) {
        console.error(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    setScrollData() {
      // this.loaded = false;
      // this.loading = true;
      const savedDataPage = JSON.parse(sessionStorage.getItem("savedDataPage"));
      const savedFoodList = JSON.parse(sessionStorage.getItem("savedFoodList"));
      const savedCymRecommandFoods = JSON.parse(
        sessionStorage.getItem("savedCymRecommandFoods")
      );
      const scrollPosition = JSON.parse(
        sessionStorage.getItem("scrollPosition")
      );
      if (savedFoodList) {
        this.meals = savedFoodList;
        this.page = savedDataPage;
        this.scrollTop = scrollPosition;
        if (savedCymRecommandFoods) {
          this.cymRecommandFoods = savedCymRecommandFoods;
        }
        this.loaded = true;
        this.loading = false;
      } else {
        this.getFilteredFoodList();
      }
      this.$nextTick(() => {
        this.scrollToPosition();
      });
    },
    scrollToPosition() {
      // scroll 위치 저장해놨다가 불러와서 이동
      const scrollPosition = sessionStorage.getItem("scrollPosition");
      if (scrollPosition !== 0)
        this.$refs["food-list"].scroll({ top: scrollPosition });
    },
  },
  mounted() {
    this.updateFilter();
    this.setScrollData();
    EventBus.$on("favoriteDeleted", () => {
      this.reloadFilteredFoodList();
    });
    EventBus.$on("favoriteAdded", () => {
      this.reloadFilteredFoodList();
    });
    this.isKo = this.$i18n.locale.includes("ko");
  },
};
</script>

<style lang="scss" scoped>
.dp-20 {
  padding: 10px 10px 85px 10px;
}
.bg-white {
  background-color: #fff;
  // height: 70vh;
  // height: calc(100vh - 270px);
}

.select-filter-btns {
  display: flex;
  justify-content: flex-start;
  padding: 0 10px 10px 0;
  width: 100%;
}

.btn__wrapper {
  // max-width: 140px;
  display: flex;
  justify-content: end;
}

.select-btn {
  /* Yellosis gray/1 */
  background-color: transparent;
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: 80px;
  padding: 0 10px;
  border: 1px solid #ededed;
  border-radius: 6px;
  margin-left: 7px;
  font-size: 16px;
  color: #858585;
  font-weight: 500;
  line-height: 25px;
  height: 30px;
  white-space: nowrap;
  button {
    display: flex;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
  .btn-text {
    width: 70%;
    height: 100%;
  }
  .img__container {
    width: 30%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  img {
    object-fit: contain;
    margin-left: 3px;
    height: 13px;
  }
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding-left: 20px;
}

.no-data__title {
  font-weight: 500;
  font-size: 20px;
}
.no-data__subtitle {
  color: #646464;
  // font-weight: 500;
  font-size: 16px;
}

.active {
  line-height: 25px;
  background-color: #41d8e6;
  padding: 0 10px;
  border: 1px solid #41d8e6;
  border-radius: 5px;
  margin-left: 7px;
  font-size: 16px;
  color: #fff;
  font-weight: 500;
}
.food-tag__container {
  text-align: left;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  display: flex;
  white-space: nowrap;
  gap: 0 10px;
  width: 100%;
  padding: 10px 20px 0 20px;
  &::-webkit-scrollbar {
    display: none !important;
  }
}

.food-tag {
  height: 28px;
  background-color: #ededed;
  border-radius: 20px;
  padding: 2px 12px;
  font-size: 15px;
  letter-spacing: -0.03em;
  font-weight: 400;
  font-style: normal;
}

.meal-kit-items {
  height: calc(100vh - 360px);
  padding-right: 5px;
  overflow: auto;
}
.meal-kit-items-android {
  height: calc(100vh - 330px);
  padding-right: 5px;
  overflow: auto;
}
.active-select-box {
  padding-bottom: 170px;
}

.food-card__wrapper {
  width: 100%;
  padding: 0 5px 0 10px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 8px;
}

.food-recoomand-cym {
  font-size: 18px;
  color: #41d8e6;
  font-weight: bold;
  text-align: left;
  padding: 15px 20px;
}

.divide {
  background: #ededed;
  height: 10px;
  margin: 10px 0 0 0;
  border: none;
}
</style>
