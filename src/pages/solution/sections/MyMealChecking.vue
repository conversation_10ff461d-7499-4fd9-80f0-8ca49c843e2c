<template>
  <div>
    <Loading v-if="loading" />
    <div v-if="loaded" class="bg-white dp-30">
      <div class="meal-check__title">
        {{ $t("all_calories") }}
        <v-icon @click="openEditModalHandler">$setting_btn</v-icon>
      </div>
      <div class="meal-check__amount" v-if="myTotalKcal === '0.0'">0 kcal</div>
      <div class="meal-check__amount" v-else>
        {{ Number(myTotalKcal).toLocaleString("ko-KR") }} kcal
      </div>
      <div class="slider">
        <v-progress-linear
          :value="totalKcalPercent"
          background-color="#C9F4F8"
          color="#41d8e6"
          rounded
          height="13"
        ></v-progress-linear>
        <div class="slider-labels">
          <div class="slider-label__zero">0</div>
          <div class="slider-label__max">{{ targetCalorie.toLocaleString("ko-KR") }}</div>
        </div>
      </div>

      <!-- 영양성분 -->
      <div class="nutrition__wrapper">
        <div class="nutrition-contents" v-for="(item, idx) in nutritionArr" :key="idx">
          <div class="nutrition-item" v-if="item.value === 0.0">{{ item.value }}</div>
          <div class="nutrition-item" v-else>{{ Number(item.value.toFixed(1)) }}</div>
          <div class="nutrition-gram">{{ item.name }}</div>
        </div>
      </div>

      <div class="meal-check__btns">
        <!-- 아침 버튼 -->
        <div class="rectangle-btn rectangle-btn--active" v-if="breakfast.length > 0">
          <div>
            <div class="rectangle-btn__num">{{ sumBreakfastKcal }}</div>
            <div class="rectangle-btn__unit">kcal</div>
          </div>
          <div class="rectangle-btn__name">{{ $t("breakfast") }}</div>
        </div>

        <div class="rectangle-btn" @click="goToFoodSearchHandler('breakfast')" v-else>
          <div class="rectangle-btn__img">
            <img src="@/assets/images_assets/icons/plus-blue-ic.png" alt="" />
          </div>
          <div class="rectangle-btn__name">{{ $t("breakfast") }}</div>
        </div>

        <!-- 점심 버튼 -->
        <div class="rectangle-btn rectangle-btn--active" v-if="lunch.length > 0">
          <div>
            <div class="rectangle-btn__num">{{ sumLunchKcal }}</div>
            <div class="rectangle-btn__unit">kcal</div>
          </div>
          <div class="rectangle-btn__name">{{ $t("lunch") }}</div>
        </div>

        <div class="rectangle-btn" @click="goToFoodSearchHandler('lunch')" v-else>
          <div class="rectangle-btn__img">
            <img src="@/assets/images_assets/icons/plus-blue-ic.png" alt="" />
          </div>
          <div class="rectangle-btn__name">{{ $t("lunch") }}</div>
        </div>

        <!-- 저녁 버튼 -->
        <div class="rectangle-btn rectangle-btn--active" v-if="dinner.length > 0">
          <div>
            <div class="rectangle-btn__num">{{ sumDinnerKcal }}</div>
            <div class="rectangle-btn__unit">kcal</div>
          </div>
          <div class="rectangle-btn__name">{{ $t("dinner") }}</div>
        </div>

        <div class="rectangle-btn" @click="goToFoodSearchHandler('dinner')" v-else>
          <div class="rectangle-btn__img">
            <img src="@/assets/images_assets/icons/plus-blue-ic.png" alt="" />
          </div>
          <div class="rectangle-btn__name">{{ $t("dinner") }}</div>
        </div>

        <!-- 간식 버튼 -->
        <div class="rectangle-btn rectangle-btn--active" v-if="snack.length > 0">
          <div>
            <div class="rectangle-btn__num">{{ sumSnackKcal }}</div>
            <div class="rectangle-btn__unit">kcal</div>
          </div>
          <div class="rectangle-btn__name">{{ $t("snack") }}</div>
        </div>
        <div class="rectangle-btn" @click="goToFoodSearchHandler('snack')" v-else>
          <div class="rectangle-btn__img">
            <img src="@/assets/images_assets/icons/plus-blue-ic.png" alt="" />
          </div>
          <div class="rectangle-btn__name">{{ $t("snack") }}</div>
        </div>
      </div>

      <div v-for="(meal, idx) in mealArr" :key="idx">
        <RecordList :title="meal.id" :meal="meal.data" @selectBoxHandler="selectBoxHandler" />
      </div>
    </div>
    <RecordSelectBox
      v-if="showSelectModal"
      :tabTitle="'my-meals'"
      :selectedItemCount="selectedItemLength"
      @deletedItems="deletedItems"
      @timeEdit="timeEdit"
    />
    <EditValueModal
      v-if="showEditModal"
      :targetValue="targetCalorie"
      :page="'solution'"
      @closeEditModalWindow="closeEditModalWindow"
      @saveBtnHandler="saveBtnHandler"
    />
    <div class="snackbar">
      <v-snackbar v-model="saveSuccess" timeout="2000">{{ succesContent }}</v-snackbar>
      <v-snackbar v-model="saveFail" timeout="2000" color="#EE0000">{{ failContent }}</v-snackbar>
    </div>
  </div>
</template>

<script>
import EditValueModal from "@/components/Care/EditWaterValueModal.vue";
import RecordSelectBox from "@/components/Solution/RecordSelectBox.vue";
import RecordList from "@/components/Solution/RecordList.vue";
import Loading from "@/components/Common/Loading.vue";
import API from "@/api/solution/index.js";
import { updateSubjectInfo } from "@/api/user/index.js";

import EventBus from "@/event-bus.js";

export default {
  props: {
    setCaloriesClicked: Boolean,
  },
  components: {
    EditValueModal,
    RecordSelectBox,
    RecordList,
    Loading,
  },
  data() {
    return {
      loaded: false,
      loading: false,
      showEditModal: false,
      todayMeal: [],
      dinner: [],
      lunch: [],
      breakfast: [],
      snack: [],
      mealArr: [],
      nutritionArr: [
        { id: "carb", name: `${this.$i18n.t("carbohydrate")}(g)`, value: 0 },
        { id: "protein", name: `${this.$i18n.t("protein_ingredient")}(g)`, value: 0 },
        { id: "fat", name: `${this.$i18n.t("fat")}(g)`, value: 0 },
        { id: "sugar", name: `${this.$i18n.t("sugar")}(g)`, value: 0 },
        { id: "sodium", name: `${this.$i18n.t("sodium")}(mg)`, value: 0 },
      ],
      targetCalorie: 0,
      isSelected: false,
      isFoodSelected: false,
      selectedFood: {},
      selectedItemLength: 0,
      subjectId: localStorage.getItem("subjectId"),
      saveSuccess: false,
      succesContent: this.$i18n.t("save_success"),
      saveFail: false,
      failContent: this.$i18n.t("save_fail"),
    };
  },
  computed: {
    showSelectModal() {
      return this.$store.state.showRecordSelectModal;
    },
    sumBreakfastKcal() {
      if (this.breakfast.length === 0) {
        return "";
      }
      const kcallist = this.breakfast.map((food) =>
        Number(food.food.calorie * (food.gram / food.food.amount))
      );
      const sum = kcallist.reduce((a, b) => a + b);
      return Number(sum.toFixed(1));
    },
    sumLunchKcal() {
      if (this.lunch.length === 0) {
        return "";
      }
      const kcallist = this.lunch.map((food) =>
        Number(food.food.calorie * (food.gram / food.food.amount))
      );
      const sum = kcallist.reduce((a, b) => a + b);
      return Number(sum.toFixed(1));
    },
    sumDinnerKcal() {
      if (this.dinner.length === 0) {
        return "";
      }
      const kcallist = this.dinner.map((food) =>
        Number(food.food.calorie * (food.gram / food.food.amount))
      );
      const sum = kcallist.reduce((a, b) => a + b);
      return Number(sum.toFixed(1));
    },
    sumSnackKcal() {
      if (this.snack.length === 0) {
        return "";
      }
      const kcallist = this.snack.map((food) =>
        Number(food.food.calorie * (food.gram / food.food.amount))
      );
      const sum = kcallist.reduce((a, b) => a + b);
      return Number(sum.toFixed(1));
    },
    myTotalKcal() {
      const totalSum = Number(
        Number(this.sumBreakfastKcal) +
          Number(this.sumLunchKcal) +
          Number(this.sumDinnerKcal) +
          Number(this.sumSnackKcal)
      ).toFixed(1);
      return totalSum;
      // return Math.ceil(2000 / totalSum) * 10;
    },
    totalKcalPercent() {
      return (this.myTotalKcal / this.targetCalorie) * 100;
    },
    active() {
      return this.isSelected ? "active-record-btn" : "record-handle-btn";
    },
  },
  watch: {
    isSelected(newVal) {
      // if (!newVal) this.showSelectModal = false;
    },
    setCaloriesClicked(newVal) {
      // console.log(newVal);
      this.showEditModal = true;
    },
  },
  methods: {
    selectBtnHandler() {
      this.isSelected = !this.isSelected;
    },
    selectBoxHandler(selectedFoods) {
      // console.log(selectedFoods);
      this.selectedFood = selectedFoods;
      this.selectedItemLength = selectedFoods.foods.length;
      // selectedFoods.foods.length !== 0 ? (this.showSelectModal = true) : (this.showSelectModal = false);
    },
    deletedItems(newVal) {
      // console.log(newVal);
      this.deleteRecord();
    },
    timeEdit(newVal) {
      // console.log(newVal);
      const [isAm, hours, minutes] = newVal;
      const utcTime = this.makeUtcTime(isAm, hours, minutes);
      this.updateRecord(utcTime);
    },
    openEditModalHandler() {
      this.showEditModal = true;
    },
    closeEditModalWindow() {
      this.showEditModal = false;
    },
    goToFoodSearchHandler(type) {
      this.$router.push({ path: `/food/search`, query: { type: type } });
    },
    goToFoodInfoEditHandler(type, foodId) {
      this.$router.push({ path: `/food/edit/${foodId}`, query: { type: type } });
    },
    async deleteRecord() {
      // console.log("delete btn clicked");
      // this.loading = true;
      const type = this.selectedFood.type;
      const records = { records: this.selectedFood.foods };
      // console.log(type, records);
      try {
        const { status } = await API.DeleteFoodRecord(type, this.subjectId, records);
        // console.log(status, records);
        if (status === 204) {
          this.saveSuccess = true;
          this.getRecordList();
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        } else this.saveFail = true;
      }
    },
    convert24Format(isAm, hour, min) {
      // Remove any leading zeros
      hour = parseInt(hour, 10).toString();
      min = parseInt(min, 10).toString();

      if (!isAm) {
        // Convert afternoon hours to 24-hour format
        if (hour !== "12") {
          hour = (parseInt(hour, 10) + 12).toString();
        }
      } else {
        // Convert midnight (12 AM) to 24-hour format
        if (hour === "12") {
          hour = "00";
        }
      }

      return [hour, min];
    },

    makeUtcTime(isAm, hour, min) {
      // console.log(isAm, hour, min);
      const [hh, mm] = this.convert24Format(isAm, hour, min);
      // console.log(hh, mm);
      const unixTime = new Date().getTime();
      const curLocalTime = new Date(unixTime);
      const year = curLocalTime.getFullYear();
      const month = curLocalTime.getMonth(); // month is start at 0
      const day = curLocalTime.getDate();
      // let hours = curLocalTime.getHours();
      // let minutes = curLocalTime.getMinutes();
      // const hh = isAm ?
      const newDateString = new Date(year, month, day, hh, mm).toISOString();
      // console.log(newDateString);
      return newDateString;
      // const timeset = Number(hours) > 12 ? "PM" : "AM";
    },
    async updateRecord(utcTime) {
      // console.log("update btn clicked");
      // const date = new Date().toISOString();
      const type = this.selectedFood.type;
      // console.log(this.selectedFood.foods);
      const records = [];
      this.selectedFood.foods.map((i, idx) => records.push({ recordId: i, createdAt: utcTime }));
      const newRecords = records.map((record, index) => {
        return {
          ...record,
          recordId: this.selectedFood.foods[index],
        };
      });
      // console.log(records);
      const body = { records: records };
      try {
        const { data, status } = await API.PatchFoodRecord(type, this.subjectId, body);
        // console.log(status, data);
        if (status === 200 || status === 207) {
          this.saveSuccess = true;
          this.getRecordList();
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    async getRecordList() {
      this.loading = true;
      this.todayMeal = [];
      this.dinner = [];
      this.lunch = [];
      this.breakfast = [];
      this.snack = [];
      this.mealArr = [];
      this.nutritionArr = [];
      this.nutritionArr = [
        { id: "carb", name: `${this.$i18n.t("carbohydrate")}(g)`, value: 0 },
        { id: "protein", name: `${this.$i18n.t("protein_ingredient")}(g)`, value: 0 },
        { id: "fat", name: `${this.$i18n.t("fat")}(g)`, value: 0 },
        { id: "sugar", name: `${this.$i18n.t("sugar")}(g)`, value: 0 },
        { id: "sodium", name: `${this.$i18n.t("sodium")}(mg)`, value: 0 },
      ];

      const unixTime = new Date().getTime();
      const curLocalTime = new Date(unixTime);

      let year = curLocalTime.getFullYear();
      let month = curLocalTime.getMonth();
      let day = curLocalTime.getDate();

      const unixStart = new Date(year, month, day).getTime();
      const utcStart = new Date(unixStart).toISOString();
      const unixEnd = new Date(year, month, day + 1).getTime();
      const utcEnd = new Date(unixEnd).toISOString();
      // console.log(utcStart, utcEnd);
      try {
        const { data, config } = await API.GetFoodRecord(
          this.subjectId,
          `start=${utcStart}&end=${utcEnd}`
        );
        // console.log(data, config.url);
        // 총 탄, 단, 지, 당, 나 값 구하기
        this.breakfast = data.breakfastRecord;
        this.lunch = data.lunchRecord;
        this.dinner = data.dinnerRecord;
        this.snack = data.snackRecord;
        const dataArr = [
          { id: "breakfast", data: data.breakfastRecord },
          { id: "lunch", data: data.lunchRecord },
          { id: "dinner", data: data.dinnerRecord },
          { id: "snack", data: data.snackRecord },
        ];
        this.mealArr = dataArr;
        // console.log(this.breakfast, this.lunch, this.dinner, this.snack);
        this.breakfast.map((food) => (food.createdAt = this.convertDate(food.createdAt)));
        this.lunch.map((food) => (food.createdAt = this.convertDate(food.createdAt)));
        this.dinner.map((food) => (food.createdAt = this.convertDate(food.createdAt)));
        this.snack.map((food) => (food.createdAt = this.convertDate(food.createdAt)));
        this.todayMeal = [];
        this.todayMeal.push(
          { breakfast: data.breakfastRecord },
          { lunch: data.lunchRecord },
          { dinner: data.dinnerRecord },
          { snack: data.snackRecord }
        );

        this.todayMeal.forEach((mealType) => {
          Object.keys(mealType).forEach((meal) => {
            const records = mealType[meal];
            if (Array.isArray(records) && records.length > 0) {
              records.forEach((record) => {
                this.nutritionArr.forEach((item) => {
                  item.value += record.food[item.id];
                });
              });
            }
          });
        });
        // console.log(this.breakfast);
        this.targetCalorie = data.metadata.targetCalorie;
        // console.log(this.totalKcalPercent);

        this.loading = false;
        this.loaded = true;
      } catch (error) {
        console.log(error);
        this.loading = false;
        this.loaded = true;
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    async saveBtnHandler(kcalValue) {
      // console.log("save btn clicked");
      this.myTotalKcal = kcalValue;
      try {
        const changedTargetCalorie = {
          targetCalorie: Number(kcalValue),
        };
        const res = await updateSubjectInfo(this.subjectId, changedTargetCalorie);
        // console.log(res);
        if (res.status === 200) {
          // console.log("success");
          this.saveSuccess = true;
          this.getRecordList();
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        } else this.saveFail = true;
      }
    },
    convertDate(createdAt) {
      // console.log(createdAt);
      const unixTime = new Date(createdAt).getTime();
      const localTime = new Date(unixTime);
      let hours = localTime.getHours();
      let minutes = localTime.getMinutes();
      const timeset = Number(hours) >= 12 ? "PM" : "AM";
      // console.log(hours, minutes, timeset);
      Number(hours) > 12 ? (hours = Number(hours) - 12) : null;
      Number(hours) < 10 ? (hours = `0${hours}`) : null;
      Number(minutes) < 10 ? (minutes = `0${minutes}`) : null;
      return `${hours}:${minutes} ${timeset}`;
    },
  },
  mounted() {
    this.getRecordList();
    EventBus.$on("recordAdded", () => {
      this.getRecordList();
    });
  },
};
</script>

<style lang="scss" scoped>
.bg-white {
  background-color: #fff;
  padding: 45px 30px 20vh;
  height: calc(100vh - 270px);
  overflow: auto;
}

.meal-check__title {
  font-weight: 500;
  font-size: 22px;
  text-align: left;
  padding-bottom: 20px;
  letter-spacing: -0.03em;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  font-family: GilroyMedium;
  font-size: 16px;
  color: #646464;
}

.nutrition-contents {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  width: 20%;
}

.nutrition__wrapper {
  display: flex;
  gap: 10px;
  width: 100%;
  justify-content: space-between;
  margin-top: 20px;
}

.nutrition-item {
  background-color: #f8f8f8;
  height: 35px;
  min-width: 50px;
  max-width: 55px;
  border-radius: 6px;
  font-family: GilroyMedium;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nutrition-gram {
  color: #646464;
  font-size: 11px;
  letter-spacing: -0.05em;
}

.meal-check__btns {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-gap: 10px;
  padding-top: 30px;
}

.meal-check__amount {
  font-family: GilroyBold;
  text-align: left;
  font-size: 26px;
  line-height: 26px;
  padding-bottom: 15px;
  letter-spacing: -0.03em;
}

.rectangle-btn {
  background-color: #ededed;
  border-radius: 5px;
  width: 100%;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: #646464;
  padding: 20px 0 15px 0;
}
.rectangle-btn__num {
  font-family: GilroyBold;
  line-height: 25px;
  font-size: 22px;
  line-height: 16px;
  // margin: 5px 0;
}

.rectangle-btn__unit {
  font-family: GilroyMedium;
  font-size: 16px;
  line-height: 16px;
}

.rectangle-btn__name {
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  // margin-top: 5px;
}

.rectangle-btn--active {
  background-color: #c9f4f8;
}

.meal-categories {
  padding-top: 32px;
}

.meal-categories__title {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #a7a7a7;
  padding-bottom: 10px;
  gap: 5px;
}

.meal-categories__title--txt {
  font-size: 18px;
  font-weight: 500;
}

.record-handle-btn {
  padding: 0 10px;
  border: 1px solid #ededed;
  border-radius: 5px;
  margin-left: 7px;
  font-size: 16px;
  font-weight: 500;
  color: #858585;
}

.active-record-btn {
  color: #ffffff;
  border: 1px solid #41d8e6;
  background-color: #41d8e6;
}

.meal-categories__title--ic {
  img {
    width: 14px;
  }
}

.select-btn {
  display: flex;
  align-items: center;
  gap: 5px;
}

.select-btn__button {
  height: 25px;
  img {
    width: 25px;
  }
}

.meal-category {
  // width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}
.item__title {
  color: #646464;
  font-size: 16px;
}

.item__createdat {
  font-family: GilroyMedium;
  color: #a7a7a7;
  font-size: 14px;
  // line-height: 14px;
}

.item__amount {
  font-size: 18px;
  color: #646464;
  span {
    padding-right: 10px;
    font-family: GilroyMedium;
  }
  img {
    width: 10px;
  }
}

.rectangle-btn__img {
  img {
    width: 23px;
  }
}

::v-deep .v-progress-linear--rounded {
  border-radius: 10px;
}
::v-deep .v-progress-linear__determinate {
  border-radius: 10px;
}
::v-deep .v-progress-linear__buffer {
  background-color: #c9f4f8;
}
</style>
