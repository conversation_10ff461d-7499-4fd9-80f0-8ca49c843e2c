<template>
  <div>
    <Loading v-if="loading" />
    <div class="bg-white">
      <div class="no-data__wrapper" v-if="this.meals.length === 0">
        <!-- <div class="img-container">
          <img src="@/assets/images_assets/waterdrop-img/normal.png" alt="normal" />
          <img src="@/assets/images_assets/waterdrop-img/good.png" alt="good" />
          <img src="@/assets/images_assets/waterdrop-img/warning.png" alt="warning" />
        </div> -->
        <div class="empty-state">{{ $t("no_favorite") }}</div>
        <div class="description">{{ $t("find_favorite") }}</div>
      </div>
      <template v-else>
        <div class="select-filter-btns">
          <div :class="activeSelectionBtn ? ['select-btn', 'active'] : 'select-btn'">
            <button @click="selectionBtnHandler" v-if="inspectShowSelectBtn">
              {{ $t("btn_select") }}
            </button>
          </div>
        </div>
        <div
          :class="[active, isIos ? 'meal-kit-items' : 'meal-kit-items-android']"
          @scroll="handleFoodListScroll"
          ref="bookmark-list"
        >
          <div class="food-card__wrapper">
            <MealKit
              v-for="(food, idx) in meals"
              :key="idx"
              :page="'bookmark'"
              :foodId="food.id"
              :foodImage="food.food.english"
              :foodName="isKo ? food.food.korean : food.food.english"
              :foodEnerge="food.food.calorie"
              :foodAmount="food.food.amount"
              :foodInfo="food.food"
              :selectedItemLength="selectedItemLength"
              @foodCardClickHandler="foodCardClickHandler"
              @getSelectedItem="getSelectedItem"
              type="bookmark"
            />
          </div>
        </div>
      </template>
    </div>
    <SelectBox
      v-if="showSelectModal"
      :tabTitle="tabName"
      :selectedItemCount="selectedItemLength"
      @bookmarkListHandler="bookmarkListHandler"
      @addToMyMealChecking="addToMyMealChecking"
    />
    <div class="snackbar">
      <v-snackbar v-model="successMsgOpen" timeout="2000">{{ saveContent }}</v-snackbar>
    </div>
  </div>
</template>

<script>
import MealKit from "@/components/Solution/MealKit.vue";
import SelectBox from "@/components/Solution/SelectBox.vue";
import Loading from "@/components/Common/Loading.vue";
import API from "@/api/solution/index.js";

import EventBus from "@/event-bus.js";

export default {
  // props: {
  //   reloadBookmark: Boolean,
  // },
  components: {
    MealKit,
    SelectBox,
    Loading,
  },
  data() {
    return {
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
      loaded: false,
      loading: false,
      noMoreData: false,
      meals: [],
      tabName: "bookmark-meals",
      page: 1,
      selectedItems: [],
      isKo: true,
      scrollTop: 0,
      successMsgOpen: false,
      saveContent: this.$i18n.t("complete_delete"),
      subjectId: localStorage.getItem("subjectId"),
    };
  },
  watch: {
    handleClickTitle(newVal) {
      // console.log(newVal);
      this.scrollToTop();
    },
  },
  computed: {
    showSelectModal() {
      return this.$store.state.showSelectModal;
    },
    inspectShowSelectBtn() {
      if (this.meals.length === 0) return false;
      else return true;
    },
    activeSelectionBtn() {
      return this.$store.state.selectionBtn;
    },
    selectedItemLength() {
      return this.selectedItems.length;
    },
    handleClickTitle() {
      return this.$store.state.clickTitle;
    },
    isReload() {
      return this.$store.state.isReload;
    },
    active() {
      return this.showSelectModal ? "active-select-box " : false;
    },
  },
  methods: {
    // 음식 카드 클릭 시 세션스토리지에 정보 저장 후 뒤로가기시에 해당 위치, 데이터
    foodCardClickHandler(fromChild) {
      sessionStorage.setItem("savedBookmarkDataPage", this.page);
      sessionStorage.setItem("savedBookmarkList", JSON.stringify(this.meals));
      sessionStorage.setItem("scrollbookmarkPosition", this.scrollTop);
      // console.log(fromChild);
    },
    async getBookmarklist() {
      if (this.loading || this.noMoreData) return;
      this.loaded = false;
      this.loading = true;
      try {
        const { config, data } = await API.GetBookMark(this.subjectId, this.page);
        // console.log(data, config.url, this.page);
        if (data.length === 0) this.noMoreData = true;
        data?.length === 0 && this.page === 1 ? (this.meals = []) : data?.map((foodData) => this.meals.push(foodData));
        // console.log(this.meals);
        this.loading = false;
        this.loaded = true;
      } catch (error) {
        this.loading = false;
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    async reloadBookmarklist() {
      this.loaded = false;
      this.loading = true;
      this.noMoreData = false;
      this.meals = [];
      this.page = 1;
      sessionStorage.setItem("reloadBookmark", false);
      try {
        const { config, data } = await API.GetBookMark(this.subjectId, this.page);
        // console.log("reload data:", data);
        data.map((foodData) => this.meals.push(foodData));
        this.loading = false;
        this.loaded = true;
      } catch (error) {
        this.loading = false;
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    scrollToTop() {
      this.$refs["bookmark-list"].scroll({ top: 0, behavior: "smooth" });
    },

    handleFoodListScroll(e) {
      const { scrollHeight, scrollTop, clientHeight } = e.target;
      // console.log(scrollHeight, scrollTop, clientHeight);
      if (this.loading || this.noMoreData) return;
      this.scrollTop = scrollTop;
      this.scrollToTop === 0 ? this.$store.commit("clickTitle", true) : this.$store.commit("clickTitle", false);
      const isAtTheBottom = this.isIos
        ? scrollHeight === scrollTop + clientHeight || scrollHeight === scrollTop + clientHeight + 0.5
        : scrollHeight <= scrollTop + clientHeight + 1;
      if (isAtTheBottom) this.handleLoadMore();
    },
    handleLoadMore() {
      this.page += 1;
      this.getBookmarklist();
    },

    setScrollData() {
      // this.loaded = false;
      // this.loading = true;
      const savedDataPage = JSON.parse(sessionStorage.getItem("savedBookmarkDataPage"));
      const savedFoodList = JSON.parse(sessionStorage.getItem("savedBookmarkList"));
      const scrollPosition = JSON.parse(sessionStorage.getItem("scrollbookmarkPosition"));
      if (savedFoodList) {
        this.meals = savedFoodList;
        this.page = savedDataPage;
        this.scrollTop = scrollPosition;

        this.loaded = true;
        this.loading = false;
      } else {
        this.getBookmarklist();
      }
      this.$nextTick(() => {
        if (scrollPosition) this.scrollToPosition();
      });
    },
    scrollToPosition() {
      // scroll 위치 저장해놨다가 불러와서 이동
      const scrollPosition = sessionStorage.getItem("scrollbookmarkPosition");
      // console.log(scrollPosition);
      if (scrollPosition !== 0) this.$refs["bookmark-list"].scroll({ top: scrollPosition });
    },

    // 선택버튼 핸들러
    selectionBtnHandler() {
      if (this.$store.state.selectionBtn) {
        this.$store.commit("selectionBtn", false);
        this.$store.commit("hideSelectBtn");
        this.$store.commit("closeSelectModal");
      } else {
        this.selectedItems = [];
        this.$store.commit("selectionBtn", true);
        this.$store.commit("showSelectBtn");
      }
    },

    // mealkit.vue 컴포넌트에서 선택된 음식의 index값을 가져오는 함수
    getSelectedItem(fromChild) {
      const selectedItems = this.selectedItems;
      // console.log(fromChild);
      if (selectedItems.includes(fromChild)) {
        this.selectedItems = selectedItems.filter((id) => {
          if (id !== fromChild) {
            return id;
          }
        });
      } else {
        this.selectedItems = [...this.selectedItems, fromChild];
      }
      // console.log(this.selectedItems.length);
    },
    // 즐겨찾기 리스트에서 즐겨찾기 삭제
    bookmarkListHandler() {
      this.deleteBookmarkList();
    },

    async deleteBookmarkList() {
      // console.log(this.selectedItems);
      const selectedFood = { bookmarkIds: this.selectedItems };
      try {
        const res = await API.DeleteBookMark(this.subjectId, selectedFood);
        // console.log(res);
        if (res.status === 204) {
          EventBus.$emit("favoriteDeleted");
          this.successMsgOpen = true;
          this.reloadBookmarklist();
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    // 식단추가 함수
    addToMyMealChecking(fromChild) {
      this.saveRecordList(fromChild);
    },
    async saveRecordList(type) {
      const filteredArr = [];
      this.meals
        .filter((i) => this.selectedItems.includes(i.id))
        .map((food) => {
          filteredArr.push({ foodId: food.food.id, gram: food.food.amount });
        });
      const foodData = { records: filteredArr };
      // console.log(filteredArr);
      try {
        const { status } = await API.PostFoodRecord(this.subjectId, type, foodData);
        // console.log(status);
        if (status === 201) {
          EventBus.$emit("recordAdded");
          this.saveContent = this.$i18n.t("complete_add_record");
          this.successMsgOpen = true;
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
  },
  created() {
    EventBus.$on("favoriteAdded", () => {
      // console.log("event bus reload");
      this.reloadBookmarklist();
    });
  },
  mounted() {
    this.isKo = this.$i18n.locale === "ko";
    const isReload = JSON.parse(sessionStorage.getItem("reloadBookmark"));
    // console.log(isReload);
    isReload ? this.reloadBookmarklist() : this.setScrollData();
    // console.log(this.meals);
  },
};
</script>

<style lang="scss" scoped>
.bg-white {
  background-color: #fff;
  // min-height: 100vh;
  // padding-top: 30px;
  padding: 10px 10px 50px 10px;
}

.no-data__wrapper {
  padding: 20px;
}
.meal-kit-items {
  height: calc(100vh - 330px);
  padding-right: 5px;
  overflow: auto;
}
.meal-kit-items-android {
  height: calc(100vh - 290px);
  padding-right: 5px;
  overflow: auto;
}

.active-select-box {
  padding-bottom: 170px;
}

.img-container {
  width: 100%;
  display: flex;
  padding: 10px 0;
  img {
    width: 35px;
    margin-right: 10px;
  }
}

.empty-state {
  display: flex;
  font-weight: 500;
  font-size: 20px;
}

.description {
  display: flex;
  color: #646464;
  font-size: 16px;
}

.food-tag__container {
  text-align: left;
  width: 100%;
  // margin-top: 20px;
}

.food-tag {
  background-color: #ededed;
  border-radius: 20px;
  margin: 8px 10px 8px 0;
  padding: 2px 12px;
  font-size: 18px;
  text-align: center;
  letter-spacing: -0.03em;
}

.select-filter-btns {
  display: flex;
  justify-content: flex-end;
  padding: 0 10px 10px;
}

.select-btn {
  /* Yellosis gray/1 */
  padding: 0 10px;
  border: 1px solid #ededed;
  border-radius: 5px;
  margin-left: 7px;
  font-size: 16px;
  color: #858585;
  font-weight: 500;
  justify-content: center;
  display: flex;
  width: 55px;
  max-width: 70px;
}

.active {
  background-color: #41d8e6;
  color: #fff;
  border: 1px solid #41d8e6;
  font-weight: 500;
}

.food-card__wrapper {
  width: 100%;
  padding: 0 5px 0 10px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 8px;
}
</style>
