<template>
  <div>
    <div class="bg-modal" v-show="showGuide">
      <div
        :class="isIos ? 'close-icon__wrapper' : 'close-icon__wrapper-android'"
        @click="cancelHandler"
      >
        <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
      </div>
      <div
        :class="
          isIos ? 'solution-desc__wrapper' : 'solution-desc__wrapper-android'
        "
      >
        <div
          class="solution-desc1"
          v-html="this.$i18n.t('record_description')"
        ></div>
        <div class="reverse-arrow-icon__wrapper">
          <img src="@/assets/images/guide_img/arrow-up.png" />
        </div>
        <div class="desc-img__wrapper">
          <img :src="guideImg" />
        </div>
        <div class="arrow-icon__wrapper">
          <img src="@/assets/images/guide_img/arrow-up.png" />
        </div>
        <div
          class="solution-desc2"
          v-html="this.$i18n.t('general_calories')"
        ></div>
      </div>
      <div class="setting-btn__wrapper">
        <div class="setting-item__btn" @click="setCalories">
          {{ $t("set_calories") }}
        </div>
      </div>
    </div>

    <background>
      <div>
        <Header />
        <!-- 페이지 헤더 네비게이션 -->
        <div class="history-header">
          <div class="history-header__wrapper">
            <div
              :class="
                isIos ? 'history-header_nav' : 'history-header_nav-android'
              "
            >
              <router-link to="/home">
                <v-icon>$back_btn_bold</v-icon>
              </router-link>
            </div>
            <div @click="handleClickTitle" class="history-header_title">
              Solution
            </div>
          </div>
        </div>

        <!-- 탭 헤더 네비게이션 -->
        <div class="tab-container">
          <v-tabs
            v-model="tabName"
            color="#C9F4F8"
            class="history-tabs pa-0"
            center-active
            grow
            @change="changeTabs"
            mobile-breakpoint="xs"
            slider-color="#41D8E6"
            slider-size="3"
            height="40px"
          >
            <v-tab class="px-0 mx-0" href="#recomandation-meals">{{
              $t("solution_tab1")
            }}</v-tab>
            <v-tab class="px-0 mx-0" href="#bookmark-meals">{{
              $t("solution_tab2")
            }}</v-tab>
            <v-tab class="px-0 mx-0" href="#my-meals">{{
              $t("solution_tab3")
            }}</v-tab>
          </v-tabs>
        </div>
      </div>

      <!-- 탭 아이템들 -->
      <v-tabs-items v-model="tabName" color="transparent" :touchless="true">
        <v-tab-item value="recomandation-meals" class="tab-item">
          <RecommandationMeals />
        </v-tab-item>
        <!-- 북마크 메뉴 -->
        <v-tab-item value="bookmark-meals" class="tab-item">
          <BookMarkMeals />
        </v-tab-item>
        <v-tab-item value="my-meals" class="tab-item">
          <MyMealChecking :setCaloriesClicked="setCaloriesClicked" />
        </v-tab-item>
      </v-tabs-items>
    </background>

    <!-- 음식 필터 컴포넌트 -->
    <!-- <MealFilterModal v-if="showFilterModal" @closeFilterModal="closeFilterModal" /> -->

    <Navigation :path="path" />
  </div>
</template>

<script>
// page component
import RecommandationMeals from "./sections/RecommandationMeals.vue";
import BookMarkMeals from "./sections/BookMarkMeals.vue";
import MyMealChecking from "./sections/MyMealChecking.vue";

// functional components
import Navigation from "@/components/Common/Navigation.vue";
import Header from "@/components/History/Header.vue";
import Background from "@/components/Common/Background.vue";
// import MealFilterModal from "@/components/Solution/MealFilterModal.vue";

export default {
  // props: {
  //   titleClicked: Boolean,
  //   reloadList: Boolean,
  // },
  components: {
    Navigation,
    Header,
    Background,
    RecommandationMeals,
    BookMarkMeals,
    MyMealChecking,
    // MealFilterModal,
  },
  data() {
    return {
      path: "/solution",
      currentTabIndex: "",
      tabName: "recomandation-meals",
      showFilterModal: false,
      selectedItems: [],
      bookmarklist: [],
      selectBtn: false,
      reloadBookmark: false,
      showGuide: false,
      setCaloriesClicked: false,
      guideImg: "",
      lang: "ko",
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
  computed: {
    showSelectModal() {
      return this.$store.state.showSelectModal;
    },
    computeSelectedItemLength() {
      return this.selectedItems.length;
    },
  },
  watch: {
    selectBtn(newVal) {
      // console.log(newVal);
      if (!newVal) {
        this.$store.commit("closeSelectModal");
        this.selectedItems = [];
      }
    },
    tabName(tabName) {
      // console.log(tabName);
      sessionStorage.setItem("solutionCurTab", tabName);
      if (tabName === "bookmark-meals") {
        sessionStorage.removeItem("savedFoodList");
        sessionStorage.removeItem("savedFoodList");
      } else if (tabName === "recomandation-meals") {
        sessionStorage.removeItem("savedBookmarkList");
      } else {
        sessionStorage.setItem("solutionCurTab", tabName);
        this.showGuide =
          !!JSON.parse(localStorage.getItem("solutionGuide")) ||
          JSON.parse(localStorage.getItem("solutionGuide")) === null
            ? true
            : false;
      }
    },
  },
  methods: {
    getSelectedItem(fromChild) {
      this.selectedItems = fromChild;
    },

    showSelectBtn(fromChild) {
      this.selectBtn = fromChild;
    },

    changeTabs(tabIndex) {
      this.currentTabIndex = tabIndex;
    },

    // // 필터모달창 열기
    // openFilterModal() {
    //   this.showFilterModal = true;
    // },

    // // 필터모달창 닫기
    // closeFilterModal() {
    //   this.showFilterModal = false;
    // },

    handleClickTitle() {
      // console.log("clicked");
      this.$store.commit("clickTitle", true);
      // this.titleClicked = true;
    },
    cancelHandler() {
      this.showGuide = false;
      localStorage.setItem("solutionGuide", false);
    },
    setCalories() {
      // console.log("setCalories");
      this.showGuide = false;
      this.setCaloriesClicked = true;
    },
  },
  updated() {
    this.$store.commit("hideSelectBtn");
    this.$store.commit("closeSelectModal");
    this.$store.commit("selectionBtn", false);
  },
  mounted() {
    this.lang = this.$i18n.locale === "ko" ? "ko" : "en";
    this.guideImg = require(`@/assets/images/guide_img/mymeals-guide_${this.lang}.png`);
    this.$store.commit("hideSelectBtn");
    this.$store.commit("closeSelectModal");
    this.$store.commit("selectionBtn", false);
    this.$store.commit("closeSelectModal");
    this.tabName = sessionStorage.getItem("solutionCurTab")
      ? sessionStorage.getItem("solutionCurTab")
      : "recomandation-meals";

    const subjects = JSON.parse(sessionStorage.getItem("subjects"));
    const selectUser = Number(sessionStorage.getItem("selectUser"));
    subjects !== null && selectUser >= 1 ? this.$router.push("/home") : false;
  },
};
</script>

<style lang="scss" scoped>
.bg-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 450px;
  max-width: 450px;
  height: 100%;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.8);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.close-icon__wrapper {
  text-align: left;
  position: absolute;
  top: 65px;
  left: 30px;
  img {
    width: 26px;
  }
}
.close-icon__wrapper-android {
  text-align: left;
  position: absolute;
  top: 35px;
  left: 30px;
  img {
    width: 26px;
  }
}

.solution-desc__wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 73%;
  justify-content: flex-start;
}
.solution-desc__wrapper-android {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 85%;
  justify-content: flex-start;
}

.solution-desc1 {
  color: #ffffff;
  width: 100%;
  text-align: left;
  padding: 0 10px;
}
.reverse-arrow-icon__wrapper {
  transform: rotate(180deg);
  display: flex;
  justify-content: flex-end;
  margin: 15px 0;
  padding: 0 10px;
  img {
    width: 18px;
  }
}

.desc-img__wrapper {
  width: 100%;
  img {
    width: 100%;
  }
}

.arrow-icon__wrapper {
  display: flex;
  justify-content: flex-end;
  margin: 15px 0;
  padding: 0 10px;
  img {
    width: 18px;
  }
}

.solution-desc2 {
  color: #ffffff;
  width: 100%;
  padding: 0 10px;
  text-align: right;
}

.setting-btn__wrapper {
  width: 100%;
  height: 55px;
  position: absolute;
  bottom: 80px;
  left: 0;
  padding: 0 30px;
}

.setting-item__btn {
  margin-top: 15px;
  width: 100%;
  background-color: #41d8e6;
  border-radius: 10px;
  color: #fff;
  line-height: 55px;
  font-weight: 700;
  font-size: 22px;
  letter-spacing: -0.03em;
}

.tab-container {
  background-color: transparent;
  padding: 10px 20px 0 20px;
}

::v-deep .v-tabs-slider-wrapper {
  color: #41d8e6;
  height: 3px !important;
  // padding: 0 5px;
}

::v-deep .v-tabs-slider {
  border-radius: 3px 3px 0px 0px !important;
}

::v-deep .v-tab {
  color: rgba(0, 0, 0, 0.32) !important;
  font-weight: 500 !important;
  line-height: 40px;
  letter-spacing: -0.05em !important;
  text-transform: none !important;
  font-size: 18px !important;
}

::v-deep .v-tab--active {
  font-size: 18px !important;
  font-weight: 500 !important;
  color: #000000 !important;
}

::v-deep .v-tabs-bar__content {
  background: transparent !important;
}

::v-deep .theme--light.v-tabs-items {
  background-color: transparent !important;
}

::v-deep .theme--light.v-tabs > .v-tabs-bar {
  background-color: transparent !important;
}

::v-deep .v-toolbar__content {
  height: auto !important;
}

::v-deep .v-tab {
  min-width: 60px !important;
  background-color: transparent !important;
}

// tab ripple 제거
::v-deep .v-ripple__container {
  display: none !important;
}

::v-deep .v-input--selection-controls__ripple {
  display: none !important;
}

::v-deep .v-tab:before {
  display: none !important;
}

::v-deep .theme--light.v-tabs-items {
  background-color: transparent !important;
}

.history-header {
  padding: 0px 30px;
}

.history-header_title {
  font-family: GilroyBold;
  font-size: 36px;
  text-align: left;
  // font-weight: bold;
  color: #000000;
}

.history-header_nav {
  display: flex;
  justify-content: flex-start;
  padding-top: 65px;
}
.history-header_nav-android {
  display: flex;
  justify-content: flex-start;
  padding-top: 30px;
}
</style>
