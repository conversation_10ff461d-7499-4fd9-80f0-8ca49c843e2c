<template>
  <div>
    <background>
      <div>
        <Header />
        <div class="history-header">
          <div class="history-header__wrapper">
            <div :class="isIos ? 'history-header_nav' : 'history-header_nav-android'">
              <router-link to="/solution"><v-icon>$back_btn_bold</v-icon></router-link>
            </div>
            <div class="history-header_title" :class="lang === 'ko' ? '' : 'en-title'">{{ $t("food_search") }}</div>
          </div>
        </div>
        <div class="bg-white">
          <div class="pt-30">
            <div class="input-container">
              <div class="search-input">
                <img src="@/assets/images_assets/icons/search-ic.png" />
                <input
                  v-model="searchInput"
                  @focus="focusInHandler"
                  @blur="focusOutHandler"
                  :placeholder="this.$i18n.t('food_search_placeHolder')"
                />
                <!-- <v-text-field v-model="searchInput"></v-text-field> -->
              </div>
              <div class="search-btn" @click="searchBtnHandler">{{ $t("search") }}</div>
            </div>
            <div v-if="!focusInput && !isSearched" class="search-result">
              <div class="search-result__header">
                <div class="tab__wrapper">
                  <v-tabs
                    v-model="tabName"
                    color="#C9F4F8"
                    center-active
                    @change="changeTabs"
                    mobile-breakpoint="xs"
                    slider-color="#41D8E6"
                    height="40px"
                  >
                    <!-- grow -->
                    <!-- slider-size="1" -->
                    <v-tab class="tab-title px-0 mx-0" href="#tab-recent">{{ $t("recent_food") }}</v-tab>
                    <v-tab class="tab-title px-0 mx-0" href="#tab-favorite">{{ $t("bookmark") }}</v-tab></v-tabs
                  >
                </div>
                <!-- <div class="header__title">최근 음식</div> -->
                <div class="header__addBtn" v-if="tabName === 'tab-recent'">
                  <v-btn
                    elevation="0"
                    color="#41D8E6"
                    class="add-btn"
                    :disabled="selectedRecentFoods.length === 0"
                    @click="recentFoodAddHandler"
                    >{{ $t("add_record") }}</v-btn
                  >
                </div>
                <div class="header__addBtn" v-else>
                  <v-btn
                    elevation="0"
                    color="#41D8E6"
                    class="add-btn"
                    :disabled="!isSelectedFood"
                    @click="addBtnHandler"
                    >{{ $t("add_record") }}</v-btn
                  >
                </div>
              </div>
              <v-tabs-items v-model="tabName">
                <v-tab-item value="tab-recent">
                  <SearchResultItem
                    :searchResult="recentSearch"
                    @addBtnHandler="changeAmountsFoodsHandler"
                    @selectBtnHandler="selectedRecentFoodsHandler"
                  />
                </v-tab-item>

                <v-tab-item value="tab-favorite">
                  <div class="result-list">
                    <!-- v-if="loaded" -->
                    <SearchResultItem
                      :searchResult="bookmarkList"
                      :isSearchedItem="false"
                      :isSearched="false"
                      :noMoreData="noMoreData"
                      @pageHandler="pageHandler"
                      @addBtnHandler="favoriteFoodsHandler"
                      @selectBtnHandler="selectBtnHandler"
                    />
                  </div>
                </v-tab-item>
              </v-tabs-items>
            </div>
            <!-- v-if="loaded" -->
            <SearchResultItem
              :searchResult="searchResult"
              :isSearchedItem="searchResult.length !== 0"
              :isSearched="isSearched"
              @pageHandler="searchPageHandler"
              @searchItemHandler="searchItemHandler"
              @addBtnHandler="resultSelectBtnHandler"
              @selectBtnHandler="searchResultListHandler"
            />
          </div>
        </div>
      </div>
    </background>
    <Loading v-if="loading" />
  </div>
</template>

<script>
import SearchResultItem from "@/components/Solution/SearchResultItem.vue";
import Background from "@/components/Common/Background.vue";
import Header from "@/components/History/Header.vue";
import Loading from "@/components/Common/Loading.vue";

import API from "@/api/solution/index.js";

export default {
  components: {
    Background,
    SearchResultItem,
    Header,
    Loading,
  },
  data() {
    return {
      loading: false,
      loaded: false,
      isSearched: false,
      searchInput: "",
      focusInput: false,
      searchResult: [],
      bookmarkList: [],
      recentSearch: [],
      selectedItems: [],
      selectedRecentFoods: [],
      changedAmountItems: [],
      changedAmountRecentFoods: [],
      tabName: "tab-recent",
      currentTabIndex: "",
      subjectId: localStorage.getItem("subjectId"),
      page: 1,
      noMoreData: false,
      searchListPage: 1,
      noMoreSearchData: false,
      lang: "",
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
  computed: {
    isSelectedFood() {
      return this.selectedItems.length !== 0;
    },
  },
  methods: {
    focusInHandler(e) {
      // console.log(e);
      e.isTrusted ? (this.focusInput = true) : null;
    },
    focusOutHandler(e) {
      // console.log(e);
      e.isTrusted ? (this.focusInput = false) : null;
    },
    searchBtnHandler() {
      // console.log(this.searchInput);
      this.searchResult = [];
      this.searchListPage = 1;
      this.noMoreSearchData = false;
      this.getSearchList();
    },
    searchPageHandler(fromChild) {
      // console.log(fromChild);
      if (fromChild) this.searchListPage += 1;
      if (!this.noMoreSearchData) this.getSearchList();
    },
    async getSearchList() {
      this.loading = true;
      this.loaded = false;
      const page = `&page=${this.searchListPage}`;
      try {
        const searchQuery = `?keyword=${this.searchInput}${page}`;
        // console.log(searchQuery);
        const { data, status, config } = await API.GetSearchFood(this.subjectId, searchQuery);
        // console.log(data, status, config.url);
        if (data.length === 0) this.noMoreSearchData = true;
        data.map((foodData) => this.searchResult.push(foodData));
        // this.searchResult = data;
        this.isSearched = true;
        this.loading = false;
        this.loaded = true;
      } catch (error) {
        console.error(error);
        this.loading = false;
        this.loaded = true;
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    changeTabs(tabIndex) {
      this.currentTabIndex = tabIndex;
    },
    changeAmountsFoodsHandler(fromChild) {
      // console.log(fromChild);
      this.changedAmountRecentFoods = fromChild;
    },
    favoriteFoodsHandler(fromChild) {
      // console.log(fromChild);
      this.changedAmountItems = fromChild;
    },
    resultSelectBtnHandler() {
      const filteredArr = [];
      this.searchResult
        .filter((i) => this.selectedItems.includes(i.id))
        .map((food) => {
          // this.changedAmountItems.length !== 0
          //   ? filteredArr.push({ foodId: food.id, gram: food.newAmount })
          //   : filteredArr.push({ foodId: food.id, gram: food.amount });
          filteredArr.push({ foodId: food.id, gram: food.amount });
        });
    },
    recentFoodAddHandler() {
      const filteredArr = [];
      if (this.changedAmountRecentFoods.length !== 0) {
        this.recentSearch
          .filter((i) => this.selectedRecentFoods.includes(i.id))
          .map((food) => {
            filteredArr.push({ foodId: food.id, gram: food.newAmount });
          });
      } else {
        this.recentSearch
          .filter((i) => this.selectedRecentFoods.includes(i.id))
          .map((food) => {
            filteredArr.push({ foodId: food.id, gram: food.amount });
          });
      }
      // console.log(filteredArr);
      this.postRecord(filteredArr);
    },
    addBtnHandler() {
      const filteredArr = [];
      if (this.changedAmountItems.length !== 0) {
        this.bookmarkList
          .filter((i) => this.selectedItems.includes(i.id))
          .map((food) => {
            filteredArr.push({ foodId: food.id, gram: food.newAmount });
          });
      } else {
        this.bookmarkList
          .filter((i) => this.selectedItems.includes(i.id))
          .map((food) => {
            filteredArr.push({ foodId: food.id, gram: food.amount });
          });
      }
      // console.log(filteredArr);
      this.postRecord(filteredArr);
    },
    removeDuplicates(array, property) {
      return array.filter((item, index, self) => index === self.findIndex((obj) => obj[property] === item[property]));
    },
    async postRecord(arr) {
      const subjectId = localStorage.getItem("subjectId");
      const type = this.$route.query.type;
      const food = { records: arr };
      try {
        const { data, status } = await API.PostFoodRecord(subjectId, type, food);
        // console.log(status, data);
        const filteredItems = this.searchResult.filter((item) => {
          return arr.some((arrItem) => arrItem.foodId === item.id);
        });
        const recentFoods = this.bookmarkList.filter((i) => this.selectedItems.includes(i.id));
        const savedFoods = JSON.parse(localStorage.getItem("recentFoods"));
        const sumArr = savedFoods !== null ? [...savedFoods, ...recentFoods, ...filteredItems] : recentFoods;
        const uniqueArr = this.removeDuplicates(sumArr, "id");
        if (status === 201) {
          localStorage.setItem("recentFoods", JSON.stringify(uniqueArr));
          this.$router.push("/solution?tab=2");
        }
      } catch (error) {
        console.error(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    searchResultListHandler(foodId) {
      if (this.selectedItems.includes(foodId)) {
        const index = this.selectedItems.indexOf(foodId);
        this.selectedItems.splice(index, 1);
      } else {
        this.selectedItems.push(foodId);
      }
      // console.log(this.selectedItems);
    },
    searchItemHandler(addedBody) {
      // console.log(addedBody);
      this.postRecord(addedBody);
    },

    selectedRecentFoodsHandler(foodId) {
      this.selectedRecentFoods = foodId;
    },

    selectBtnHandler(foodId) {
      this.selectedItems = foodId;
    },

    pageHandler(fromChild) {
      if (fromChild) this.page += 1;
      if (!this.noMoreData) this.getBookmarkList();
    },
    async getBookmarkList() {
      this.loaded = false;
      this.loading = true;
      try {
        const { config, data } = await API.GetBookMark(this.subjectId, this.page);
        // console.log(config.url, data);
        if (data.length === 0) this.noMoreData = true;
        data.map((foodData) => this.bookmarkList.push(foodData.food));
        this.loading = false;
        this.loaded = true;
      } catch (error) {
        this.loading = false;
        console.error(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
  },
  watch: {
    searchInput(newVal) {
      // console.log(newVal);
    },
  },
  mounted() {
    this.getBookmarkList();
    const recentFoods = JSON.parse(localStorage.getItem("recentFoods"));
    this.recentSearch = recentFoods;
    this.lang = this.$i18n.locale === "ko" ? "ko" : "en";
    // console.log(this.recentSearch);
  },
};
</script>

<style lang="scss" scoped>
.bg-white {
  background-color: #fff;
  height: calc(100vh - 200px);
  overflow: hidden;
  padding: 0 20px;
}

.input-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
}

.search-input {
  width: 80%;
  height: 40px;
  background: #ededed;
  border-radius: 10px;
  text-align: left;
  padding: 0px 20px;
  position: relative;
  top: 0;
  line-height: 40px;
  border: 2px solid transparent;

  img {
    width: 24px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }

  input {
    margin-left: 30px;
    outline: none;
    position: absolute;
    top: -2px;
    &:placeholder {
      color: #a7a7a7 !important;
    }
  }
  &:hover {
    border: 2px solid #41d8e6;
    background-color: #f8f8f8;
    // padding: 2px 22px;
  }
}

.search-btn {
  width: 20%;
  height: 40px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  color: #41d8e6 !important;
  text-align: right !important;
  letter-spacing: -0.03em;
  font-weight: 500;
  font-size: 20px !important;
  z-index: 999;
}

.search-result {
  padding: 30px 0px 100px 0px;
}
.search-result__header {
  display: flex;
  width: 100%;
  padding-left: 10px;
  justify-content: space-between !important;
  letter-spacing: -0.03em !important;
}

.tab__wrapper {
  width: 60%;
}

.header__title {
  font-weight: bold;
}

.header__addBtn {
  display: flex;
  height: 40px;
  align-items: center;
}
.add-btn {
  border: 1px solid #ededed !important;
  padding: 0 15px;
  height: 25px !important;
  min-width: 90px !important;
  border-radius: 5px !important;
  color: #858585 !important;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: -0.03em;
  white-space: nowrap;
}

::v-deep .v-slide-group__content {
  display: flex;
  justify-content: flex-start !important;
  left: 0;
  letter-spacing: -0.03em !important;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #ffffff !important;
  border: 1px solid #ededed !important;
  padding: 0 10px !important;
}
::v-deep .theme--light.v-btn.v-btn--disabled {
  color: #858585 !important;
}
::v-deep .theme--light.v-btn {
  color: #ffffff !important;
  border: 1px solid #41d8e6 !important;
}

.history-header {
  padding: 10px 30px;
}

.history-header_title {
  // font-family: GilroyBold;
  font-size: 30px;
  text-align: left;
  font-weight: bold;
  padding-top: 10px;
  color: #000000;
}

.history-header_nav {
  display: flex;
  justify-content: flex-start;
  padding-top: 55px;
}
.history-header_nav-android {
  display: flex;
  justify-content: flex-start;
  padding-top: 20px;
}

.result-list {
  height: calc(100vh - 300px);
  // overflow: auto;
}

::v-deep .v-ripple__container {
  display: none !important;
}

::v-deep .v-input--selection-controls__ripple {
  display: none !important;
}

::v-deep .v-tab:before {
  display: none !important;
}

::v-deep .theme--light.v-tabs-items {
  background-color: transparent !important;
}

::v-deep .v-tabs--grow > .v-tabs-bar .v-tab {
  flex: 1 0 0;
}

::v-deep .v-tabs-slider-wrapper {
  color: #41d8e6;
  height: 3px !important;
  // width: 80px !important;
  // width: 100px;
}

::v-deep .v-tabs-slider {
  border-radius: 3px 3px 0px 0px !important;
}

::v-deep .v-tab {
  color: rgba(0, 0, 0, 0.3) !important;
  font-weight: 500 !important;
  text-transform: none !important;
  font-size: 16px !important;
  letter-spacing: -0.03em !important;
  width: 100px;
}
::v-deep .v-tab--active {
  font-size: 16px !important;
  letter-spacing: -0.03em !important;
  color: rgba(0, 0, 0, 0.87) !important;
}

.v-tab--active {
  letter-spacing: -0.03em !important;
}

::v-deep .v-tabs-bar__content {
  background: transparent !important;
  letter-spacing: -0.03em !important;
}

::v-deep .theme--light.v-tabs-items {
  background-color: transparent !important;
}

::v-deep .theme--light.v-tabs > .v-tabs-bar {
  background-color: transparent !important;
}

::v-deep .v-toolbar__content {
  height: auto !important;
}

::v-deep .v-tab {
  // min-width: 60px !important;
  // width: 60px !important;
  // background-color: transparent !important;
  // justify-content: flex-start;
  // padding-left: 10px;
}
</style>
