<template>
  <div>
    <background>
      <div>
        <Header />
        <div class="history-header">
          <div class="history-header__wrapper">
            <div :class="isIos ? 'history-header_nav' : 'history-header_nav-android'">
              <router-link to="/solution?tab=2"><v-icon>$back_btn_bold</v-icon></router-link>
            </div>
            <div class="title__wrapper">
              <div v-if="isKo" class="history-header_title">{{ koName }}</div>
              <div v-else class="history-header_title">{{ enName }}</div>
              <div class="title-createdAt">{{ createdAt }}</div>
            </div>
          </div>
        </div>
        <div class="dp-30 bg-white">
          <div class="food-info-box">
            <div class="info-box__header">
              <div class="header__title">{{ $t("edit_per_serving") }}</div>
              <div class="header__btns">
                <button class="edit-button" @click="deleteBtnHandler">{{ $t("delete_btn") }}</button>
                <button class="edit-button" @click="saveBtnHandler">{{ $t("save") }}</button>
              </div>
            </div>
          </div>
          <div class="food-amount__input">
            <!-- <div class="amount">{{ foodAmount }}</div> -->
            <div>
              <v-text-field
                v-model="foodAmount"
                type="number"
                inputmode="numeric"
                pattern="[0-9]*"
                color="#41d8e6"
                outlined
                @blur="foodAmountHandler"
              >
              </v-text-field>
            </div>
            <div class="gram">g</div>

            <div class="food-kcal__title">
              <div class="food-info__title">{{ $t("calorie") }}</div>
              <div>
                <div class="food-info__item">
                  <div class="item__title">{{ sumKcal }}</div>
                  <div class="item__title">kcal</div>
                </div>
              </div>
            </div>
            <div class="food-infomation">
              <div class="food-info__title">{{ $t("nutrition_info") }}</div>
              <div>
                <div class="food-info__item">
                  <div>{{ $t("carbohydrate") }}</div>
                  <div class="item__amount">{{ sumCarb }} g</div>
                </div>
                <div class="food-info__item">
                  <div>{{ $t("protein_ingredient") }}</div>
                  <div class="item__amount">{{ sumPro }} g</div>
                </div>
                <div class="food-info__item">
                  <div>{{ $t("fat") }}</div>
                  <div class="item__amount">{{ sumFat }} g</div>
                </div>
                <div class="food-info__item">
                  <div>{{ $t("sodium") }}</div>
                  <div class="item__amount">{{ sumSodium }} mg</div>
                </div>
                <div class="food-info__item">
                  <div>{{ $t("sugar") }}</div>
                  <div class="item__amount">{{ sumSugar }} g</div>
                </div>
                <!-- <div class="food-info__item" v-for="(item, idx) in foodInfo" :key="idx">
                  <div>{{ item.name }}</div>
                  <div class="item__amount">{{ item.value }}</div>
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </background>
  </div>
</template>

<script>
import Background from "@/components/Common/Background.vue";
import API from "@/api/solution/index.js";

export default {
  components: {
    Background,
  },
  data() {
    return {
      isKo: true,
      foodAmount: 0,
      originAmout: 0,
      foodKcal: 0,
      sumKcal: 0,
      carb: 0,
      sumCarb: 0,
      protein: 0,
      sumPro: 0,
      fat: 0,
      sumFat: 0,
      sodium: 0,
      sumSodium: 0,
      sugar: 0,
      sumSugar: 0,
      koName: "",
      enName: "",
      createdAt: "",
      foodInfo: [],
      food: [],
      subjectId: localStorage.getItem("subjectId"),
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
  watch: {
    foodAmount(newVal) {
      // console.log(newVal);
      const ratio = (newVal / this.originAmout).toFixed(1);
      // console.log(ratio);
      const newKcal = this.foodKcal * ratio;
      this.sumKcal = newKcal.toFixed(1);

      const sumCarb = this.carb * ratio;
      this.sumCarb = sumCarb.toFixed(1);
      const sumPro = this.protein * ratio;
      this.sumPro = sumPro.toFixed(1);
      const sumFat = this.fat * ratio;
      this.sumFat = sumFat.toFixed(1);
      const sumSodium = this.sodium * ratio;
      this.sumSodium = sumSodium.toFixed(1);
      const sumSugar = this.sugar * ratio;
      this.sumSugar = sumSugar.toFixed(1);
    },
  },
  methods: {
    async updateRecordInfo() {
      const type = this.$route.query.type;
      const selected = this.$store.state.selectedFood;
      const foodData = [{ recordId: selected.id, gram: Number(this.foodAmount) }];
      // console.log(foodData);
      try {
        const { data, status } = await API.PatchFoodRecord(type, this.subjectId, { records: foodData });
        // console.log(data, status);
        if (status === 200) {
          this.$router.push({ path: "/solution?tab=2" });
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    async deleteRecord() {
      const type = this.$route.query.type;
      const selected = this.$store.state.selectedFood;
      const body = { records: [selected.id] };
      try {
        const { status } = await API.DeleteFoodRecord(type, this.subjectId, body);
        if (status === 204) {
          this.$router.push({ path: "/solution?tab=2" });
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    getFoodData() {
      const id = Number(this.$route.params.id);
      const selected = this.$store.state.selectedFood;
      // console.log(selected);
      this.koName = selected.food.korean;
      this.enName = selected.food.english;
      this.createdAt = selected.createdAt;
      this.foodAmount = Number(selected.gram);
      this.originAmout = selected.food.amount;
      this.foodKcal = selected.food.calorie;
      this.carb = selected.food.carb;
      this.sumCarb = selected.food.sucarb;
      this.protein = selected.food.protein;
      this.sumPro = selected.food.protein;
      this.fat = selected.food.fat;
      this.sumFat = selected.food.fat;
      this.sodium = selected.food.sodium;
      this.sumSodium = selected.food.sodium;
      this.sugar = selected.food.sugar;
      this.sumSugar = selected.food.sugar;
      const info = [
        { name: this.$i18n.t("carbohydrate"), value: `${this.carb} g` },
        { name: this.$i18n.t("protein_ingredient"), value: `${this.protein} g` },
        { name: this.$i18n.t("fat"), value: `${this.fat} g` },
        { name: this.$i18n.t("sodium"), value: `${this.sodium} mg` },
        { name: this.$i18n.t("sugar"), value: `${this.sugar} g` },
      ];
      this.foodInfo = info;
    },
    foodAmountHandler(e) {
      const type = this.$route.query.type;
      const id = Number(this.$route.params.id);
      // console.log(e.target.value);
    },
    deleteBtnHandler() {
      this.deleteRecord();
    },
    saveBtnHandler() {
      this.updateRecordInfo();
    },
  },
  mounted() {
    this.isKo = this.$i18n.locale === "ko";
    this.getFoodData();
  },
};
</script>

<style lang="scss" scoped>
.bg-white {
  background: #fff;
  height: calc(100vh - 200px);
}

.history-header {
  padding: 10px 30px 25px;
}

.title__wrapper {
  width: 100%;
  display: flex;
  align-items: flex-end;
  height: 55px;
  gap: 10px;
}

.history-header_title {
  font-size: 30px;
  text-align: left;
  font-weight: 700;
  color: #000000;
  line-height: 30px;
  // width: 100%;
  // width: 60%;
}

.title-createdAt {
  font-family: GilroyMedium;
  color: #646464;
  font-size: 14px;
  line-height: 14px;
  // width: 20%;
}

.history-header_nav {
  display: flex;
  justify-content: flex-start;
  padding-top: 55px;
}
.history-header_nav-android {
  display: flex;
  justify-content: flex-start;
  padding-top: 20px;
}
.food-info-edit__title {
  font-weight: 700;
  font-size: 28px;
  text-align: left;
  padding-top: 20px;
}
.info-box__header {
  display: flex;
  justify-content: space-between;
  padding: 30px 0 20px 0;
}

.header__title {
  font-family: Noto Sans KR;
  letter-spacing: -0.03em;
  color: #646464;
}

.header__btns {
  display: flex;
  gap: 10px;
}

.edit-button {
  background: #c8c8c8;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  color: #fff;
  padding: 0 10px;
  white-space: nowrap;
  font-size: 18px;
  font-weight: 700;
  &:active {
    background: #41d8e6;
    color: #fff;
  }
}
.food-amount__input {
  background-color: #ededed;
  width: 100%;
  height: 50px;
  border-radius: 10px;
}

.gram {
  position: fixed;
  top: 263px;
  right: 55px;
  padding-bottom: 10px;
  color: #646464;
  font-size: 20px;
  font-family: GilroyMedium;
}

.food-infomation {
  margin: 20px 0;
}

.food-kcal__title {
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  padding-top: 10px;
}

.food-info__title {
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  border-bottom: 1px solid #a7a7a7;
  padding: 5px 0px;
}

.food-info__item {
  display: flex;
  justify-content: space-between;
  border-bottom: 0.5px solid #a7a7a7;
  padding: 5px 10px;
  font-size: 16px;
  color: #646464;
}

.food-info__item:last-child {
  border: none;
}

.item__title {
  font-family: GilroyMedium;
  font-size: 20px;
}
.item__amount {
  font-family: GilroyMedium;
  font-size: 18px;
}

::v-deep .v-text-field.v-text-field--enclosed:not(.v-text-field--rounded) > .v-input__control > .v-input__slot,
.v-text-field.v-text-field--enclosed .v-text-field__details {
  padding: 0 20px !important;
}

::v-deep {
  .v-text-field--filled > .v-input__control > .v-input__slot,
  .v-text-field--full-width > .v-input__control > .v-input__slot,
  .v-text-field--outlined > .v-input__control > .v-input__slot {
    min-height: 50px !important;
  }
}

::v-deep .v-text-field--outlined fieldset {
  border-color: transparent;
  border-radius: 10px;
}

::v-deep .v-input__slot {
  margin: 0 !important;
  height: 50px;
  padding: 20px;
  font-family: GilroyMedium;
  font-size: 24px;
}

.v-text-field .v-text-field--solo .v-input__control input {
  caret-color: #41d8e6 !important;
}
</style>
