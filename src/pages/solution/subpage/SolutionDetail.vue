<template>
  <div>
    <background>
      <!-- header section -->
      <div class="history-header">
        <div class="history-header__wrapper">
          <div :class="isIos ? 'history-header_nav' : 'history-header_nav-android'">
            <router-link to="/solution"><v-icon>$back_btn_bold</v-icon></router-link>
          </div>
          <div class="history-header_title" :class="isKo ? '' : 'en-title'">
            <span v-if="isKo">{{ koName }}</span>
            <span v-else>{{ enName }}</span>
            <img src="@/assets/images_assets/icons/bookmark-y-ic.png" v-if="bookmarkBtn" @click="bookmarkBtnHandler" />
            <img src="@/assets/images_assets/icons/bookmark-n-ic.png" v-else @click="bookmarkBtnHandler" />
          </div>
        </div>
      </div>

      <!-- image section -->
      <div class="food-image">
        <img :src="foodImg" />
      </div>
      <div class="food-contents__wrapper">
        <!-- tags section -->
        <div class="food-tag__container">
          <!-- <button class="food-tag">고단백 🍗</button>
          <button class="food-tag">저탄수 🥥</button>
          <button class="food-tag">페스코 🐟</button> -->
          <button class="food-tag">🔥 {{ calorie }}Kcal</button>
          <button class="food-tag" v-if="isKo">{{ $t("per_serving") }} {{ amount }}g</button>
          <button class="food-tag" v-else>{{ amount }}g {{ $t("per_serving") }}</button>
        </div>

        <!-- food infomation section -->

        <div class="food-infomation">
          <div class="food-info__title">{{ $t("nutrition_info") }}</div>
          <div class="food-info__items">
            <div class="food-info__item" v-for="(item, idx) in foodInfo" :key="idx">
              <div class="item__title">{{ item.name }}</div>
              <div class="item__amount">{{ item.value }}</div>
            </div>
          </div>
        </div>
        <div class="food-infomation">
          <div class="food-info__title">{{ $t("main_ingredient") }}</div>
          <div class="food-ingredient__items">
            <span class="food-ingredient__item">{{ foodIngredient }}</span>
          </div>
        </div>
      </div>
      <div class="snackbar">
        <v-snackbar v-model="successMsgOpen" timeout="2000">{{ saveContent }}</v-snackbar>
        <v-snackbar v-model="deleteMsgOpen" timeout="2000">{{ deleteContent }}</v-snackbar>
      </div>
    </background>
  </div>
</template>

<script>
import Background from "@/components/Common/Background.vue";
import API from "@/api/solution/index.js";

import EventBus from "@/event-bus.js";

export default {
  components: {
    Background,
  },
  data() {
    return {
      foodId: 0,
      koName: "",
      enName: "",
      foodImg: `https://food-cym-s3.s3.ap-northeast-2.amazonaws.com/cym/${this.enName}.jpg`,
      foodInfo: [],
      foodIngredient: "",
      amount: "",
      calorie: "",
      bookmarkBtn: false,
      isKo: true,
      bookmarkList: [],
      successMsgOpen: false,
      deleteMsgOpen: false,
      saveContent: this.$i18n.t("complete_success"),
      deleteContent: this.$i18n.t("complete_delete"),
      successBookmarkId: 0,
      subjectId: localStorage.getItem("subjectId"),
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
  methods: {
    getFoodData() {
      const id = Number(this.$route.params.id);
      const selected = this.$store.state.selectedFood;
      // console.log(selected);
      this.foodId = this.$store.state.selectedFoodId;
      // console.log(this.foodId);
      this.koName = selected.korean;
      this.enName = selected.english;
      this.foodImg = `https://food-cym-s3.s3.ap-northeast-2.amazonaws.com/cym/${selected.english}.jpg`;
      const info = [
        { name: this.$i18n.t("carbohydrate"), value: `${selected.carb} g` },
        { name: this.$i18n.t("protein_ingredient"), value: `${selected.protein} g` },
        { name: this.$i18n.t("fat"), value: `${selected.fat} g` },
        { name: this.$i18n.t("sodium"), value: `${selected.sodium} mg` },
        { name: this.$i18n.t("sugar"), value: `${selected.sugar} g` },
      ];
      this.foodInfo = info;
      this.foodIngredient = selected.ingredient;
      this.calorie = selected.calorie;
      this.amount = selected.amount;
      const storedBookmark = JSON.parse(localStorage.getItem("bookmarkList"));
      // console.log(storedBookmark);
      const isBookmark = sessionStorage.getItem("solutionCurTab");
      // console.log(isBookmark === "bookmark-meals");
      this.bookmarkBtn = isBookmark === "bookmark-meals" ? true : selected.foodBookmark[0] !== undefined;
    },
    async bookmarkBtnHandler() {
      // 북마크 삭제
      if (this.bookmarkBtn) {
        const selected = this.$store.state.selectedFood;
        // console.log(Number(this.$route.query.savedId));
        // console.log(this.successBookmarkId, this.$route.query.savedId, selected.foodBookmark[0].id);
        // page가 추천음식이면 successBookmarkId 가 default인 0
        // 추천음식에서 북마크 삭제하려는 경우 id를 정확히 못 찾고 있음
        const bookmarkId =
          this.successBookmarkId !== 0
            ? this.successBookmarkId
            : this.$route.query.savedId !== undefined
            ? Number(this.$route.query.savedId)
            : selected?.foodBookmark[0]?.id;
        const bookmarkIdObj = { bookmarkIds: [bookmarkId] };
        // console.log(bookmarkIdObj);
        this.bookmarkBtn = false;
        try {
          const { data, status } = await API.DeleteBookMark(this.subjectId, bookmarkIdObj);
          // console.log(data, status);
          if (status === 204) {
            this.deleteMsgOpen = true;
            this.savedListHandler(false, 0);
            EventBus.$emit("favoriteAdded");
          }
        } catch (error) {
          console.log(error);
          if (error.response.status === 429) {
            this.$store.commit("setoverReqModal", true);
          }
        }
        // const newBookmarkList = this.bookmarkList.filter((cur) => cur !== this.foodId);
        // console.log(newBookmarkList);
        // localStorage.setItem("bookmarkList", JSON.stringify(newBookmarkList));
      } else {
        // 북마크 추가
        this.bookmarkBtn = true;
        const selected = this.$store.state.selectedFood;
        const selectedFood = { foodIds: [selected.id] };
        // console.log(selectedFood);
        try {
          const { data, status } = await API.PostBookMark(this.subjectId, selectedFood);
          // console.log(data, status);
          if (status === 201) {
            this.successMsgOpen = true;
            this.successBookmarkId = data.successBookmarkIds[0];
            sessionStorage.setItem("reloadBookmark", true);
            EventBus.$emit("favoriteAdded");
            this.savedListHandler(true, data.successBookmarkIds[0]);
          }
        } catch (e) {
          console.log(e);
        }
        // const newBookmarkList = [...this.bookmarkList, this.foodId];
        // console.log(newBookmarkList);
        // localStorage.setItem("bookmarkList", JSON.stringify(newBookmarkList));
        // this.$store.commit("addToBookmarkList", [this.foodId]);
      }
    },
    savedListHandler(isBookmark, bookmarkId) {
      // console.log(isBookmark, bookmarkId);
      const isBookmarkPage = sessionStorage.getItem("solutionCurTab") === "bookmark-meals";
      if (isBookmarkPage) {
        // if (isBookmark) {
        let savedList = JSON.parse(sessionStorage.getItem("savedBookmarkList"));
        // console.log(isBookmarkPage);
        // console.log(this.foodId);
        savedList = savedList.filter((food) => food.id !== this.foodId);
        // console.log(savedList);
        sessionStorage.setItem("savedBookmarkList", JSON.stringify(savedList));
        // } else
      } else {
        const savedList = JSON.parse(sessionStorage.getItem("savedFoodList"));
        // 선택한 음식의 인덱스 찾기
        let selectedFoodIndex = savedList.findIndex((food) => food.id === this.foodId);
        // 선택한 음식의 북마크 변경
        savedList[selectedFoodIndex].foodBookmark = isBookmark ? [{ id: bookmarkId }] : [];
        // 변경된 음식 리스트 세션스토리지에 저장
        sessionStorage.setItem("savedFoodList", JSON.stringify(savedList));
      }
    },
  },
  mounted() {
    this.isKo = this.$i18n.locale === "ko";
    // console.log(this.$route.query.savedId);
    this.getFoodData();
  },
};
</script>

<style lang="scss" scoped>
.history-header {
  padding: 10px 30px 10px 30px;
}

.history-header_title {
  font-weight: 700;
  font-size: 30px;
  text-align: left;
  display: flex;
  padding-top: 14px;
  color: #000000;
  gap: 10px;
  align-items: center;
  img {
    width: 20px;
    padding-top: 5px;
    object-fit: contain;
  }
}

.history-header_nav {
  display: flex;
  justify-content: flex-start;
  padding-top: 55px;
}
.history-header_nav-android {
  display: flex;
  justify-content: flex-start;
  padding-top: 20px;
}

.food-image {
  width: 100%;
  height: 26vh;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.food-contents__wrapper {
  padding: 20px 30px 0 30px;
}

.food-tag__container {
  text-align: left;
  margin-bottom: 20px;
}
.food-tag {
  background-color: #ededed;
  border-radius: 20px;
  padding: 2px 10px;
  font-size: 16px;
  margin: 0 10px 8px 0px;
  letter-spacing: -0.03em;
  font-weight: 400;
  font-style: normal;
}

.food-infomation {
  margin: 20px 0;
}

.food-info__title {
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  border-bottom: 1px solid #a7a7a7;
  padding: 5px 0px;
}

.food-info__item {
  display: flex;
  justify-content: space-between;
  border-bottom: 0.5px solid #a7a7a7;
  padding: 5px 0px;
  font-size: 16px;
  color: #646464;
}

.food-info__item:last-child {
  border: none;
}

.food-ingredient__items {
  text-align: left;
}

.food-ingredient__item {
  font-size: 14px;
  color: #646464;
}
</style>
