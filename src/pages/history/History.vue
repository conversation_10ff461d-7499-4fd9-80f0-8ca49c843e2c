<template>
  <div class="history__wrapper">
    <div v-if="showKetoneGuide && tabName === 'tab-ketone'" class="overlay"></div>
    <div class="bg-modal" v-if="showKetoneGuide && tabName === 'tab-ketone'">
      <div
        :class="isIos ? 'close-icon__wrapper' : 'close-icon__wrapper-android'"
        @click="cancelHandler"
      >
        <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
      </div>
      <div :class="isIos ? 'arrow-icon__wrapper' : 'arrow-icon__wrapper-android'">
        <img src="@/assets/images/guide_img/arrow-up.png" />
      </div>
      <div class="guide-contents__wrapper">
        <div v-if="isKetoneMode">
          <div
            :class="isIos ? 'guide-contents' : 'guide-contents-android'"
            v-html="this.$i18n.t('ketosis_guide_desc')"
          ></div>
          <div :class="isIos ? 'guide-desc__wrapper' : 'guide-desc__wrapper-android'">
            <img :src="ketosisImg" alt="ketosis guide img" />
          </div>
        </div>

        <div v-else>
          <div
            :class="isIos ? 'guide-contents' : 'guide-contents-android'"
            v-html="this.$i18n.t('diabetes_guide_desc')"
          ></div>
          <div :class="isIos ? 'guide-desc__wrapper' : 'guide-desc__wrapper-android'">
            <img :src="diabetesImg" alt="ketone guide img" />
          </div>
        </div>

        <div class="ketoneMode-btn__wrapper">
          <div @click="ketoneModeModalHandler" class="ketoneMode-btn">
            {{ $t("set_ketone_mode") }}
          </div>
        </div>
      </div>
    </div>

    <Loading v-if="loading" />
    <background v-if="loaded">
      <!-- TODO: 🔖 header component -->
      <div>
        <Header />
        <div class="history-header">
          <div class="history-header__wrapper">
            <div :class="isIos ? 'history-header_nav' : 'history-header_nav-android'">
              <router-link to="/home"><v-icon>$back_btn_bold</v-icon></router-link>
            </div>
            <div class="history-header_title">History</div>
          </div>
        </div>
        <div class="tab-container">
          <v-tabs
            v-model="tabName"
            color="#C9F4F8"
            class="history-tabs"
            center-active
            grow
            @change="changeTabs"
            mobile-breakpoint="xs"
            slider-color="#41D8E6"
            slider-size="5"
            height="40px"
          >
            <v-tab class="px-0 mx-0" href="#tab-cym702">{{ $t("tab_cym") }}</v-tab>
            <v-tab class="px-0 mx-0" href="#tab-blood">{{ $t("tab_blood") }}</v-tab>
            <v-tab id="ph-text" class="px-0 mx-0" href="#tab-ph">{{ $t("tab_ph") }}</v-tab>
            <v-tab class="px-0 mx-0" href="#tab-protein">{{ $t("tab_protein") }}</v-tab>
            <v-tab class="px-0 mx-0" href="#tab-glucose">{{ $t("tab_glucose") }}</v-tab>
            <v-tab class="px-0 mx-0" href="#tab-ketone">{{ $t("tab_ketone") }}</v-tab>
          </v-tabs>
        </div>
      </div>

      <v-tabs-items v-model="tabName" color="transparent" :touchless="true">
        <v-tab-item value="tab-cym702" class="tab-item">
          <Cym702
            :historyData="cym"
            :totalCount="totalCount"
            :avgScore="avgScore"
            :startDate="startDate"
            :endDate="endDate"
            @pageHandler="pageHandler"
            @limitHandler="limitHandler"
            v-if="loaded"
          />
          <!-- @changeCount="changeCount" -->
          <!-- :totalPage="totalPage" -->
        </v-tab-item>
        <v-tab-item value="tab-blood" class="tab-item">
          <UrineTestItemView type="blood" :historyData="blood" v-if="loaded" />
        </v-tab-item>
        <v-tab-item value="tab-ph" class="tab-item">
          <UrineTestItemView type="ph" :historyData="ph" v-if="loaded" />
        </v-tab-item>
        <v-tab-item value="tab-protein" class="tab-item">
          <UrineTestItemView type="protein" :historyData="protein" v-if="loaded" />
        </v-tab-item>
        <v-tab-item value="tab-glucose" class="tab-item">
          <UrineTestItemView type="glucose" :historyData="glucose" v-if="loaded" />
        </v-tab-item>
        <v-tab-item value="tab-ketone" class="tab-item">
          <UrineTestItemView type="ketone" :historyData="ketone" v-if="loaded" />
        </v-tab-item>
      </v-tabs-items>
    </background>
    <Navigation :path="path" />
    <CymScore v-if="showGuide" />
    <EditValueModal
      v-if="showEditModal || openKetoneEditModal"
      page="ketone"
      @closeEditModalWindow="closeEditModalWindow"
      @saveBtnHandler="saveBtnHandler"
    />
    <div class="snackbar">
      <v-snackbar v-model="saveSuccess" timeout="2000">{{ succesContent }}</v-snackbar>
      <v-snackbar v-model="saveFail" timeout="2000" color="#EE0000">{{ failContent }}</v-snackbar>
    </div>
  </div>
</template>

<script>
// history components
import Header from "@/components/History/Header.vue";
import Cym702 from "@/components/History/Cym702.vue";
import UrineTestItemView from "@/components/History/UrineTestItemView.vue";
import CymScore from "@/components/History/CymScore.vue";

import Loading from "@/components/Common/Loading.vue";

// common components
import Background from "@/components/Common/Background.vue";
import Navigation from "@/components/Common/Navigation.vue";

import EditValueModal from "@/components/Care/EditWaterValueModal.vue";

// API
import API from "@/api/cym702/index.js";
import { updateSubjectInfo } from "@/api/user/index.js";
import dataProcessing from "@/assets/data/manufacturing/cym.js";
import { getSubjectId } from "@/components/Common/getSubjectId.js";

export default {
  components: {
    Header,
    Background,
    Cym702,
    UrineTestItemView,
    Navigation,
    CymScore,
    EditValueModal,
    Loading,
  },
  data() {
    return {
      currentScrollValue: 0,
      tabName: "tab-cym702",
      path: "/home",
      loading: false,
      loaded: false,
      currentTabIndex: "",
      count: "recent",
      historyData: [],
      cymData: [],
      avgData: [],
      cym: [],
      blood: [],
      glucose: [],
      protein: [],
      ph: [],
      ketone: [],
      totalCount: 0,
      recentCount: 0,
      yearCount: 0,
      avgScore: 0,
      startDate: "",
      endDate: "",
      page: 1,
      limit: 5,
      showKetoneGuide: true,
      isKetoneMode: false,
      showEditModal: false,
      saveSuccess: false,
      succesContent: this.$i18n.t("save_success"),
      saveFail: false,
      failContent: this.$i18n.t("save_fail"),
      diabetesImg: require(`@/assets/images/guide_img/${this.$i18n.t("diabetes_guide_img")}.png`),
      ketosisImg: require(`@/assets/images/guide_img/${this.$i18n.t("ketosis_guide_img")}.png`),
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },

  computed: {
    openKetoneEditModal() {
      return this.$store.state.openKetoneEditModal;
    },
    showGuide() {
      return this.$store.state.showCymscore;
    },
    enableTouchless() {
      if (this.$store.state.isEnableTouch) {
        return true;
      } else {
        return false;
      }
    },
    historyPage() {
      return this.$store.state.historyPage;
    },
    historyTotalPage: {
      get() {
        return this.$store.state.historyTotalPage;
      },
      set(value) {
        this.$store.commit("SET_HISTORY_TOTAL_PAGE", value);
      },
    },
    historyLimit() {
      return this.$store.state.historyLimit;
    },
  },

  watch: {
    tabName(newVal) {
      // console.log("tabName", newVal);
      newVal === "tab-ketone" && this.showKetoneGuide ? this.toggleScroll(true) : null;
    },
    showEditModal(newVal) {
      this.toggleScroll(newVal);
    },
    openKetoneEditModal(newVal) {
      this.toggleScroll(newVal);
    },
    avgScore(newVal) {
      // console.log(newVal);
    },
    historyPage(newVal) {
      // console.log("history page", newVal);
      // console.log("241, this.tabName", this.tabName);
      this.tabName.includes("cym702")
        ? this.fetchCymScoreData(newVal, this.historyLimit)
        : this.reFetchUrinalysisData(this.tabName, newVal, this.historyLimit);
      this.$store.commit("SET_TAB_STATE", true);
    },
    historyLimit(newVal) {
      // console.log("history limit", newVal);
      this.tabName.includes("cym702")
        ? this.fetchCymScoreData(this.historyPage, newVal)
        : this.reFetchUrinalysisData(this.tabName, this.historyPage, newVal);
      this.$store.commit("SET_TAB_STATE", true);
    },
    totalPage() {},
  },

  methods: {
    toggleScroll(state) {
      // console.log(state);
      state
        ? document.body.classList.add("no-scroll")
        : document.body.classList.remove("no-scroll");
    },
    ketoneModeModalHandler() {
      this.showKetoneGuide = false;
      this.showEditModal = true;
    },
    cancelHandler() {
      this.toggleScroll(false);
      this.showKetoneGuide = false;
      localStorage.setItem("ketoneGuide", false);
    },
    closeEditModalWindow() {
      this.showEditModal = false;
      this.$store.commit("openKetoneEditModal", false);
      // console.log(this.$store.state.openKetoneEditModal);
    },
    changeTabs(tabIndex) {
      this.$store.commit("SET_TAB_STATE", true);
      this.$store.commit("setHistoryTabChanged", tabIndex);
      this.currentTabIndex = tabIndex;
      tabIndex.includes("cym702")
        ? this.fetchCymScoreData(this.historyPage, this.historyLimit)
        : this.reFetchUrinalysisData(tabIndex, this.historyPage, this.historyLimit);
    },
    pageHandler(fromChild) {
      // console.log("cym page", fromChild);
    },
    limitHandler(fromChild) {
      // console.log("cym limit", fromChild);
    },

    async fetchCymScoreData(page, limit) {
      // this.loaded = false;
      this.loading = true;
      const subjectId = getSubjectId();
      try {
        const { data, status, config } = await API.GetCymScoreData(subjectId, page, limit);
        // console.log(data, status, config.url);
        if (status === 200) {
          this.cym = dataProcessing.MAKE_CYMSCORE_DATA(data.result);
          this.totalCount = data.result.length;
          this.avgScore = data.avgCymScore;
          const totalPage = Math.ceil(data.count / limit);
          this.$store.commit("SET_HISTORY_TOTAL_PAGE", totalPage);
          // console.log(this.cym);
          this.changeData();
        }
        this.loaded = true;
        this.loading = false;
      } catch (error) {
        console.log(error);
        this.loaded = true;
        this.loading = false;
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    async fetchUrinalysisData(tabIdx) {
      // console.log("here?", tabIdx);
      // this.loaded = false;
      this.loading = true;
      const subjectId = getSubjectId();
      const tab = Number(tabIdx);
      const tabArr = ["cym", "blood", "ph", "protein", "glucose", "ketone"];
      const cymType = tabArr[tab];
      // console.log(cymType);
      try {
        const { data, status, config } = await API.GetUrinalysisData(
          subjectId,
          cymType,
          this.page,
          this.limit
        );
        // console.log(data, status, config.url);
        if (data) {
          this[cymType] = dataProcessing.MAKE_HISTORY_DATA(data.result, cymType);
          const totalPage = Math.ceil(data.count / this.limit);
          this.$store.commit("SET_HISTORY_TOTAL_PAGE", totalPage);
        }
        this.loaded = true;
        this.loading = false;
      } catch (error) {
        console.log(error);
        this.loaded = true;
        this.loading = false;
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    async reFetchUrinalysisData(tabName, page, limit) {
      // console.log("tabname here?", tabName);
      const cymType = tabName.split("-")[1];
      // console.log(cymType);
      // this.loaded = false;
      this.loading = true;
      const subjectId = getSubjectId();
      try {
        const { data, status, config } = await API.GetUrinalysisData(
          subjectId,
          cymType,
          page,
          limit
        );
        // console.log(data, status, config.url);
        if (data) {
          this[cymType] = dataProcessing.MAKE_HISTORY_DATA(data.result, cymType);
          const totalPage = Math.ceil(data.count / limit);
          this.$store.commit("SET_HISTORY_TOTAL_PAGE", totalPage);
        }
        this.loaded = true;
        this.loading = false;
      } catch (error) {
        console.log(error);
        this.loaded = true;
        this.loading = false;
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    initialFetch() {
      // this.loaded = false;
      this.loading = true;
      const tab = this.$route.query.tab;
      // console.log("tab idx", tab);
      tab === undefined
        ? this.fetchCymScoreData(this.historyPage, this.historyLimit)
        : this.fetchUrinalysisData(tab);
    },
    changeData() {
      const len = this.cym.length;
      // console.log(this.cym);
      let [y, m, d] = this.cym[len - 1].createdAt.slice(0, 10).split("-");
      // console.log(typeof y);
      y = String(y).slice(2);
      this.endDate = `${y}.${m}.${d}`;
      let [startYear, startMonth, startDay] = this.cym[0].createdAt.slice(0, 10).split("-");
      startYear = String(startYear).slice(2);
      this.startDate = `${startYear}.${startMonth}.${startDay}`;
    },
    saveBtnHandler(isKetoneMode) {
      // console.log(isKetoneMode);
      this.saveKetoneMode(isKetoneMode);
    },
    async saveKetoneMode(isKetoneMode) {
      // console.log(userData);
      const userData = { ketoneMode: isKetoneMode };
      const subjectId = getSubjectId();
      try {
        const { data, status } = await updateSubjectInfo(subjectId, userData);
        // console.log(data);
        if (status === 200) {
          this.$router.replace({ query: { ...this.$route.query, tab: "5" } });
          localStorage.setItem("ketoneMode", isKetoneMode);
          this.saveSuccess = true;
          this.$router.go();
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    ketoneGuideHandler() {
      this.showKetoneGuide =
        !!JSON.parse(localStorage.getItem("ketoneGuide")) ||
        JSON.parse(localStorage.getItem("ketoneGuide")) === null
          ? true
          : false;
      // this.$store.commit("openKetoneEditModal", true);
      const ketoneMode = JSON.parse(localStorage.getItem("ketoneMode"));
      this.isKetoneMode = ketoneMode;
    },
  },

  created() {
    // window.scrollTo(0, 0);
    let tab = this.$route.query.tab;
    console.log("tab idx", this.$route.query.tab);
    if (tab === "0" || tab === undefined) {
      this.tabName = "tab-cym702";
    } else if (tab === "1") {
      this.tabName = "tab-blood";
    } else if (tab === "2") {
      this.tabName = "tab-ph";
    } else if (tab === "3") {
      this.tabName = "tab-protein";
    } else if (tab === "4") {
      this.tabName = "tab-glucose";
    } else if (tab === "5") {
      this.tabName = "tab-ketone";
    }
  },
  beforeMount() {
    this.loading = true;
  },
  mounted() {
    this.initialFetch();
    this.ketoneGuideHandler();
    this.showKetoneGuide && this.$route.query.tab === "5" ? this.toggleScroll(true) : null;
  },

  beforeDestroy() {
    // console.log("page leaved");
    this.$store.commit("SET_HISTORY_PAGE", 1);
    this.$store.commit("closeGuideModal");
  },
};
</script>

<style lang="scss" scoped>
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 99999;
}

.bg-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 450px;
  height: 100%;
  opacity: 1;
  z-index: 9999998;
}

.close-icon__wrapper {
  z-index: 9999999;
  text-align: left;
  position: absolute;
  top: 65px;
  left: 30px;
  img {
    width: 26px;
  }
}
.close-icon__wrapper-android {
  z-index: 9999999;
  text-align: left;
  position: absolute;
  top: 35px;
  left: 30px;
  img {
    width: 26px;
  }
}

.arrow-icon__wrapper {
  z-index: 9999;
  position: absolute;
  // bottom: 270px;
  top: 59vh;
  right: 30px;
  img {
    width: 18px;
    transform: rotate(180deg);
  }
}
.arrow-icon__wrapper-android {
  z-index: 9999;
  position: absolute;
  // bottom: 270px;
  top: 54vh;
  right: 30px;
  img {
    width: 18px;
    transform: rotate(180deg);
  }
}

.guide-contents__wrapper {
  width: 100%;
}

.guide-contents {
  position: absolute;
  text-align: right;
  // top: 0;
  right: 30px;
  top: 47vh;
  z-index: 9999999;
  color: #ffffff;
  font-size: 18px;
  margin: 20px 0 30px;
}
.guide-contents-android {
  position: absolute;
  text-align: right;
  // top: 0;
  right: 30px;
  top: 44vh;
  z-index: 9999999;
  color: #ffffff;
  font-size: 18px;
  margin: 20px 0 30px;
}

.ketoneMode-btn__wrapper {
  position: absolute;
  // bottom: 30px;
  width: 100%;
  top: 85vh;
  width: calc(100vw - 60px);
  left: 30px;
  z-index: 9999999;
  // padding: 0 30px;
}
.ketoneMode-btn {
  width: 100%;
  height: 50px;
  max-width: 390px !important;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 700;
  font-size: 20px;
  background-color: #41d8e6;
  border-radius: 10px;
  color: #fff;
}

.guide-desc__wrapper {
  width: 100%;
  padding: 0 20px;
  position: absolute;
  top: 65vh;
  left: 0;
  img {
    width: 100%;
    max-width: 390px !important;
  }
}
.guide-desc__wrapper-android {
  width: 100%;
  padding: 0 20px;
  position: absolute;
  top: 60vh;
  left: 0;
  img {
    width: 100%;
    max-width: 390px !important;
  }
}

#ph-text {
  font-family: GilroyMedium;
  letter-spacing: 0 !important;
}

:deep(.v-tabs-slider-wrapper) {
  color: #41d8e6;
  height: 3px !important;
}

:deep(.v-tabs-slider) {
  border-radius: 3px 3px 0px 0px !important;
}

:deep(.v-tab) {
  color: rgba(0, 0, 0, 0.32) !important;
  font-weight: 500 !important;
  line-height: 20px;
  letter-spacing: -0.05em !important;
  text-transform: none !important;
  font-size: 18px !important;
}
:deep(.v-tab--active) {
  font-size: 18px !important;
  font-weight: bold !important;
  color: #000000 !important;
}

:deep(.v-tabs-bar__content) {
  background: transparent !important;
}

:deep(.theme--light.v-tabs-items) {
  background-color: transparent !important;
}

:deep(.theme--light.v-tabs > .v-tabs-bar) {
  background-color: transparent !important;
}

:deep(.v-toolbar__content) {
  height: auto !important;
}

:deep(.v-tab) {
  min-width: 40px !important;
  width: 16.5%;
  background-color: transparent !important;
}

// tab ripple 제거
:deep(.v-ripple__container) {
  display: none !important;
}

:deep(.v-input--selection-controls__ripple) {
  display: none !important;
}

:deep(.v-tab:before) {
  display: none !important;
}

:deep(.theme--light.v-tabs-items) {
  background-color: transparent !important;
}

.history-header {
  padding: 0px 30px;
}

.history-header_title {
  font-family: GilroyBold;
  font-size: 36px;
  text-align: left;
  // font-weight: bold;
  color: #000000;
}

.tab-container {
  width: 100%;
  padding: 10px 3px 0 3px;
}

.history-header_nav {
  display: flex;
  justify-content: flex-start;
  padding-top: 65px;
}
.history-header_nav-android {
  display: flex;
  justify-content: flex-start;
  padding-top: 30px;
}

::v-deep
  .v-tabs:not(.v-tabs--vertical):not(.v-tabs--right)
  > .v-slide-group--is-overflowing.v-tabs-bar--is-mobile:not(.v-slide-group--has-affixes)
  .v-slide-group__prev {
  display: none;
}
</style>
