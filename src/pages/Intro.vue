<template>
  <div>
    <background>
      <div class="container">
        <div class="stepper-wrapper">
          <!-- eslint-disable-next-line vue/valid-v-model -->
          <Slot v-for="(item, idx) in slides" v-bind="item" :key="idx" v-model="visibleSlide" />
        </div>
        <Carousel @next="next">
          <CarouselSlide v-for="(slide, index) in slides" :key="index" :index="index" :visibleSlide="visibleSlide">
            <div class="intro-title" :class="lang === 'ko' ? '' : 'en-title'" v-html="slide.title"></div>
            <div class="intro-subtitle" v-html="slide.subtitle"></div>
            <div class="intro-img__wrapper">
              <img :src="slide.img" alt="" />
            </div>
          </CarouselSlide>
        </Carousel>
      </div>

      <LastButton :title="title" :path="path" :color="color" v-if="showButton" @setLocalStorage="setLocalStorage" />
    </background>
  </div>
</template>

<script>
import LastButton from "@/components/Common/LastButton.vue";
import Slot from "@/components/Common/Slot.vue";
import Carousel from "@/components/Common/Carousel.vue";
import CarouselSlide from "@/components/Common/CarouselSlide.vue";

export default {
  components: { Carousel, CarouselSlide, LastButton, Slot },
  data() {
    return {
      title: this.$i18n.t("confirm"),
      path: "/login",
      color: "#41D8E6",
      showButton: false,
      lang: "ko",
      slides: [
        {
          id: 0,
          title: this.$i18n.t("intro_title_1"),
          subtitle: this.$i18n.t("intro_mid_1"),
          img: "",
        },
        {
          id: 1,
          title: this.$i18n.t("intro_title_2"),
          subtitle: this.$i18n.t("intro_mid_2"),
          img: "",
        },
        {
          id: 2,
          title: this.$i18n.t("intro_title_3"),
          subtitle: this.$i18n.t("intro_mid_3"),
          img: "",
        },
      ],
      visibleSlide: 0,
    };
  },
  computed: {
    slidesLen() {
      return this.slides.length;
    },
    active() {
      return this.visibleSlide === this.slides.id ? "active" : false;
    },
  },
  methods: {
    next() {
      if (this.visibleSlide === 1) this.showButton = true;
      if (this.visibleSlide >= this.slides.length - 1) {
        this.visibleSlide = 2;
      } else {
        this.visibleSlide++;
      }
    },
    setLocalStorage() {
      // console.log("intro checked!");
      localStorage.setItem("introChecked", true);
    },
  },
  mounted() {
    this.lang = this.$i18n.locale === "ko" ? "ko" : "en";
    // console.log(this.$i18n.locale);
    this.slides.map((slide, idx) => {
      slide.img = require(`@/assets/images/intro/intro${idx + 1}_${this.lang}.png`);
    });
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  // height: 100vh;
  // position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
  padding: 10vh 30px 12vh;
}

.stepper-wrapper {
  display: flex;
  margin: 2vh 0;
}

.intro-title {
  font-style: normal;
  font-weight: 600;
  font-size: 26px;
  line-height: 30px;
  text-align: center;
  letter-spacing: -0.03em;
  /* Yellosis gray/5 */
  color: #323232;
}

.intro-subtitle {
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 22px;
  /* or 143% */
  text-align: center;
  letter-spacing: -0.03em;
  color: #000000;
  margin: 2vh 0 1.5vh 0;
}

.intro-img__wrapper {
  width: 100%;
  height: 100%;
  img {
    width: 80%;
  }
}
</style>
