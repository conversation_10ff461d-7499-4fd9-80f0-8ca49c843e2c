<template>
  <div>
    <background>
      <!-- 회원가입 폼 헤더, 제목 영역 -->
      <FormHeader :currentStateIdx="currentStateIdx" :join="false" @moveBack="moveBack" />
      <div class="join-form__wrapper">
        <FormTitle :currentStateIdx="currentStateIdx" :join="false" />
        <FormSubTitle :currentStateIdx="currentStateIdx" :join="false" />
        <SurveyForm
          @nextPhaseHandler="nextPhaseHandler"
          :currentStateIdx="currentStateIdx"
          :questions="questions"
          :checkedData="checkedData"
          v-if="currentStateIdx >= 0 && currentStateIdx < 5 && loaded"
        />
        <MealSurveyForm
          @nextPhaseHandler="nextPhaseHandler"
          :questions="questions"
          :checkedData="checkedData"
          v-if="currentStateIdx === 5 && loaded"
        />
      </div>
      <Loading v-if="loading" />
    </background>
  </div>
</template>

<script>
import Background from "@/components/Common/Background.vue";

import FormHeader from "@/components/Join/FormHeader.vue";
import FormTitle from "@/components/Join/FormTitle.vue";
import FormSubTitle from "@/components/Join/FormSubTitle.vue";
import Loading from "@/components/Common/Loading.vue";

import SurveyForm from "@/components/Forms/SurveyForm.vue";
import MealSurveyForm from "@/components/Forms/MealSurveyForm.vue";

import API from "@/api/survey/index.js";

export default {
  name: "Survey",
  components: {
    Background,
    FormHeader,
    FormTitle,
    FormSubTitle,
    SurveyForm,
    MealSurveyForm,
    Loading,
  },
  data() {
    return {
      currentStateIdx: 1,
      subjectId: localStorage.getItem("subjectId"),
      surveyIdx: 1,
      questions: [],
      loaded: false,
      loading: false,
      checkedData: {},
    };
  },

  methods: {
    nextPhaseHandler(idx) {
      // console.log("from survey form", idx);
      this.currentStateIdx = idx;
    },
    moveBack(idx) {
      this.currentStateIdx = this.currentStateIdx - idx;
      this.getSurveyId();
      this.loaded = false;
      this.loading = true;
    },
    async getSurveyId() {
      try {
        const { status, data } = await API.getSurveyStatus();
        // console.log(status, data);
        if (status === 200) {
          this.surveyIdx = data.surveys[0].id;
          this.$nextTick(() => this.getSurveyData());
        }
      } catch (e) {
        console.log(e);
      }
    },
    async getSurveyData() {
      try {
        const { data, status } = await API.getSurveyData(this.surveyIdx);
        // console.log(data, status);
        if (status === 200) {
          this.questions = data.survey.question;
          // console.log(this.questions);
          this.$nextTick(() => this.getSurveyCheckedData());
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    async getSurveyCheckedData() {
      try {
        // console.log(this.subjectId);
        const query = `subjectId=${this.subjectId}&surveyId=${this.surveyIdx}`;
        const { data, status } = await API.getSurveyCheckedData(query);
        // console.log(data);
        if (status === 200) {
          this.checkedData = data;
          this.loaded = true;
          this.loading = false;
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
  },
  beforeMount() {
    this.loading = true;
  },
  created() {
    this.subjectId = localStorage.getItem("subjectId");
    this.getSurveyId();
  },
  mounted() {
    const status = localStorage.getItem("surveyStatus");
    const surveyIdx = Number(localStorage.getItem("surveyIdx"));
    this.currentStateIdx = status === "survey_ongoing" || status === "survey_ready" ? surveyIdx : 0;
    this.$store.commit("setSurveyModal", false);
    // const lang = this.$route.query.lang;
    // console.log("params:", lang);
    // localStorage.setItem("locale", lang);
  },
};
</script>

<style lang="scss">
.join-form__wrapper {
  width: 100%;
  height: 100%;
  padding-top: 90px;
}
:deep(.v-input) {
  font-family: GilroyMedium !important;
  font-size: 1.125em;
}

.main-large-btn {
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
  margin-top: 5px;
}

.theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}
</style>
