<template>
  <div>
    <background>
      <WithdrawalModal v-show="isWithdrawal" />
      <ErrorModal
        v-show="showErrorModal"
        :error="errorMsg"
        @isClicked="isClicked"
      />
      <NoticeModal
        :content="content"
        v-if="showNoticeModal"
        btnText=""
        @isConfirmed="isConfirmed"
      />
      <!-- 로그인페이지 헤더, 슬로건이미지 영역 -->
      <div :class="isIos ? 'header__wrapper' : 'header__wrapper-android'">
        <div class="text-center main-text" justify="center">
          <img class="slogan" src="@/assets/images/cym702_slogan.png" />
          <!-- 개발용 -->
          <strong v-if="isDevelopment">dev</strong>
        </div>
      </div>

      <!-- 로그인 폼 -->
      <div class="login-form__wrapper">
        <v-form>
          <v-text-field
            v-model="userId"
            :label="this.$i18n.t('input_id')"
            color="#41D8E6"
            type="text"
            required
          >
          </v-text-field>
          <v-text-field
            v-model="password"
            :label="this.$i18n.t('input_pwd')"
            color="#41D8E6"
            :append-icon="showPassword ? '$eye_show' : '$eye_off'"
            :type="showPassword ? 'text' : 'password'"
            @click:append="showPassword = !showPassword"
            required
          ></v-text-field>

          <v-btn
            class="main-large-btn"
            elevation="0"
            color="#41D8E6"
            @click="generalLogin"
          >
            {{ $t("login") }}
          </v-btn>
          <div class="btn__wrapper pt-21">
            <router-link :to="{ name: 'Join', query: { type: type } }">
              <div class="btn-item txt-blackColor">
                <div>{{ $t("join_btn") }}</div>
              </div>
            </router-link>
            <div class="btn-divider">|</div>
            <router-link :to="'/find'">
              <div class="btn-item">{{ $t("find_account_btn") }}</div>
            </router-link>
          </div>
        </v-form>
        <!-- SNS 버튼 section -->
        <!-- <SnsButtons /> -->
        <SnsButtons v-if="type !== 'kardio'" />
      </div>
      <Loading v-if="loading" />
    </background>
  </div>
</template>

<script>
import Background from "@/components/Common/Background.vue";
import SnsButtons from "./sections/SnsButtons.view.vue";
import WithdrawalModal from "@/components/Home/DeletedModal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";
import Loading from "@/components/Common/Loading.vue";

import NoticeModal from "@/components/Common/ConfirmModal.vue";

export default {
  components: {
    NoticeModal,

    Background,
    SnsButtons,
    WithdrawalModal,
    ErrorModal,
    Loading,
  },
  data() {
    return {
      type: "",
      userId: "",
      password: "",
      loading: false,
      showPassword: false,
      isWithdrawal: false,
      // showErrorModal: false,
      // error: this.$i18n.t("login_error_msg"),
      showNoticeModal: false,
      content:
        "[서버점검안내] <br/> 5월 10일 00:00 - 12일 23:59 동안 <br/>서버 점검으로 앱 이용이 어렵습니다.<br/>양해 부탁드려요.😊",
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
      isDevelopment: process.env.NODE_ENV === "development",
    };
  },
  computed: {
    appType() {
      return this.$store.state.appType;
    },
    showErrorModal() {
      return this.$store.state.loginError;
    },
    errorMsg() {
      if (this.$store.state.loginErrorMsg === "login")
        return this.$i18n.t("login_error_msg");
      else if (this.$store.state.loginErrorMsg === "delete")
        return this.$i18n.t("deleted_account");
      else return this.$i18n.t("session_error_msg");
    },
  },
  watch: {
    appType(newVal) {
      this.type = newVal;
    },
  },
  mounted() {
    this.isWithdrawal = JSON.parse(localStorage.getItem("isWithdrawal"));
    const localType = localStorage.getItem("type");
    // console.log(localType);
    if (localType === null || localType === "") {
      this.type = this.appType === "kardio" ? this.appType : "human";
    } else if (localType === "kardio") {
      this.type = localType;
    } else {
      this.type = "human";
    }
    // console.log("type:", this.type);
    // console.log(this.type !== "kardio");
  },
  methods: {
    isConfirmed() {
      // console.log("notice modal");
    },

    async generalLogin() {
      this.loading = true;
      try {
        // this.showErrorModal = false;
        const userData = {
          type: this.type,
          account: this.userId,
          password: this.password,
        };
        this.$store.dispatch("LOGIN", userData);
        this.loading = false;
      } catch (error) {
        // this.showErrorModal = true;
        console.log(error.response);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
      // setTimeout(() => {
      //   this.showErrorModal = localStorage.getItem("loginError");
      // }, 5000);
    },
    isClicked() {
      // this.showErrorModal = false;
      this.$store.commit("setLoginError", false);
      localStorage.setItem("loginError", false);
    },
  },
};
</script>

<style lang="scss" scoped>
.header__wrapper {
  padding: 190px 0 90px;
}
.header__wrapper-android {
  padding: 160px 30px 80px;
}

.main-text {
  width: 100%;
  /* padding: 0 30px; */
  img {
    width: 100%;
  }
}

.login-form__wrapper {
  width: 100%;
  padding: 0 30px;
}

.v-text-field.v-text-field--solo .v-input__control input {
  caret-color: #41d8e6 !important;
  padding: 0px 0 10px !important;
}

.main-large-btn {
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

:deep(.v-input) {
  font-family: GilroyMedium !important;
  font-size: 20px;
}

:deep(.v-text-field input:invalid) {
  padding: 8px 0 4px 0;
  // padding: 0;
}
:deep(.v-text-field) {
  label {
    padding: 0;
    font-size: 20px !important;
    color: #a7a7a7;
  }
}

:deep(.v-text-field .v-label) {
  top: 0 !important;
  padding: 0;
  // font-size: 12px;
  // color: #41d8e6;
}

.btn__wrapper {
  display: flex;
  justify-content: center;
}

.btn-item {
  margin: 0px 10px;
  font-size: 16px;
  color: #858585;
  font-weight: 500;
  letter-spacing: -0.03em;
}

.txt-blackColor {
  color: #000;
}

.btn-divider {
  font-size: 14px;
}
</style>
