<template>
  <div class="aos-btn__wrapper">
    <div class="aos-btn__container">
      <div class="kakao-btn" @click="kakaoLogin">
        <img src="@/assets/images_assets/sns-icon/kakao.png" alt="kakao login" />
        <div class="login-text">{{ $t("kakao_login") }}</div>
        <div class="ballon_kakao" v-if="recentLogin === 'kakao'">{{ $t("recent_login") }}</div>
      </div>
      <div class="google-btn" @click="googleLogin">
        <img src="@/assets/images_assets/sns-icon/google.png" alt="google login" />
        <div class="login-text">{{ $t("google_login") }}</div>
        <div class="ballon" v-if="recentLogin === 'google'">{{ $t("recent_login") }}</div>
      </div>
    </div>
    <!-- <div v-for="(item, idx) in aosSnsBtns" :key="idx" class="aos-btn__container" @click="snsLoginRequest(item.type)">
      <div class="sns-btn"><img :src="item.img" alt="item.type" /></div>
    </div> -->
  </div>
</template>

<script>
import API from "@/api/auth/index.js";

export default {
  props: {
    recentLogin: String,
  },
  data() {
    return {
      recentIcon: require("@/assets/images_assets/icons/recent_tooltip-ic.png"),
      aosSnsBtns: [
        {
          type: "kakao",
          img: require("@/assets/images_assets/sns-icon/kakao.png"),
        },
        {
          type: "google",
          img: require("@/assets/images_assets/sns-icon/google.png"),
        },
      ],
    };
  },
  methods: {
    kakaoLogin() {
      // console.log("kakao");
      /*global Webview*/
      /*eslint no-undef: "error"*/
      const bridge_msg = {
        action: "loginKakao",
      };
      Webview.kakaoLogin(bridge_msg);
    },
    googleLogin() {
      // console.log("google");
      const bridge_msg = {
        action: "loginGoogle",
      };
      Webview.googleLogin(bridge_msg);
    },
  },
};
</script>

<style lang="scss" scoped>
.aos-btn__wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-top: 40px;
}

.aos-btn__container {
  position: relative;
  height: 70px;
  img {
    width: 100%;
  }
}

.google-btn {
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  border: 1px solid #ededed;
  border-radius: 10px;
  img {
    width: 28px;
    height: 28px;
    line-height: 60px;
  }
}

.kakao-btn {
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fae300;
  border-radius: 10px;
  margin-bottom: 20px;
  img {
    width: 28px;
    height: 26px;
    line-height: 60px;
  }
}
.login-text {
  width: 70%;
  font-weight: 500;
  font-size: 20px;
}

.ballon {
  position: absolute;
  width: 45px;
  height: 40px;
  right: 7%;
  bottom: 62px;
  background: #41d8e6;
  color: white;
  font-weight: 500;
  border-radius: 100%;
  padding: 8px 0;
  letter-spacing: -0.03em;
  font-size: 14px;
}

.ballon:after {
  border-top: 10px solid #41d8e6;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 0px solid transparent;
  content: "";
  position: absolute;
  left: 39%;
  bottom: -5px;
}
.ballon_kakao {
  position: absolute;
  width: 45px;
  height: 40px;
  right: 7%;
  bottom: -15px;
  background: #41d8e6;
  color: white;
  font-weight: 500;
  border-radius: 100%;
  padding: 8px 0;
  letter-spacing: -0.03em;
  font-size: 14px;
}

.ballon_kakao:after {
  border-top: 10px solid #41d8e6;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 0px solid transparent;
  content: "";
  position: absolute;
  left: 39%;
  bottom: -5px;
}
</style>
