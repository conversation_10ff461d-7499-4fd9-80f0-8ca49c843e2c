<template>
  <div class="ios-snsBtns__wrapper">
    <div v-for="(item, idx) in iosSnsBtns" :key="idx" class="ios-btn__container" @click="snsLoginRequest(item.type)">
      <img :src="item.img" alt="item.type" />
      <!-- <img :src="recentIcon" alt="icon" id="recent-icon" /> -->
      <div class="ballon" v-if="recentLogin === item.type">{{ $t("recent_login") }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    recentLogin: String,
  },
  data() {
    return {
      recentIcon: require("@/assets/images_assets/icons/recent_tooltip-ic.png"),
      iosSnsBtns: [
        {
          type: "apple",
          img: require("@/assets/images_assets/sns-icon/apple-small.png"),
        },
        {
          type: "google",
          img: require("@/assets/images_assets/sns-icon/google-small.png"),
        },
        {
          type: "kakao",
          img: require("@/assets/images_assets/sns-icon/kakao-small.png"),
        },
      ],
    };
  },
  methods: {
    kakaoLogin() {
      // console.log("kakao");
      /*global Webview*/
      /*eslint no-undef: "error"*/
      const bridge_msg = {
        action: "loginKakao",
      };
      Webview.kakaoLogin(bridge_msg);
    },
    googleLogin() {
      // console.log("google");
      const bridge_msg = {
        action: "loginGoogle",
      };
      Webview.googleLogin(bridge_msg);
    },
    appleLogin() {
      // console.log("apple");
      const bridge_msg = {
        action: "loginApple",
      };
      Webview.appleLogin(bridge_msg);
    },
    snsLoginRequest(type) {
      if (type === "kakao") {
        this.kakaoLogin();
      }
      if (type === "google") {
        this.googleLogin();
      }
      if (type === "apple") {
        this.appleLogin();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.ios-snsBtns__wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
}

.ios-btn__container {
  position: relative;
  img {
    width: 80px;
    height: 80px;
  }
}

#recent-icon {
  position: absolute;
  width: 32px;
  height: 35px;
  top: -17%;
  left: 45%;
  transform: translateX(-50%);
}

.ballon {
  position: absolute;
  width: 45px;
  height: 40px;
  top: -28%;
  left: 45%;
  transform: translateX(-50%);
  background: #41d8e6;
  color: white;
  font-weight: 500;
  border-radius: 100%;
  padding: 8px 0;
  letter-spacing: -0.03em;
  font-size: 14px;
}

.ballon:after {
  border-top: 10px solid #41d8e6;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 0px solid transparent;
  content: "";
  position: absolute;
  left: 39%;
  bottom: -5px;
}
</style>
