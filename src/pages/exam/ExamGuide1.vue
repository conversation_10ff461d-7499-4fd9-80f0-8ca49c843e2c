<template>
  <div>
    <background>
      <HeaderNav page="guide1" />
      <ExamGuide1 />
      <FixedButton :title="title" :path="path" :color="color" :textColor="textColor" />
    </background>
  </div>
</template>

<script>
import HeaderNav from "@/components/Exam/HeaderNav.vue";
import FixedButton from "@/components/Exam/FixedButton.vue";

import ExamGuide1 from "../../components/Exam/ExamGuide1.vue";

export default {
  components: {
    HeaderNav,
    FixedButton,
    ExamGuide1,
  },

  data() {
    return {
      title: this.$i18n.t("btn_content_first"),
      path: "/exam/guide2",
      color: "#41d8e6",
      textColor: "#fff",
      hideDelimiters: true,
      touchless: true,
      currentState: 2,
    };
  },

  methods: {
    changeCurrentState(idx) {
      this.currentState = idx;
    },
  },
};
</script>

<style></style>
