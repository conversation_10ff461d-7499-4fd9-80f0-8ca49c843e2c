<template>
  <div>
    <background>
      <HeaderNav page="guide2" />
      <ExamGuide2 />
      <FixedButton :title="title" :path="path" :color="color" :textColor="textColor" />
    </background>
  </div>
</template>

<script>
import HeaderNav from "@/components/Exam/HeaderNav.vue";
import FixedButton from "@/components/Exam/FixedButton.vue";

import ExamGuide2 from "@/components/Exam/ExamGuide2.vue";

export default {
  components: {
    HeaderNav,
    FixedButton,
    ExamGuide2,
  },

  data() {
    return {
      title: this.$i18n.t("btn_content_second"),
      path: "/exam/wait",
      color: "#41d8e6",
      textColor: "#fff",
      hideDelimiters: true,
      touchless: true,
      currentState: 3,
    };
  },

  methods: {
    changeCurrentState(idx) {
      this.currentState = idx;
    },
  },
};
</script>

<style></style>
