<template>
  <div>
    <background>
      <HeaderNav page="video" />
      <ExamVideo />
      <FixedButton :title="title" :path="path" :color="color" :textColor="textColor" />
    </background>
  </div>
</template>

<script>
import HeaderNav from "@/components/Exam/HeaderNav.vue";
import FixedButton from "@/components/Exam/FixedButton.vue";

import ExamVideo from "@/components/Exam/ExamVideo.vue";

export default {
  components: {
    HeaderNav,
    FixedButton,
    ExamVideo,
  },

  data() {
    return {
      title: this.$i18n.t("confirm_btn"),
      path: "/exam/guide1",
      color: "#41d8e6",
      textColor: "#fff",
      hideDelimiters: true,
      touchless: true,
      currentState: 1,
    };
  },

  methods: {
    changeCurrentState(idx) {
      this.currentState = idx;
    },
  },
};
</script>

<style></style>
