<template>
  <div>
    <background>
      <HeaderNav page="wait" @goToCamera="goToCamera" />
      <!-- image section -->
      <div v-show="firstContents" :class="lang === 'ko' ? 'images-section' : 'images-section_en'">
        <div class="image__wrapper">
          <!-- <img src="@/assets/images/guide_img/removing_pee.gif" alt="removing pee image" /> -->
          <transition name="fade">
            <div v-show="firstTextIndex >= 0" class="guide_text1">
              {{ $t("exam_guide_txt_one") }}
            </div>
          </transition>
          <transition name="fade">
            <div v-show="firstTextIndex >= 1 || firstTextIndex >= 2" class="guide_bold_text">
              {{ $t("exam_guide_txt_two") }}
            </div>
          </transition>
          <transition name="fade">
            <div v-show="firstTextIndex >= 2" class="guide_text3">
              {{ $t("exam_guide_txt_three") }}
            </div>
          </transition>
        </div>
      </div>
      <div class="guide-contents__wrapper">
        <div v-show="timeToContents" class="video-section">
          <!-- type="video/mp4"
          autoplay="autoplay"
          loop="loop"
          muted="muted"
          playsinline
          webkit-playsinline="webkit-playsinline" -->
          <img :src="guideTutorial" width="100%" />
        </div>
        <div class="exam-wait-content-section">
          <div class="timer__wrapper">
            <circular-count-down-timer
              class="timer"
              :initial-value="initSecond"
              :stroke-width="5"
              :seconds-stroke-color="'#41D8E6'"
              :underneath-stroke-color="'#F8F8F8'"
              :seconds-fill-color="'transparent'"
              :size="240"
              :padding="4"
              :second-label="''"
              :show-second="true"
              :show-minute="false"
              :show-hour="false"
              :show-negatives="true"
              :notify-every="'second'"
              :paused="stopTimer"
              @finish="startCamera"
              @update="checkSecond"
            ></circular-count-down-timer>
          </div>
        </div>
      </div>

      <div class="wait-btn__wrapper">
        <div :class="lang === 'ko' ? 'wait-btn' : 'wait-btn_en'">
          <div>{{ $t("exam_notice_btn") }}</div>
        </div>
      </div>
    </background>
  </div>
</template>

<script>
import HeaderNav from "@/components/Exam/HeaderNav.vue";
import { refreshToken, setExp } from "@/api/common/tokenModule";
import { getSubjectId } from "@/components/Common/getSubjectId.js";

export default {
  components: {
    HeaderNav,
  },
  data() {
    return {
      initSecond: 60,
      waitText: "",
      waitImg: "",
      firstContents: true,
      timeToContents: false,
      isPlay: true,
      waitTextArr: [
        this.$i18n.t("exam_guide_txt_one"),
        this.$i18n.t("exam_guide_txt_two"),
        this.$i18n.t("exam_guide_txt_three"),
      ],
      lang: this.$i18n.locale === "ko" ? "ko" : "en",
      guideTutorial: "",
      textIndex: 0,
      firstTextIndex: 0,
      stopTimer: false,
      cameraAction: true,
    };
  },
  mounted() {
    // this.waitText = this.waitTextArr[this.firstTextIndex];
    this.guideTutorial = require(`@/assets/images/guide_img/guide_tutorial_${this.lang}.gif`);
    const interval = setInterval(() => {
      this.firstTextIndex++;
      // console.log(this.firstTextIndex);
      if (this.firstTextIndex === 5) {
        clearInterval(interval);
      }
    }, 1000);
  },

  methods: {
    goBackHome() {
      this.$router.push({ path: "/home" });
    },
    checkSecond(status) {
      if (status.value === 0) {
        this.stopTimer = true;
        this.isPlay = false;
      }
      if (status.value === 55) {
        this.timeToContents = true;
        this.firstContents = false;
        const interval = setInterval(() => {
          if (this.isPlay) {
            this.textIndex++;
            if (this.textIndex === 6) {
              clearInterval(interval);
            }
          } else {
            clearInterval(interval);
          }
        }, 9000);
      }
    },
    skipNext() {
      // console.log("do something");
    },

    async startCamera() {
      if (this.cameraAction) {
        /*global Webview*/
        /*eslint no-undef: "error"*/
        const subjectId = getSubjectId();
        // const token = await refreshToken();
        // localStorage.auth = token;
        // this.$store.commit("SET_TOKEN", token);
        // setExp(token);
        const message = {
          action: "turnOnCameraMessage",
          accessToken: localStorage.auth,
          subId: subjectId,
        };
        // console.log(message);
        Webview.openCamera(message);
      }
    },
    goToCamera() {
      this.stopTimer = true;
      this.initSecond = 60;
      this.cameraAction = false;

      const subjectId = getSubjectId();
      // const token = refreshToken();
      // localStorage.auth = token;

      const message = {
        action: "turnOnCameraMessage",
        accessToken: localStorage.auth,
        subId: subjectId,
      };
      Webview.openCamera(message);
    },
  },
};
</script>

<style scoped>
.guide-contents__wrapper {
  height: 100%;
  top: 100px;
}

.images-section {
  background-color: #ffffff;
  height: 410px;
  min-height: 410px;
  padding-top: 90px;
  position: relative;
}
.images-section_en {
  background-color: #ffffff;
  height: 410px;
  min-height: 410px;
  padding-top: 90px;
  position: relative;
}

.video-section {
  height: 410px;
  min-height: 410px;
  padding-top: 90px;
}

.image__wrapper {
  background-color: #ffffff;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image__wrapper > img {
  width: 100%;
}

.guide_text1 {
  color: #000000;
  font-size: 32px;
  font-weight: 500;
  letter-spacing: -0.03em;
  position: absolute;
  top: 165px;
  transition: opacity 0.5s linear;
}
.guide_bold_text {
  color: #41d8e6;
  font-size: 40px;
  font-weight: 700;
  letter-spacing: -0.03em;
  position: absolute;
  top: 210px;
  transition: opacity 0.5s linear;
}
.guide_text3 {
  color: #000000;
  font-size: 32px;
  font-weight: 500;
  letter-spacing: -0.03em;
  position: absolute;
  top: 265px;
  transition: opacity 0.5s linear;
}

.image-txt__wrapper {
  width: 100%;
  position: absolute;
  background-color: #c9f4f8;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
}

.note_text {
  color: #000000;
  text-align: center;
  font-weight: 500;
  font-size: 20px;
  line-height: 20px;
}

.guide1-content-section .guide1-content-title__wrapper {
  width: 100%;
  text-align: left;
  font-style: normal;
  font-weight: 600;
  font-size: 22px;
  line-height: 27px;
}

.exam-wait-content-section {
  width: 100%;
  height: 400px;
}

.timer__wrapper {
  width: 100%;
  height: 100%;
  font-family: "GilroyMedium";
  /* padding: 12vh 0; */
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(#container) {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.wait-btn__wrapper {
  width: 100%;
  position: fixed;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 20.83%, #ffffff 100%);
  bottom: 0;
  height: calc(5vh + 90px);
  left: 0;
  padding: 30px 30px 0px 30px;
}

.exam-next-btn {
  width: 100%;
  max-width: 390px !important;
  height: 50px;
  color: #fff;
  background: #41d8e6;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  font-weight: 700;
  font-size: 20px;
}

.timer {
  padding: 0;
}

:deep(#container .item > div) {
  font-style: normal;
  font-size: 60px !important;
  line-height: 200px !important;
}

.timer-guide {
  text-align: center;
}
.timer-guide-text {
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.03em;
  color: #000000;
}

.wait-btn {
  font-weight: 700;
  font-size: 20px;
  line-height: 29px;
  letter-spacing: -0.03em;
  width: 100%;
  max-width: 390px !important;
  height: 50px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #41d8e6;
  color: #c9f4f8;
  margin: 0 auto;
}
.wait-btn_en {
  /* font-family: GilroyMedium; */
  font-size: 25px;
  line-height: 29px;
  letter-spacing: -0.03em;
  width: 100%;
  max-width: 390px !important;
  height: 50px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #41d8e6;
  color: #c9f4f8;
  margin: 0 auto;
}

.fade-enter {
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.8s ease-out;
}

.fade-leave-to {
  opacity: 0;
}
</style>
