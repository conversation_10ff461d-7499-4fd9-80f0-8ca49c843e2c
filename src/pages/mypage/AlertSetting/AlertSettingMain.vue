<template>
  <div>
    <ConfirmModal
      v-show="toggleClicked"
      :content="content"
      :btnText="this.$i18n.t('confirm_btn')"
      :createdAt="createdAt"
      @isConfirmed="isConfirmed"
    />
    <CheckModal
      v-show="showCheckModal"
      :content="checkContent"
      @cancelHandler="cancelHandler"
      @confirmHandler="confirmHandler"
    />
    <HeaderNav :pageName="pageName" />
    <div class="settings__wrapper">
      <div class="pushAlert__container">
        <div class="alert-header">{{ pushAlert }}</div>
        <div class="icon__wrapper">
          <v-switch
            color="#41d8e6"
            height="20px"
            v-model="isPushAlertSettingMode"
            inset
            @click="setPushAlertSetting"
          ></v-switch>
        </div>
      </div>
      <div class="underline"></div>

      <div class="pushAlert__container">
        <div class="alert-header">{{ notificationAlert }}</div>
        <div class="icon__wrapper">
          <v-switch color="#41D8E6" v-model="isNotificationAlertMode" inset @click="setNotificationAlert"></v-switch>
        </div>
      </div>
      <div class="pushAlert__container">
        <div class="alert-header">{{ marketingAlertMode }}</div>
        <div class="icon__wrapper">
          <v-switch color="#41D8E6" v-model="isMarketingAlertMode" inset @click="setMarketingAlert"></v-switch>
        </div>
      </div>
      <p class="alert-description" v-html="this.$i18n.t('alert_description')"></p>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import ConfirmModal from "@/components/Common/ConfirmModal.vue";
import CheckModal from "@/components/Common/CheckModal.vue";

export default {
  components: {
    HeaderNav,
    ConfirmModal,
    CheckModal,
  },
  data() {
    return {
      isKo: true,
      pageName: this.$i18n.t("setting_notification"),
      isPushAlertSettingMode: false,
      pushAlert: this.$i18n.t("setting_push"),
      isNotificationAlertMode: false,
      notificationAlert: this.$i18n.t("setting_noti"),
      isMarketingAlertMode: false,
      marketingAlertMode: this.$i18n.t("setting_marketing"),
      content: "",
      createdAt: "",
      toggleClicked: false,
      showCheckModal: false,
      checkContent: "",
      topic: "",
    };
  },

  computed: {
    notiStatus() {
      return this.$store.state.notiStatus;
    },
  },

  watch: {
    notiStatus(newVal) {
      this.isPushAlertSettingMode = newVal;
    },
  },

  methods: {
    setPushAlertSetting() {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      const message = [{ action: "onPushAlert" }, { action: "onMarketing" }, { action: "onNotice" }];
      const offMessage = [{ action: "offPushAlert" }, { action: "offMarketing" }, { action: "offNotice" }];
      if (this.isPushAlertSettingMode) {
        this.isNotificationAlertMode = true;
        this.isMarketingAlertMode = true;
        localStorage.setItem("push", true);
        localStorage.setItem("marketing", true);
        localStorage.setItem("notice", true);
        this.content = this.$i18n.t("notice_marketing_agree_modal");
        this.getCurTime();
        this.toggleClicked = true;
        Webview.onPushAlert(message[0]);
        Webview.onMarketing(message[1]);
        Webview.onNotice(message[2]);
      } else {
        this.isNotificationAlertMode = false;
        this.isMarketingAlertMode = false;
        localStorage.setItem("push", false);
        localStorage.setItem("marketing", false);
        localStorage.setItem("notice", false);
        this.content = this.$i18n.t("notice_marketing_disagree_modal");
        this.getCurTime();
        this.toggleClicked = true;
        Webview.offPushAlert(offMessage[0]);
        Webview.offMarketing(offMessage[1]);
        Webview.offNotice(offMessage[2]);
      }
    },
    setNotificationAlert() {
      // console.log(this.isNotificationAlertMode);
      if (this.isPushAlertSettingMode) {
        if (this.isNotificationAlertMode) {
          localStorage.setItem("notice", true);
          this.content = this.$i18n.t("notice_agree_modal");
          this.getCurTime();
          this.toggleClicked = true;
          Webview.onNotice({ action: "onNotice" });
        } else {
          this.topic = this.$i18n.t("topic_notice");
          this.notiHandler();
        }
      }
    },
    setMarketingAlert() {
      if (this.isPushAlertSettingMode) {
        if (this.isMarketingAlertMode) {
          localStorage.setItem("marketing", true);
          this.content = this.$i18n.t("marketing_agree_modal");
          this.getCurTime();
          this.toggleClicked = true;
          Webview.onMarketing({ action: "onMarketing" });
        } else {
          this.topic = this.$i18n.t("topic_marketing");
          this.notiHandler();
        }
      }
    },
    notiHandler() {
      // console.log(this.topic);
      this.checkContent = this.isKo
        ? `${this.topic} ${this.$i18n.t("confirm_disagree_modal")}`
        : `${this.$i18n.t("confirm_disagree_modal")} ${this.topic}?`;
      this.showCheckModal = true;
    },
    cancelHandler() {
      // console.log("Cancelling");
      this.showCheckModal = false;
      this.loadNotiStatus();
    },
    confirmHandler() {
      if (this.topic === this.$i18n.t("topic_notice")) {
        localStorage.setItem("notice", false);
        this.showCheckModal = false;
        this.toggleClicked = true;
        this.content = this.isKo
          ? `${this.topic} ${this.$i18n.t("notice_disagree_modal")}`
          : `${this.$i18n.t("notice_disagree_modal")} ${this.topic}.`;
        this.getCurTime();
        Webview.offNotice({ action: "offNotice" });
      } else {
        localStorage.setItem("marketing", false);
        this.showCheckModal = false;
        this.toggleClicked = true;
        this.content = this.isKo
          ? `${this.topic} ${this.$i18n.t("notice_disagree_modal")}`
          : `${this.$i18n.t("notice_disagree_modal")} ${this.topic}.`;
        this.getCurTime();
        Webview.offMarketing({ action: "offMarketing" });
      }
    },
    isConfirmed() {
      this.toggleClicked = false;
      this.loadNotiStatus();
    },
    getCurTime() {
      const unixTime = new Date().getTime();
      const curLocalTime = new Date(unixTime);
      const year = curLocalTime.getFullYear();
      const month = curLocalTime.getMonth() + 1;
      const day = curLocalTime.getDate();
      let hh = curLocalTime.getHours();
      let mm = curLocalTime.getMinutes();
      let period = hh < 12 ? "AM" : "PM";

      if (hh === 0) {
        hh = 12;
      } else if (hh > 12) {
        hh = hh - 12;
      }

      hh = hh < 10 ? `0${hh}` : hh;
      mm = mm < 10 ? `0${mm}` : mm;

      this.createdAt = `${year}.${month}.${day} ${hh}:${mm} ${period}`;
    },
    notiIsAuthorized() {
      Webview.notiAuthorized({ action: "notiAuthorized" });
      this.$nextTick(() => this.loadNotiStatus());
    },
    loadNotiStatus() {
      // this.isPushAlertSettingMode = JSON.parse(localStorage.getItem("push"));
      this.isNotificationAlertMode = JSON.parse(localStorage.getItem("notice"));
      this.isMarketingAlertMode = JSON.parse(localStorage.getItem("marketing"));
    },
  },
  mounted() {
    this.notiIsAuthorized();
    this.isPushAlertSettingMode = this.notiStatus && JSON.parse(localStorage.getItem("push"));
    this.isKo = this.$i18n.locale === "ko";
  },
};
</script>

<style lang="scss" scoped>
.settings__wrapper {
  width: 100%;
  padding: 40px 30px;
  letter-spacing: -0.03em;
}

.pushAlert__container {
  display: flex;
  justify-content: space-between;
  margin: 15px 0;
}

.alert-header {
  height: 100%;
  display: flex;
  justify-content: space-between;
  color: #000000;
  font-weight: 500;
  font-size: 18px;
}

p {
  font-size: 14px;
  color: #646464;
  text-align: left;
  margin: 5px 0 10px;
}

.icon__wrapper {
  display: flex;
  width: 40px;
  height: 30px;
  align-items: center;
  justify-content: flex-end;
}

.underline {
  border-bottom: 0.5px solid #a7a7a7;
}

a {
  color: #000;
}

::v-deep .v-input--selection-controls__input {
  width: 36px !important;
}

::v-deep .theme--light.v-input--switch .v-input--switch__track {
  color: #dadada;
}

::v-deep .v-input--switch__track {
  opacity: 1 !important;
  width: 45px !important;
  height: 25px !important;
}

::v-deep .v-input--switch__thumb {
  color: #ffffff !important;
  width: 17px !important;
  height: 17px !important;
}

::v-deep .v-messages {
  min-height: 5px;
}

.alert-description {
  margin: 20px 0;
  padding: 23px 13px;
  font-size: 14px;
  letter-spacing: -0.03em;
  background-color: #f8f8f8;
  border-radius: 10px;
}
</style>
