<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <ConnectionModal
      v-if="isConnectedModal"
      :content="$i18n.t('alreay_exist')"
      :btnText="$i18n.t('confirm')"
      @isConfirmed="closeHandler"
    />
    <div v-if="loaded" class="container">
      <div v-for="(sns, idx) in snsArr" :key="idx">
        <div class="toggle__wrapper" v-if="sns.ios">
          <div class="toggle-title">
            {{ $t(sns.type) }}
            <span v-if="sns.logined" class="cur-logined">{{ $t("cur_login") }}</span>
          </div>
          <div class="switch__wrapper">
            <v-switch
              class="pa-1 mt-0 mr-2"
              color="#41d8e6"
              height="20px"
              inset
              v-model="sns.connected"
              @change="connectionHandler(sns)"
              :disabled="sns.logined"
            ></v-switch>
          </div>
        </div>
      </div>
      <div v-if="general !== null" class="toggle__wrapper">
        <div class="toggle-title">
          {{ $t("general_account") }}
          <span v-show="!isSnsLogin" class="cur-logined">{{ $t("cur_login") }}</span>
        </div>
        <div class="general">
          {{ general.length > 8 ? general.slice(0, 8) + "..." : general }}
        </div>
      </div>
    </div>
    <div class="snackbar">
      <v-snackbar v-model="isSuccess" timeout="2000">{{ successContent }}</v-snackbar>
      <v-snackbar v-model="isFailed" timeout="2000" color="#EE0000">{{ failContent }}</v-snackbar>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import ConnectionModal from "@/components/Common/ConfirmModal.vue";
import { getConntectedAccount, postConnection, deleteConnection } from "@/api/user";

export default {
  components: {
    HeaderNav,
    ConnectionModal,
  },

  data() {
    return {
      pageName: this.$i18n.t("sns_login_settings"),
      snsArr: [
        { type: "kakao", logined: false, connected: false, ios: true },
        { type: "google", logined: false, connected: false, ios: true },
        { type: "apple", logined: false, connected: false, ios: true },
      ],
      general: null,
      loaded: false,
      isSnsLogin: JSON.parse(localStorage.getItem("isSnsLogin")),
      clickedType: "",
      isSuccess: false,
      isFailed: false,
      successContent: this.$i18n.t("connection_success"),
      failContent: this.$i18n.t("connection_fail"),
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },

  computed: {
    isConnectedModal() {
      return this.$store.state.snsConnectionModal;
    },
    snsUid() {
      return this.$store.state.snsUid;
    },
  },

  watch: {
    snsUid(newVal) {
      // alert(newVal);
      // console.log("uid:");
      // console.log(newVal);
      this.oauthConnect(newVal);
    },
  },

  methods: {
    connectionHandler(sns) {
      // console.log(sns.connected);
      this.clickedType = sns.type;
      sns.connected ? this.snsLoginRequest(sns.type) : this.disconnect(sns.type);
    },

    closeHandler() {
      const obj = this.snsArr.find((obj) => obj.type === this.clickedType);
      if (obj) obj.connected = false;
      this.$store.commit("SET_CONNECTION_MODAL", false);
    },

    initialState(data) {
      const type = localStorage.snsType;
      this.isGeneral = data.general !== null;
      this.general = data.general;
      this.snsArr.map((sns) => {
        sns.connected = data[sns.type];
        sns.type === type ? (sns.logined = true) : null;
      });
      this.loaded = true;
    },

    async getAccountInfo() {
      try {
        const { data, status } = await getConntectedAccount();
        // console.log(data);
        this.initialState(data);
      } catch (e) {
        console.log(e);
      }
    },

    async oauthConnect(uid) {
      // console.log("sns connection");
      try {
        const body = { oauthUid: uid };
        const { data, status } = await postConnection(this.clickedType, body);
        // console.log(data, status);
        if (status === 201) {
          this.successContent = "연동 성공";
          this.isSuccess = true;
          // console.log("연동 성공");
        }
      } catch (err) {
        this.failContent = "연동 실패";
        this.isFailed = true;
        console.log(err);
      }
    },

    async disconnect(type) {
      try {
        const { status } = await deleteConnection(type);
        // console.log(status);
        if (status === 204) {
          this.successContent = "연동 해제 성공";
          this.isSuccess = true;
          // console.log("해제 성공");
        }
      } catch (err) {
        this.failContent = "연동 해제 실패";
        this.isFailed = true;
        console.log(err);
      }
    },

    snsLoginRequest(type) {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      if (type === "kakao") {
        const bridge_msg = {
          action: "loginKakao",
        };
        Webview.kakaoLogin(bridge_msg);
      } else if (type === "google") {
        const bridge_msg = {
          action: "loginGoogle",
        };
        Webview.googleLogin(bridge_msg);
      } else {
        const bridge_msg = {
          action: "loginApple",
        };
        Webview.appleLogin(bridge_msg);
      }
    },
  },

  mounted() {
    this.getAccountInfo();
    this.snsArr[2].ios = this.isIos;
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  padding: 40px 30px;
}
.toggle__wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.toggle-title {
  color: #000000;
  font-size: 16px;
  font-weight: 500;
}

.switch__wrapper {
  display: flex;
  width: 40px;
  align-items: center;
  justify-content: flex-end;
}

.cur-logined {
  color: #a7a7a7;
}

.general {
  font-size: 16px;
  color: #a7a7a7;
}

::v-deep .v-input--selection-controls__input {
  width: 36px !important;
}

::v-deep .theme--light.v-input--switch .v-input--switch__track {
  color: #dadada;
}

::v-deep .v-input--switch__track {
  opacity: 1 !important;
  width: 45px !important;
  height: 25px !important;
}

::v-deep .v-input--switch__thumb {
  color: #ffffff !important;
  width: 17px !important;
  height: 17px !important;
}

::v-deep .v-messages {
  min-height: 5px;
}

::v-deep .theme--light.v-input--is-disabled {
  opacity: 0.4;
}
</style>
