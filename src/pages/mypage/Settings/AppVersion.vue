<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div class="version-page__wrapper">
      <div class="cym-logo__wrapper">
        <img src="@/assets/images/cym702_logo/Cym702.png" alt="Cym702" />
      </div>
      <!-- <div v-if="isUpdate" class="version-description">{{ $t("need_update") }}</div> -->
      <p class="cur-version">{{ $t("cur_version") }} {{ curVersion }}</p>
      <!-- <button v-if="isUpdate" @click="gotoAppstore">{{ $t("version") }} {{ newestVersion }} {{ $t("update") }}</button> -->
    </div>
    <p class="bottom-description" v-if="isIos">{{ $t("supported") }} iOS 13.0 {{ $t("above") }}</p>
    <p class="bottom-description" v-else>{{ $t("supported") }} Android 8.0 {{ $t("above") }}</p>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";

export default {
  components: { HeaderNav },
  data() {
    return {
      pageName: this.$i18n.t("setting_version_txt"),
      newestVersion: "1.3.4",
      curVersion: localStorage.getItem("curVersion") || "1.0.0",
      isUpdate: false,
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
  methods: {
    gotoAppstore() {
      // console.log("click to appstore");
      /*global Webview*/
      /*eslint no-undef: "error"*/
      const bridge_msg = {
        action: "goCym702",
        url: "https://apps.apple.com/kr/app/cym702/id1589026226",
      };
      Webview.goCym702(bridge_msg);
    },
    getNeedUpdate() {
      this.isUpdate = this.newestVersion !== this.curVersion;
    },
  },
  mounted() {
    // console.log(this.curVersion);
    this.getNeedUpdate();
  },
};
</script>

<style lang="scss" scoped>
.version-page__wrapper {
  width: 100%;
  height: 100%;
  padding: 15vh 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.cym-logo__wrapper {
  width: 60vw;

  img {
    width: 100%;
  }
}

.version-description {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: -0.03em;
  margin-top: 20px;
}

p {
  margin-top: 10px;
  font-size: 12px;
}

button {
  width: 100%;
  background-color: #41d8e6;
  color: #ffffff;
  max-width: 300px;
  height: 55px;
  border-radius: 10px;
  font-weight: 700;
  font-size: 22px;
  margin-top: 10px;
}

.cur-version {
  font-size: 14px;
  letter-spacing: -0.03em;
}

.bottom-description {
  font-size: 14px;
  position: absolute;
  bottom: 12vh;
  letter-spacing: -0.03em;
  width: 100%;
  color: #646464;
}
</style>
