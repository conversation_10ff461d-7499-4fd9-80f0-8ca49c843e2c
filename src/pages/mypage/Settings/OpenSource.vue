<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div class="license__wrapper">
      <div class="list__wrapper" v-for="(source, idx) in openSource" :key="idx">
        <div class="license">
          {{ source }}
        </div>
        <!-- <div class="icon__wrapper">
          <img src="@/assets/images/chevron_right.png" alt="right arrow" />
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";

export default {
  components: { HeaderNav },
  data() {
    return {
      pageName: this.$i18n.t("open_source"),
      openSource: [
        this.$i18n.t("foods_source"),
        "Alamofire",
        "SwiftyJSON",
        "Gifu",
        "iOSDropDown",
        "Firebase",
        "Firebase/Core",
        "Firebase/Messaging",
        "ReachabilitySwift",
        "GoogleSignIn",
        "KakaoSDK",
        "KakaoSD<PERSON><PERSON>ommon",
        "KakaoSDKAuth",
        "KakaoSDKUser",
        "KakaoSDKTalk",
        "openCV",
        "retrofit",
        "SwiftyJSON",
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.license__wrapper {
  width: 100%;
  padding: 30px;
}

.list__wrapper {
  width: 100%;
  display: flex;
  padding: 15px 0;
  border-bottom: 0.5px solid #a7a7a7;
}

.license {
  // width: 80%;
  display: flex;
  text-align: left;
  font-size: 16px;
  font-weight: 500;
}
.list__wrapper:last-child {
  border: none;
}

.icon__wrapper {
  display: flex;
  width: 20%;
  align-items: center;
  justify-content: flex-end;
  img {
    width: 10px;
  }
}
</style>
