<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <CheckModal
      v-if="showLogoutModal"
      :content="content"
      @cancelHandler="cancelHandler"
      @confirmHandler="confirmHandler"
    />
    <div class="settings__container">
      <div class="menu-items">
        <router-link to="/mypage/settings/account">
          <div class="menu-card">
            <div class="menu-card-header">
              <div class="menu-card-title">{{ $t("account_info") }}</div>
              <div class="icon__wrapper">
                <img
                  src="@/assets/images/chevron_right.png"
                  alt="right arrow"
                />
              </div>
            </div>
          </div>
        </router-link>
        <router-link to="/mypage/settings/sns">
          <div class="menu-card">
            <div class="menu-card-header">
              <div class="menu-card-title">{{ $t("sns_login_settings") }}</div>
              <div class="icon__wrapper">
                <img src="@/assets/images/chevron_right.png" alt="right arrow" />
              </div>
            </div>
          </div>
        </router-link>
        <div class="menu-item" @click="logoutModalHandler">
          <div class="menu-right">{{ $t("setting_logout") }}</div>
          <div class="icon__wrapper">
            <img src="@/assets/images/chevron_right.png" alt="right arrow" />
          </div>
        </div>
      </div>
      <!-- HealthKit 영역 -->
      <!-- <div v-if="isIos">
        <div class="menu-card">
          <div class="menu-card-header">
            <div class="menu-card-title">{{ $t("setting_data_share") }}</div>
          </div>
        </div>
        <div class="menu-card">
          <div class="menu-sub-card">
            <div class="menu-sub-title">{{ samsungHealthKit }}</div>
            <div class="icon__wrapper">
              <v-switch
                background="white"
                v-model="isSamsungHealthKitMode"
                @click="setSamsungHealthkitMode"
                inset
              ></v-switch>
            </div>
          </div>
          <div class="menu-sub-card">
            <div class="menu-sub-title">{{ appleHealthKit }}</div>
            <div class="icon__wrapper">
              <v-switch
                class="pa-1 mt-0 mr-2"
                color="#41d8e6"
                height="20px"
                v-model="isAppleHealthkitMode"
                @click="setAppleHealthkitMode"
                inset
              ></v-switch>
            </div>
          </div>
          <div class="underline"></div>
        </div>
      </div> -->

      <div class="menu-card">
        <div v-for="(menu, idx) in menus" :key="idx">
          <router-link :to="{ path: menu.path }" class="menu-card-header">
            <div class="left-menu__wrapper">
              <div class="menu-card-title">
                {{ menu.title }}
              </div>
              <!-- <div class="tag" v-if="menu.isUpdate">N</div> -->
            </div>
            <div class="right-menu__wrapper">
              <!-- <div class="version-txt" v-show="idx === 1">v{{ curVersion }}</div> -->
              <div class="icon__wrapper">
                <img
                  src="@/assets/images/chevron_right.png"
                  alt="right arrow"
                />
              </div>
            </div>
          </router-link>
        </div>
        <!-- <div @click="shareSns">카카오톡 공유 기능 test</div> -->
      </div>

      <div v-if="type === 'kardio'">
        <div class="underline-wrapper">
          <div class="underline"></div>
          <div class="menu-card">
            <div v-for="(menu, idx) in mypageMenus" :key="idx">
              <router-link :to="{ path: menu.path }" class="menu-card-header">
                <div class="left-menu__wrapper">
                  <div class="menu-card-title">
                    {{ menu.title }}
                  </div>
                </div>
                <div class="right-menu__wrapper">
                  <div class="icon__wrapper">
                    <img
                      src="@/assets/images/chevron_right.png"
                      alt="right arrow"
                    />
                  </div>
                </div>
              </router-link>
            </div>
          </div>
          <div class="underline-wrapper">
            <div class="underline"></div>
          </div>
          <div class="menu-card">
            <div class="menu-card-header">
              <div class="menu-card-title" @click="goCym702">
                {{ $t("category_about_company") }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import CheckModal from "@/components/Common/CheckModal.vue";
import { logout } from "@/api/user";

export default {
  components: {
    HeaderNav,
    CheckModal,
  },
  data() {
    return {
      type: "",
      showLogoutModal: false,
      pageName: this.$i18n.t("setting_header_title"),
      content: this.$i18n.t("setting_logout_confirm_txt"),
      samsungHealthKit: this.$i18n.t("setting_samsung"),
      appleHealthKit: this.$i18n.t("setting_apple"),
      isAppleHealthkitMode: false,
      isSamsungHealthKitMode: false,
      isKoreanMode: true,
      isEnglishMode: false,
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
      curVersion: localStorage.getItem("curVersion") || "1.0.0",
      recentVersion: "1.3.4",
      menus: [
        {
          title: this.$i18n.t("setting_notification"),
          path: "/mypage/settings/alert",
        },
        {
          title: this.$i18n.t("open_source"),
          path: "/mypage/settings/opensource",
        },
      ],
      mypageMenus: [
        {
          img: require("@/assets/images/mypage-icon/guide_icon.png"),
          title: this.$i18n.t("category_guide"),
          path: "/mypage/guide",
        },
        {
          img: require("@/assets/images/mypage-icon/push_icon.png"),
          title: this.$i18n.t("category_alert"),
          path: "/mypage/examalert",
        },
        {
          img: require("@/assets/images/mypage-icon/notice_icon.png"),
          title: this.$i18n.t("category_notice"),
          path: "/mypage/notice",
        },
        {
          img: require("@/assets/images/mypage-icon/help_icon.png"),
          title: this.$i18n.t("category_customer_service"),
          path: "/help",
        },
      ],
    };
  },

  methods: {
    logoutModalHandler() {
      this.showLogoutModal = true;
    },
    confirmHandler(fromChild) {
      // console.log(fromChild);
      this.logoutRequest();
    },
    cancelHandler(fromChild) {
      // console.log(fromChild);
      this.showLogoutModal = false;
    },
    async logoutRequest() {
      const { status } = await logout();

      if (status === 204) {
        this.$store.commit("LOGOUT");
      }
      // console.log("logout");
    },

    // 폰기종 IOS인 경우 active
    setAppleHealthkitMode() {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      if (this.isAppleHealthkitMode) {
        // console.log("connect");
        localStorage.setItem("onAppleHealthkit", true);
        const message = {
          action: "connectHealthApp",
        };

        Webview.connectHealthApp(message);
      } else {
        // console.log("disconnect");
        localStorage.setItem("onAppleHealthkit", false);
        const message = {
          action: "disconnectAppleHealthApp",
        };

        Webview.disconnectHealthApp(message);
      }
    },

    // 폰기종 AOS인 경우 active
    setSamsungHealthkitMode() {
      if (this.isSamsungHealthKitMode) {
        // console.log("connect");
        const message = {
          action: "connectSamsungHealthApp",
        };

        Webview.connectHealthApp(message);
      } else {
        // console.log("disconnect");
        const message = {
          action: "disconnectSamsungHealthApp",
        };

        Webview.disconnectHealthApp(message);
      }
    },
    goCym702() {
      // 회사 소개 페이지로 이동.
      const bridge_msg = {
        action: "goCym702",
        url: "https://yellosis.com/company.html",
      };
      Webview.goCym702(bridge_msg);
    },
    shareSns() {
      const bridge_msg = {
        action: "shareSns",
        type: "kakao",
      };
      Webview.shareSns(bridge_msg);
    },
  },
  mounted() {
    this.isAppleHealthkitMode = JSON.parse(
      localStorage.getItem("onAppleHealthkit"),
    );
    this.type = localStorage.getItem("type") ?? "human";
    // console.log(localStorage.getItem("onAppleHealthkit"));
    // console.log(this.curVersion === this.recentVersion);
    // this.menus[1].isUpdate = this.curVersion !== this.recentVersion;
  },
};
</script>

<style lang="scss" scoped>
.settings__container {
  padding: 40px 30px;
}
.menu-items {
  // margin: auto;
  // margin: 50px 30px 0px 30px;
  padding-bottom: 20px;
  border-bottom: 0.5px solid #a7a7a7;
}

.underline {
  border-bottom: 0.5px solid #a7a7a7;
}
.menu-item {
  display: flex;
  justify-content: space-between;
  // margin-bottom: 20px;
}

.menu-right {
  font-weight: 500;
  font-size: 18px;
  line-height: 23px;
}

.menu-card {
  width: 100%;
}

.menu-card-header {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0;
}

.left-menu__wrapper {
  display: flex;
  height: 100%;
  gap: 5px;
  align-items: center;
  justify-content: center;
}

.menu-card-title {
  font-size: 18px;
  font-weight: 500;
  color: #000000;
}

.menu-sub-card {
  height: 100%;
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.menu-sub-title {
  font-size: 16px;
  color: #646464;
  margin-left: 10px;
  line-height: 20px;
}

.v-input--selection-controls {
  margin: 0;
  padding: 0;
}

.right-menu__wrapper {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.icon__wrapper {
  display: flex;
  width: 20px;
  align-items: center;
  justify-content: flex-end;
  img {
    width: 10px;
  }
}

.menu-card-content {
  padding-left: 10px;
  color: #646464;
  font-size: 14px;
  line-height: 23px;
  /* margin-bottom: 10px; */
}

.version-txt {
  font-family: GilroyMedium;
  font-size: 18px;
  color: #41d8e6;
}

.tag {
  background-color: #ee0000;
  color: #ffffff;
  border-radius: 100%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  font-weight: 600;
  // line-height: 17px;
  padding-right: 1px;
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .v-input--selection-controls__input {
  width: 36px !important;
}

::v-deep .theme--light.v-input--switch .v-input--switch__track {
  color: #dadada;
}

::v-deep .v-input--switch__track {
  opacity: 1 !important;
  width: 45px !important;
  height: 25px !important;
}

::v-deep .v-input--switch__thumb {
  color: #ffffff !important;
  width: 17px !important;
  height: 17px !important;
}

::v-deep .v-messages {
  min-height: 5px;
}

// .healthkitSettingBtn__container {
//   // padding: 0 30px;
//   border-bottom: 0.5px solid #a7a7a7;
// }

// .languageSettingBtn__container {
//   padding: 0 30px;
// }
</style>
