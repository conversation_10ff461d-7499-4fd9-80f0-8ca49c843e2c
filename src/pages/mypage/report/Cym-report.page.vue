<template>
  <background>
    <!-- REPORT HEADER -->
    <CymReportHeader :tabName="currentTab" />

    <!-- REPORT CATEGORY -->
    <ReportCategory
      :tab="currentTab"
      @change="handleChangeTab"
      :tabList="tabs"
    />

    <!-- TAB ITEMS -->
    <div class="tab-content">
      <component
        v-if="currentComponent"
        :is="currentComponent"
        :key="currentTab"
      />
      <div v-else class="loading-container">
        <Loading />
      </div>
    </div>

    <!-- NAVIGATION -->
    <Navigation :path="path" />
  </background>
</template>

<script>
import CymReportHeader from "../../../components/Mypage/report/ui/cym-report-header.ui.vue";
import ReportCategory from "../../../components/Mypage/report/ui/report-category.ui.vue";
import Navigation from "@/components/Common/Navigation.vue";
import Loading from "@/components/Common/Loading.vue";

import { CYM_REPORT_VARIABLE } from "./cym-report.enum";

export default {
  name: "Cym-report",

  setup() {},

  components: {
    CymReportHeader,
    ReportCategory,
    Navigation,
    Loading,
  },
  data() {
    return {
      path: "/mypage/cym-report",
      currentTab: "#health-report",
      loadedComponents: {}, // 로드된 컴포넌트들을 캐시
      tabItems: [
        {
          id: CYM_REPORT_VARIABLE.HEALTH_REPORT,
          value: "health-report",
          componentLoader: () => import("./tabs/Health-report.page.vue"),
        },
        {
          id: CYM_REPORT_VARIABLE.CARE_CALENDAR,
          value: "care-calendar",
          componentLoader: () => import("./tabs/Care-calendar.page.vue"),
        },
      ],
      tabs: [
        {
          id: CYM_REPORT_VARIABLE.HEALTH_REPORT,
          name: "건강리포트",
          href: "#health-report",
        },
        {
          id: CYM_REPORT_VARIABLE.CARE_CALENDAR,
          name: "케어 캘린더",
          href: "#care-calendar",
        },
      ],
    };
  },
  computed: {
    currentTabItem() {
      return this.tabItems.find((item) => `#${item.value}` === this.currentTab);
    },
    currentComponent() {
      const tabValue = this.currentTab.replace("#", "");
      return this.loadedComponents[tabValue] || null;
    },
  },
  async mounted() {
    // 초기 탭 컴포넌트 로드
    await this.loadTabComponent(this.currentTab);
  },
  methods: {
    async handleChangeTab(newTab) {
      this.currentTab = newTab;
      await this.loadTabComponent(newTab);
    },
    async loadTabComponent(tabValue) {
      const cleanTabValue = tabValue.replace("#", "");

      // 이미 로드된 컴포넌트인지 확인
      if (this.loadedComponents[cleanTabValue]) {
        return;
      }

      const tabItem = this.tabItems.find(
        (item) => item.value === cleanTabValue
      );
      if (tabItem && tabItem.componentLoader) {
        try {
          const module = await tabItem.componentLoader();
          this.$set(this.loadedComponents, cleanTabValue, module.default);
        } catch (error) {
          console.error(
            `Failed to load component for tab: ${cleanTabValue}`,
            error
          );
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.tab-content {
  min-height: calc(100vh - 220px);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background: white;
}
</style>
