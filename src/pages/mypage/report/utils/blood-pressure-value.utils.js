function getBloodPressureMinMax(data) {
  if (!data || data.length === 0) {
    return {
      systolicMin: undefined,
      systolicMax: undefined,
      diastolicMin: undefined,
      diastolicMax: undefined,
    };
  }

  let systolicMin = Infinity;
  let systolicMax = -Infinity;
  let diastolicMin = Infinity;
  let diastolicMax = -Infinity;

  for (const entry of data) {
    if (entry.systolic < systolicMin) {
      systolicMin = entry.systolic;
    }
    if (entry.systolic > systolicMax) {
      systolicMax = entry.systolic;
    }
    if (entry.diastolic < diastolicMin) {
      diastolicMin = entry.diastolic;
    }
    if (entry.diastolic > diastolicMax) {
      diastolicMax = entry.diastolic;
    }
  }

  return {
    systolicMin,
    systolicMax,
    diastolicMin,
    diastolicMax,
  };
}

export { getBloodPressureMinMax };
