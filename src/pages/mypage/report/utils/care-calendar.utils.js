/**
 *
 * @param {Array} data
 * @param {Boolean} isIos
 *
 */
export function progressCareCalendarCalculate(data, isIos) {
  const result = [];
  // Get today's date and set its time to 00:00:00 for accurate comparison
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  for (let i = 0, len = data.length; i < len; i++) {
    const { date, urine, water, weight, bloodPressure, step, woman } = data[i];

    // Create a Date object from the current data's date string
    const dataDate = new Date(date);
    dataDate.setHours(0, 0, 0, 0); // Normalize time to 00:00:00

    // Check if the current date is before today
    if (dataDate > today) {
      // If the date is in the past, stop the loop.
      // This will break the loop at the first occurrence of a past date.
      break;
    }

    let p = 0;
    if (urine && urine.length !== 0) p++;
    if (water && water.length !== 0) p++;
    if (weight && weight.length !== 0) p++;
    if (bloodPressure && bloodPressure.length !== 0) p++;
    if (!isIos) {
      // 체 수 배 혈 걸
      if (step && step !== null) p++;
    }
    const total = isIos ? 4 : 5;

    const progress = Math.round((p / total) * 100);

    result.push({
      date,
      progress,
    });
  }

  return result;
}
/**
 * @typedef {Object} CareData
 * @property {Array} weight - 체중 데이터 배열
 * @property {Array} water - 수분 데이터 배열
 * @property {Array} urine - 소변 데이터 배열
 * @property {Array} bloodPressure - 혈압 데이터 배열
 * @property {Object|null} step - 걸음수 데이터
 * @property {string|null} woman - 여성 케어 데이터
 */

/**
 * @typedef {Object} ApiResponse
 * @property {Array<CareData>} data - 케어 데이터 배열
 * @property {Object|null} menstruation - 생리 데이터
 */

/**
 * @typedef {Object} DetailDataResult
 * @property {CareData} data - 특정 날짜의 케어 데이터
 * @property {Object|null} menstruation - 생리 데이터
 */

/**
 * 특정 날짜의 상세 케어 데이터를 추출합니다
 * @param {string} date - YYYY-MM-DD 형식의 날짜
 * @param {ApiResponse|Array} responseData - API 응답 데이터
 * @returns {DetailDataResult} 상세 데이터 객체
 */
/**
 * API 응답 데이터가 유효한 형식인지 검증합니다
 * @param {any} data - 검증할 데이터
 * @returns {boolean} 유효성 여부
 */
function isValidApiResponse(data) {
  return (
    data &&
    typeof data === "object" &&
    Array.isArray(data.data) &&
    data.data.length >= 0
  );
}

/**
 * 배열 데이터인지 검증합니다
 * @param {any} data - 검증할 데이터
 * @returns {boolean} 배열 여부
 */
function isValidArray(data) {
  return Array.isArray(data) && data.length >= 0;
}

/**
 * 날짜 문자열이 유효한 형식인지 검증합니다
 * @param {any} date - 검증할 날짜
 * @returns {boolean} 유효성 여부
 */
function isValidDateString(date) {
  return typeof date === "string" && /^\d{4}-\d{2}-\d{2}$/.test(date);
}

/**
 * 기본 케어 데이터 구조를 반환합니다
 * @param {Object|null} menstruation - 생리 데이터
 * @returns {DetailDataResult} 기본 데이터 구조
 */
function getDefaultCareData(menstruation = null) {
  return {
    data: {
      weight: [],
      water: [],
      urine: [],
      bloodPressure: [],
      step: null,
      woman: null,
    },
    menstruation: menstruation,
  };
}

export function detailCareCalendarData(date, responseData) {
  // 입력 매개변수 검증
  if (!isValidDateString(date)) {
    console.error("Invalid date format:", date);
    return getDefaultCareData();
  }

  if (!responseData) {
    console.warn("No response data provided");
    return getDefaultCareData();
  }

  // responseData가 배열인지 객체인지 확인
  const dataArray = isValidArray(responseData)
    ? responseData
    : responseData.data;
  const menstruation = responseData.menstruation || null;

  // 데이터 배열 유효성 검증
  if (!isValidArray(dataArray)) {
    console.warn("Invalid data format:", responseData);
    return getDefaultCareData(menstruation);
  }

  // 해당 날짜의 데이터 검색
  for (const dateData of dataArray) {
    if (!dateData || typeof dateData !== "object") {
      console.warn("Invalid date data item:", dateData);
      continue;
    }

    if (date === dateData.date) {
      const { progress, ...detail } = dateData;

      // 반환 데이터 구조 검증
      const validatedDetail = {
        weight: isValidArray(detail.weight) ? detail.weight : [],
        water: isValidArray(detail.water) ? detail.water : [],
        urine: isValidArray(detail.urine) ? detail.urine : [],
        bloodPressure: isValidArray(detail.bloodPressure)
          ? detail.bloodPressure
          : [],
        step: detail.step || null,
        woman: detail.woman || null,
      };

      return {
        data: validatedDetail,
        menstruation: menstruation,
      };
    }
  }

  // 해당 날짜의 데이터를 찾지 못한 경우 기본값 반환
  console.info(`No data found for date: ${date}`);
  return getDefaultCareData(menstruation);
}
