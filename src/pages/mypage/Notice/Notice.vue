<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div class="notice__wrapper">
      <!-- {{ noticeList[0].content }} -->
      <!-- <div v-html="this.noticeList[0].content"></div> -->
      <div class="card__wrapper">
        <div
          class="notice-card"
          v-for="(notice, idx) in noticeContents"
          :key="idx"
          @click="clickToFold(idx)"
        >
          <div class="notice-card-header">
            <div class="notice-card-title" v-html="notice.title"></div>
            <div :class="notice.isOpen ? 'unfold' : 'fold'">
              <img class="icon" src="@/assets/images/bottom-arrow.png" />
            </div>
          </div>
          <div class="notice-sub-content">
            <div class="notice-date">{{ notice.date }}</div>
          </div>
          <div v-if="notice.isOpen">
            <div class="notice-card-text">
              <div class="img__wrapper" v-html="notice.content"></div>
              <div
                v-if="notice.buttonText"
                class="link-button"
                :class="lang === 'ko' ? '' : 'en-title'"
                @click="openBrowser(notice.buttonLink)"
              >
                {{ notice.buttonText }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
// import API from "@/api/auth/index";
// import axios from "axios";

export default {
  name: "MypageNotices",
  components: { HeaderNav },
  data() {
    return {
      pageName: this.$i18n.t("category_notice"),
      noticeImg: "",
      lang: "ko",
      noticeList: [],
      noticeContents: [],
    };
  },

  methods: {
    clickToFold(idx) {
      // console.log("clicked!", idx);
      this.clicked = !this.clicked;
      this.noticeContents.map((i, index) => {
        idx !== index ? (i.isOpen = false) : null;
      });
      this.noticeContents[idx].isOpen = !this.noticeContents[idx].isOpen;
    },
    openBrowser(url) {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      // console.log(url);
      const bridge_msg = {
        action: "goCym702",
        // url: "https://smartstore.naver.com/cym702",
        url: url,
      };
      Webview.goCym702(bridge_msg);
    },
  },
  mounted() {
    this.lang = this.$i18n.locale === "ko" ? "ko" : "en";
    // this.getNoticeList();
    // this.makeArr();
    this.noticeContents =
      this.$i18n.locale === "ko"
        ? this.$i18n.messages.ko.notice.ko.reverse()
        : this.$i18n.messages.ko.notice.en.reverse();
    // console.log(this.$i18n.messages.ko.notice);
  },
};
</script>

<style lang="scss" scoped>
.notice__wrapper {
  width: 100%;
  text-align: left;
}

.card__wrapper {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  padding: 40px 30px;
}

.notice-card {
  width: 100%;
  margin-bottom: 20px;
}

.notice-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.notice-card-title {
  width: 95%;
  font-size: 18px;
  font-weight: 500;
  color: #000000;
  letter-spacing: -0.03em;
}
.notice-date {
  color: #646464;
  font-weight: 400;
  font-size: 12px;
  margin: 10px 0;
}

.notice-sub-content {
  border-bottom: 0.5px solid #a7a7a7;
}

.notice-card-content {
  display: none;
}

.fold {
  width: 12px;
  display: flex;
}

img {
  width: 100% !important;
}

.unfold {
  width: 12px;
  transform: rotate(180deg);
}

.notice-card-text {
  color: #000000;
  font-size: 16px;
  margin-top: 20px;
  letter-spacing: -0.03em;
  img {
    width: 100% !important;
  }
}

.img__wrapper {
  width: 100%;
  padding: 15px 0;
}

.notice-contents {
  /* padding-bottom: 50px; */
}

.sub-contents {
  padding: 20px 0;
}

.pic__wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding-top: 20px;
  gap: 15px;
}

.link-button {
  width: 100%;
  height: 50px;
  background-color: #41d8e6;
  color: #fff;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 700;
  font-size: 20px;
  margin-bottom: 50px;
}
</style>
