<template>
  <div>
    <div v-if="loaded" class="container">
      <div class="contents__wrapper">
        <GenderForm :initialGender="initialGender" @genderInputHandler="genderInputHandler" />
        <!-- <div class="name-form__wrapper">
          <NameForm :name="name" />
        </div> -->
        <div class="form__wrapper">
          <BirthForm :initialBirth="initialBirth" @birthInputHandler="birthInputHandler" />
        </div>
        <div class="form__wrapper">
          <HeightForm :initialHeight="initialHeight" @heightInputHandler="heightInputHandler" />
        </div>
        <!-- <div class="form__wrapper">
          <WeightForm :weight="weight" />
        </div> -->
        <div class="form__wrapper">
          <WeightGoalForm :initialWeight="initialWeight" @weightGoalInputHandler="weightGoalInputHandler" />
        </div>
        <div class="form__wrapper">
          <WaterGoalFormVue :initialWater="initialWater" @waterGoalInputHandler="waterGoalInputHandler" />
        </div>
        <div class="form__wrapper">
          <KetoneModeForm :initialKetoneMode="initialKetoneMode" @ketoneModeHandler="ketoneModeHandler" />
        </div>
      </div>
      <div class="save-btn__wrapper">
        <v-btn
          elevation="0"
          color="#41D8E6"
          type="submit"
          :disabled="!isValid"
          @click="submitHandler"
          class="save-btn"
          >{{ $t("save") }}</v-btn
        >
      </div>
    </div>
    <Loading v-if="loading" />
    <div class="snackbar">
      <v-snackbar v-model="saveSuccess" timeout="2000">{{ succesContent }}</v-snackbar>
      <v-snackbar v-model="saveFailed" timeout="2000" color="#EE0000">{{ failContent }}</v-snackbar>
    </div>
  </div>
</template>

<script>
import GenderForm from "@/components/Forms/GenderForm.vue";
// import NameForm from "@/components/Forms/NameForm.vue";
import BirthForm from "@/components/Forms/BirthForm.vue";
import HeightForm from "@/components/Forms/HeightForm.vue";
// import WeightForm from "@/components/Forms/WeightForm.vue";
import WeightGoalForm from "@/components/Forms/WeightGoalForm.vue";
import WaterGoalFormVue from "@/components/Forms/WaterGoalForm.vue";
import KetoneModeForm from "@/components/Forms/KetoneModeForm.vue";
import { getSubjectId } from "@/components/Common/getSubjectId.js";

import { fetchGetSubjectInfo, updateSubjectInfo } from "@/api/user/index";
import Loading from "@/components/Common/Loading.vue";

export default {
  components: {
    GenderForm,
    // NameForm,
    BirthForm,
    HeightForm,
    // WeightForm,
    WeightGoalForm,
    WaterGoalFormVue,
    KetoneModeForm,
    Loading,
  },

  data() {
    return {
      loading: true,
      loaded: false,
      saveSuccess: false,
      saveFailed: false,
      succesContent: this.$i18n.t("save_success"),
      failContent: this.$i18n.t("save_fail"),
      initialGender: "",
      gender: "",
      initialBirth: "",
      birth: "",
      birthValid: false,
      initialHeight: 0,
      height: 0,
      heightValid: false,
      initialWeight: 0,
      goalWeight: 0,
      weightValid: false,
      initialWater: 0,
      goalWater: 0,
      initialKetoneMode: false,
      ketoneMode: false,
    };
  },
  methods: {
    async getBasicData() {
      try {
        const subjectId = getSubjectId();
        const { data } = await fetchGetSubjectInfo(subjectId);
        // console.log(data.subject);
        const userData = data.subject;
        localStorage.setItem("ketoneMode", userData.ketoneMode);
        this.initialGender = userData.sex;
        this.gender = userData.sex;
        this.initialHeight = userData.metadata.height || 0;
        this.height = userData.metadata.height || 0;
        this.initialWeight = userData.targetWeight || 0;
        this.goalWeight = userData.targetWeight || 0;
        this.initialWater = userData.targetWater;
        this.goalWater = userData.targetWater;
        this.initialBirth = userData.birth;
        this.birth = userData.birth;
        this.initialKetoneMode = userData.ketoneMode;
        this.ketoneMode = userData.ketoneMode;
        this.loading = false;
        this.loaded = true;
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        } else {
          this.loading = false;
          this.loaded = true;
        }
      }
    },

    genderInputHandler(gender) {
      // console.log(gender);
      this.gender = gender;
    },

    birthInputHandler(inputBirth) {
      // console.log(inputBirth);
      this.birth = inputBirth.birth;
      this.birthValid = inputBirth.valid;
    },

    heightInputHandler(inputHeight) {
      // console.log(inputHeight);
      this.height = inputHeight.height;
      this.heightValid = inputHeight.valid;
    },

    weightGoalInputHandler(inputGoalWeight) {
      // console.log(inputGoalWeight);
      this.goalWeight = inputGoalWeight.goal;
      this.weightValid = inputGoalWeight.valid;
    },

    waterGoalInputHandler(goalWater) {
      // console.log(goalWater);
      this.goalWater = goalWater;
    },

    ketoneModeHandler(ketoneMode) {
      // console.log(ketoneMode);
      this.ketoneMode = ketoneMode;
    },

    resetData() {
      this.initialGender = "";
      this.gender = "";
      this.initialBirth = "";
      this.birth = "";
      this.birthValid = false;
      this.initialHeight = 0;
      this.height = 0;
      this.heightValid = false;
      this.initialWeight = 0;
      this.goalWeight = 0;
      this.weightValid = false;
      this.initialWater = 0;
      this.goalWater = 0;
      this.initialKetoneMode = false;
      this.ketoneMode = false;
      this.getBasicData();
    },

    submitHandler() {
      // console.log("submit");
      const userData = {};
      this.gender !== this.initialGender ? (userData.sex = this.gender) : null;
      this.birth !== this.initialBirth && this.birthValid ? (userData.birth = this.birth) : null;
      this.height !== this.initialHeight && this.heightValid ? (userData.height = Number(this.height)) : null;
      this.goalWeight !== this.initialWeight && this.weightValid
        ? (userData.targetWeight = Number(this.goalWeight))
        : null;
      this.goalWater !== this.initialWater ? (userData.targetWater = Number(this.goalWater)) : null;
      this.ketoneMode !== this.initialKetoneMode ? (userData.ketoneMode = this.ketoneMode) : null;
      // console.log(userData);
      this.updateUserData(userData);
    },

    async updateUserData(userData) {
      try {
        const subjectId = getSubjectId();
        const { data, status } = await updateSubjectInfo(subjectId, userData);
        // console.log(data, status);
        if (status === 200) {
          this.saveSuccess = true;
          this.resetData();
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        } else this.saveFailed = true;
      }
    },
  },
  computed: {
    isValid() {
      return (
        this.gender !== this.initialGender ||
        (this.birth !== this.initialBirth && this.birthValid) ||
        (Number(this.height) !== this.initialHeight && this.heightValid) ||
        (Number(this.goalWeight) !== this.initialWeight && this.weightValid) ||
        Number(this.goalWater) !== this.initialWater ||
        this.ketoneMode !== this.initialKetoneMode
      );
    },
  },
  mounted() {
    this.loading = true;
    this.getBasicData();
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  padding: 40px 0;
  display: flex;
}

.contents__wrapper {
  height: 70vh;
  overflow: auto;
  padding: 0 30px 200px;
}

.form__wrapper {
  margin-bottom: 40px;
}
.name-form__wrapper {
  margin-top: 30px;
}

.save-btn__wrapper {
  width: 100%;
  position: fixed;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 20.83%, #ffffff 100%);
  bottom: 0;
  height: calc(5vh + 90px);
  left: 0;
  padding: 30px 30px 0px 30px;
}

.save-btn {
  width: 100%;
  max-width: 390px !important;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}

::v-deep .v-text-field {
  margin: 0;
  padding: 0;
}
::v-deep .v-text-field input {
  padding: 5px 0 5px;
}
</style>
