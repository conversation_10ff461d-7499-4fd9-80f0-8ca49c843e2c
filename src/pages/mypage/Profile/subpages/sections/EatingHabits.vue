<template>
  <div class="container">
    <div class="contents__wrapper">
      <div class="diet-title">{{ $t("diet_answer") }}</div>
      <div class="checkbox" id="first-option">
        <v-checkbox v-model="isGeneral" off-icon="$check_box" on-icon="$check_box_inactive" color="#41D8E6">
          <template v-slot:label>
            <div class="checkbox-label">
              {{ $t("general_diet") }}
            </div>
          </template>
        </v-checkbox>
      </div>
      <!-- </div> -->
      <div class="form-title" :class="{ 'disabled-checkbox': isGeneral }">
        {{ $t(calorieArr.text) }}<span class="multi-title"> {{ $t("multi_desc") }}</span>
      </div>
      <div class="checkbox">
        <v-checkbox
          v-for="list in calorieArr.choice"
          :key="list.id"
          color="#41d8e6"
          off-icon="$check_box"
          on-icon="$check_box_inactive"
          :multiple="calorieArr.isMultipleChoice"
          v-model="checkedCalorie"
          v-show="list.status === 'active'"
          :value="list.id"
          :disabled="isGeneral"
          :class="{ 'disabled-checkbox': isGeneral }"
        >
          <template v-slot:label>
            <div class="checkbox-label">{{ $t(list.text) }}</div>
          </template>
        </v-checkbox>
      </div>
      <div class="form-title" :class="{ 'disabled-checkbox': isGeneral }">
        {{ $t(vegeArr.text) }}<span class="multi-title"> {{ $t("single_desc") }}</span>
      </div>
      <div class="checkbox">
        <v-checkbox
          color="#41d8e6"
          off-icon="$check_box"
          v-for="list in vegeArr.choice"
          :key="list.id"
          :multiple="vegeArr.isMultipleChoice"
          on-icon="$check_box_inactive"
          v-model="checkedVege"
          v-show="list.status === 'active'"
          :label="list.text"
          :value="list.id"
          :disabled="isGeneral"
          :class="{ 'disabled-checkbox': isGeneral }"
        >
          <template v-slot:label>
            <div class="checkbox-label">{{ $t(list.text) }}</div>
          </template>
        </v-checkbox>
      </div>
      <div class="form-title" :class="{ 'disabled-checkbox': isGeneral }">
        {{ $t(careArr.text) }} <span class="multi-title">{{ $t("multi_desc") }}</span>
      </div>
      <div class="checkbox">
        <v-checkbox
          color="#41d8e6"
          off-icon="$check_box"
          v-for="list in careArr.choice"
          :key="list.id"
          :multiple="careArr.isMultipleChoice"
          on-icon="$check_box_inactive"
          v-model="checkedCare"
          v-show="list.status === 'active'"
          :label="list.text"
          :value="list.id"
          :disabled="isGeneral"
          :class="{ 'disabled-checkbox': isGeneral }"
        >
          <template v-slot:label>
            <div class="checkbox-label">{{ $t(list.text) }}</div>
          </template>
        </v-checkbox>
      </div>
      <div class="meal-category-title" @click="showTextAreaHandler" :class="{ 'disabled-checkbox': isGeneral }">
        {{ $t("diet_input_etc") }}
        <span class="opinion-comment-text">{{ $t("diet_input_etc_desc") }}</span>
      </div>
      <template v-if="showTextArea">
        <v-textarea
          outlined
          color="#41D8E6"
          :placeholder="this.$i18n.t('diet_input_etc_placeholder')"
          minlength="5"
          maxlength="50"
          v-model="opinion"
          :disabled="isGeneral"
          :class="{ 'disabled-checkbox': isGeneral }"
        ></v-textarea>
      </template>
    </div>
    <div class="save-btn__wrapper">
      <v-btn color="#41D8E6" :disabled="!isValid" elevation="0" @click="submitHandler" class="save-btn">
        {{ $t("save") }}
      </v-btn>
    </div>
    <div class="snackbar">
      <v-snackbar v-model="saveSuccess" timeout="2000">{{ successContent }}</v-snackbar>
      <v-snackbar v-model="successFail" timeout="2000" color="#EE0000">{{ failContent }}</v-snackbar>
    </div>
  </div>
</template>

<script>
import API from "@/api/survey/index.js";

export default {
  props: { eatingHabits: Array },

  data() {
    return {
      saveSuccess: false,
      successContent: this.$i18n.t("save_success"),
      successFail: false,
      failContent: this.$i18n.t("save_fail"),
      initialIsGeneral: false,
      isGeneral: false,
      calorieArr: [],
      initialCalorieAnswers: [],
      checkedCalorie: [],
      vegeArr: [],
      initialVegeAnswer: null,
      checkedVege: null,
      careArr: [],
      initialCareAnswers: [],
      checkedCare: [],
      initialOpinion: "",
      opinion: "",
      initialShowTextArea: false,
      showTextArea: false,
    };
  },

  watch: {
    checkedCalorie(newVal) {
      // console.log("checkedCalorie", newVal);
      // this.initialCalorieAnswers.includes(32);
    },
    checkedVege(newVal) {
      // console.log("checkedVege", newVal);
    },
    checkedCare(newVal) {
      // console.log("checkedCare", newVal);
    },
    questions() {
      this.getSurveyCheckedData();
    },
    isGeneral(newVal) {
      // this.opinionlValid();
      // this.calorieAnswerValid();
      // this.vegeAnswerValid();
      // this.careAnswerValid();
      // console.log(newVal);
      // newVal
      //   ? ((this.checkedCalorie = [32]), (this.checkedVege = 36), (this.checkedCare = [41]), (this.opinion = ""))
      //   : ((this.checkedCalorie = []), (this.checkedVege = null), (this.checkedCare = []));
    },
    eatingHabits(newVal) {
      // console.log(newVal);
      this.makeQuestion();
    },
  },
  computed: {
    isValid() {
      // return this.opinionlValid();
      return (
        this.opinionlValid() || this.calorieAnswerValid() || this.vegeAnswerValid() || this.careAnswerValid()
        // &&
        // this.nonCheckValid()
      );
    },
  },
  methods: {
    nonCheckValid() {
      if (!this.isGeneral) {
        return this.checkedCalorie.length !== 0 || this.checkedVege !== null || this.checkedCare.length !== 0;
      }
    },
    opinionlValid() {
      if (this.isGeneral) {
        return this.initialIsGeneral !== this.isGeneral;
      } else {
        if (this.showTextArea) {
          return (
            this.initialShowTextArea !== this.showTextArea ||
            (this.opinion.length > 4 && this.initialOpinion !== this.opinion)
          );
        } else return this.initialShowTextArea !== this.showTextArea;
      }
    },
    calorieAnswerValid() {
      if (!this.isGeneral) {
        if (this.checkedCalorie.length !== 0) {
          if (this.initialCalorieAnswers.length !== this.checkedCalorie.length) {
            return true;
          } else {
            return (
              this.initialCalorieAnswers.some((value, index) => value !== this.checkedCalorie[index]) &&
              this.checkedCalorie.length !== 0
            );
          }
        } else return this.checkedVege !== null || this.checkedCare.length !== 0;
      } else {
        return this.initialIsGeneral !== this.isGeneral;
      }
    },
    vegeAnswerValid() {
      if (!this.isGeneral) {
        return this.initialVegeAnswer !== this.checkedVege;
        // && this.checkedVege !== null;
      } else {
        return this.initialIsGeneral !== this.isGeneral;
      }
    },
    careAnswerValid() {
      if (!this.isGeneral) {
        if (this.checkedCare.length !== 0) {
          if (this.initialCareAnswers.length !== this.checkedCare.length) {
            return true;
          } else {
            return (
              this.initialCareAnswers.some((value, index) => value !== this.checkedCare[index]) &&
              this.checkedCare.length !== 0
            );
          }
        } else return this.checkedCalorie.length !== 0 || this.checkedCare.length !== 0;
      } else {
        return this.initialIsGeneral !== this.isGeneral;
      }
    },
    submitHandler() {
      const createAnswers = [];
      const deleteAnswers = [];

      if (this.isGeneral) {
        // 기존 선택값이 있었으면,
        // create (this.checkedCalorie = [32]), (this.checkedVege = 36), (this.checkedCare = [41])
        // delete 기존 값

        // 칼로리 조절식
        if (!this.initialCalorieAnswers.includes(32)) {
          createAnswers.push({ questionId: 6, choiceAnswers: [32] });
          this.initialCalorieAnswers.length !== 0
            ? deleteAnswers.push({ questionId: 6, choiceAnswers: this.initialCalorieAnswers })
            : null;
        } else if (this.initialCalorieAnswers.includes(32)) {
          this.checkedCalorie = this.checkedCalorie.filter((id) => id !== 32);
          // console.log(this.checkedCalorie);
          this.checkedCalorie.length !== 0
            ? deleteAnswers.push({ questionId: 6, choiceAnswers: this.checkedCalorie })
            : null;
        }
        // 채식
        if (this.initialVegeAnswer !== 36) {
          createAnswers.push({ questionId: 7, choiceAnswers: [36] });
          this.initialVegeAnswer !== null
            ? deleteAnswers.push({ questionId: 7, choiceAnswers: [this.initialVegeAnswer] })
            : null;
        } else {
          // if (this.checkedVege !== null) {
          //   deleteAnswers({ questionId: 7, choiceAnswers: [36] });
          //   createAnswers.push({ questionId: 7, choiceAnswers: [36] });
          // }
        }
        // 케어식
        if (!this.initialCareAnswers.includes(41)) {
          // console.log(this.initialCareAnswers);
          createAnswers.push({ questionId: 8, choiceAnswers: [41] });
          this.initialCareAnswers.length !== 0
            ? deleteAnswers.push({ questionId: 8, choiceAnswers: this.initialCareAnswers })
            : null;
        } else if (this.initialCareAnswers.includes(41)) {
          this.checkedCare = this.checkedCare.filter((id) => id !== 41);
          this.checkedCare.length !== 0 ? deleteAnswers.push({ questionId: 8, choiceAnswers: this.checkedCare }) : null;
        }
        // 기타 의견
        if (this.initialOpinion !== "") {
          deleteAnswers.push({ questionId: 9, textAnswer: this.initialOpinion });
        }
      } else {
        // 일반식 선택 X
        // 모든 항목은 빈 값일 수 없다 -> '없음'이라는 항목이 존재하기 때문(보여지지만 않을 뿐)
        // initial이 없음이면 = 32, 36, 41 비교해서 바뀌었으면 32, 36, 41 삭제 & 선택값 생성

        // 칼로리 조절식
        if (this.calorieAnswerValid()) {
          if (!this.initialCalorieAnswers.includes(32)) {
            if (this.checkedCalorie.length === 0) {
              createAnswers.push({ questionId: 6, choiceAnswers: [32] });
              deleteAnswers.push({ questionId: 6, choiceAnswers: this.initialCalorieAnswers });
            } else {
              const addedCalorieAnswers = this.checkedCalorie.filter(
                (choiceId) => !this.initialCalorieAnswers.includes(choiceId)
              );
              const deletedCalorieAnswers = this.initialCalorieAnswers.filter(
                (choiceId) => !this.checkedCalorie.includes(choiceId)
              );
              addedCalorieAnswers.length !== 0
                ? createAnswers.push({ questionId: 6, choiceAnswers: addedCalorieAnswers })
                : null;
              deletedCalorieAnswers.length !== 0
                ? deleteAnswers.push({ questionId: 6, choiceAnswers: deletedCalorieAnswers })
                : null;
            }
          } else if (this.initialCalorieAnswers.includes(32)) {
            // console.log(this.checkedCalorie.length >= 2);
            if (this.initialCalorieAnswers.length === 1) {
              if (this.checkedCalorie.length !== 0) {
                this.checkedCalorie = this.checkedCalorie.filter((id) => id !== 32);
                this.checkedCalorie.length !== 0
                  ? createAnswers.push({ questionId: 6, choiceAnswers: this.checkedCalorie })
                  : null;
                deleteAnswers.push({ questionId: 6, choiceAnswers: [32] });
              }
              // console.log(this.checkedCalorie);
            }
          }
        }

        // 채식
        if (this.vegeAnswerValid()) {
          // 일반식 아닐때
          if (this.initialVegeAnswer === 36) {
            // 없음 이라면
            if (this.checkedVege !== null) {
              createAnswers.push({ questionId: 7, choiceAnswers: [this.checkedVege] });
              deleteAnswers.push({ questionId: 7, choiceAnswers: [36] });
            }
          } else {
            // 없음 선택이 아니라면
            if (this.checkedVege !== null) {
              createAnswers.push({ questionId: 7, choiceAnswers: [this.checkedVege] });
              deleteAnswers.push({ questionId: 7, choiceAnswers: [this.initialVegeAnswer] });
            } else {
              createAnswers.push({ questionId: 7, choiceAnswers: [36] });
              deleteAnswers.push({ questionId: 7, choiceAnswers: [this.initialVegeAnswer] });
            }
          }
        }

        // 케어식
        // console.log(this.careAnswerValid());
        if (this.careAnswerValid()) {
          if (!this.initialCareAnswers.includes(41)) {
            if (this.checkedCare.length === 0) {
              createAnswers.push({ questionId: 8, choiceAnswers: [41] });
              deleteAnswers.push({ questionId: 8, choiceAnswers: this.initialCareAnswers });
            } else {
              const addedCareAnswers = this.checkedCare.filter(
                (choiceId) => !this.initialCareAnswers.includes(choiceId)
              );
              const deletedCareAnswers = this.initialCareAnswers.filter(
                (choiceId) => !this.checkedCare.includes(choiceId)
              );
              addedCareAnswers.length !== 0
                ? createAnswers.push({ questionId: 8, choiceAnswers: addedCareAnswers })
                : null;
              deletedCareAnswers.length !== 0
                ? deleteAnswers.push({ questionId: 8, choiceAnswers: deletedCareAnswers })
                : null;
            }
          } else if (this.initialCareAnswers.includes(41)) {
            if (this.initialCareAnswers.length === 1) {
              if (this.checkedCare.length !== 0) {
                this.checkedCare = this.checkedCare.filter((id) => id !== 41);
                this.checkedCare.length !== 0
                  ? createAnswers.push({ questionId: 8, choiceAnswers: this.checkedCare })
                  : null;
                deleteAnswers.push({ questionId: 8, choiceAnswers: [41] });
              }
            }
          }
          // console.log("------", addedCareAnswers, deletedCareAnswers);
        }

        // 기타의견
        if (this.opinionlValid) {
          // console.log("etc opinion");
          if (this.isGeneral) {
            // console.log("is general");
            this.initialOpinion !== "" ? deleteAnswers.push({ questionId: 9, textAnswer: this.initialOpinion }) : null;
          } else {
            // console.log("isn't general");
            // console.log(this.showTextArea, this.initialOpinion);
            if (this.showTextArea && this.initialOpinion !== "") {
              if (this.opinion.length > 4 && this.initialOpinion !== this.opinion) {
                createAnswers.push({ questionId: 9, textAnswer: this.opinion });
              }
            } else if (this.showTextArea && this.initialOpinion === "") {
              createAnswers.push({ questionId: 9, textAnswer: this.opinion });
            }
          }
        }
      }
      // console.log("create Answers:", createAnswers);
      // console.log("delete Answers:", deleteAnswers);
      const subjectId = localStorage.getItem("subjectId");
      const reqBody = { subjectId: Number(subjectId) };
      createAnswers.length !== 0 ? (reqBody.createAnswers = createAnswers) : null;
      deleteAnswers.length !== 0 ? (reqBody.deleteAnswers = deleteAnswers) : null;
      // console.log(Number(subjectId), reqBody);
      this.putSurveyData(reqBody);
    },

    async putSurveyData(body) {
      try {
        const { data, status } = await API.putSurveyData(body);
        // console.log(data, status);
        if (status === 201) {
          this.saveSuccess = true;
          this.resetData();
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        } else this.successFail = true;
      }
    },

    resetData() {
      this.initialIsGeneral = false;
      this.isGeneral = false;
      this.calorieArr = [];
      this.initialCalorieAnswers = [];
      this.checkedCalorie = [];
      this.vegeArr = [];
      this.initialVegeAnswer = null;
      this.checkedVege = null;
      this.careArr = [];
      this.initialCareAnswers = [];
      this.checkedCare = [];
      this.initialOpinion = "";
      this.opinion = "";
      this.initialShowTextArea = false;
      this.showTextArea = false;
      this.$emit("reloadSurveyData");
    },

    makeQuestion() {
      this.calorieArr = this.eatingHabits[0];
      this.eatingHabits[0].choice.map((q) => {
        if (q.choiceAnswer.length !== 0) {
          this.initialCalorieAnswers.push(q.id);
          this.checkedCalorie.push(q.id);
        }
      });
      this.vegeArr = this.eatingHabits[1];
      this.eatingHabits[1].choice.map((q) => {
        if (q.choiceAnswer.length !== 0) {
          // console.log("vege", q.id);
          this.initialVegeAnswer = q.id;
          this.checkedVege = q.id;
        }
      });
      this.careArr = this.eatingHabits[2];
      this.eatingHabits[2].choice.map((q) => {
        if (q.choiceAnswer.length !== 0) {
          this.initialCareAnswers.push(q.id);
          this.checkedCare.push(q.id);
        }
      });
      // console.log(this.eatingHabits[3].textAnswer[0]?.text);
      if (this.eatingHabits[3].textAnswer[0]?.text !== undefined) {
        this.initialShowTextArea = true;
        this.showTextArea = true;
        this.initialOpinion = this.eatingHabits[3].textAnswer[0]?.text;
        this.opinion = this.eatingHabits[3].textAnswer[0]?.text;
      }
      // this.checkedCalorie.includes(32) &&
      JSON.stringify(this.checkedCalorie) === JSON.stringify([32]) &&
      this.checkedVege === 36 &&
      JSON.stringify(this.checkedCare) === JSON.stringify([41]) &&
      this.opinion === ""
        ? ((this.isGeneral = true), (this.initialIsGeneral = true))
        : ((this.isGeneral = false), (this.isGeneral = false));
      // console.log("calorie answers:", this.checkedCalorie);
      // console.log("vege answers:", this.checkedVege);
      // console.log("care answers", this.checkedCare);
    },
    showTextAreaHandler() {
      if (!this.isGeneral) this.showTextArea = !this.showTextArea;
    },
  },
  mounted() {
    // console.log(this.eatingHabits);
    this.makeQuestion();
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  padding: 40px 0;
  display: flex;
  flex-direction: column;
}
.contents__wrapper {
  width: 100%;
  height: 70vh;
  overflow: auto;
  text-align: left;
  padding: 0 30px 150px;
}

.diet-title {
  color: #323232;
  text-align: left;
  font-weight: 500;
  font-size: 20px !important;
}

.multi-title {
  font-size: 16px;
  color: #646464;
  margin-bottom: 10px;
}

.form-title {
  font-weight: 500;
  font-size: 20px;
  margin: 20px 0;
}

.save-btn__wrapper {
  width: 100%;
  position: fixed;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 20.83%, #ffffff 100%);
  bottom: 0;
  height: calc(5vh + 90px);
  left: 0;
  padding: 30px 30px 0px 30px;
}

.save-btn {
  width: 100%;
  max-width: 390px !important;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
  color: #c9f4f8 !important;
}
.v-input--selection-controls {
  margin: 0px !important;
  padding: 0px !important;
}
::v-deep .v-input__control {
  display: block !important;
  flex-wrap: nowrap !important;
  margin: 5px 0;
  padding-left: 10px;
}

::v-deep .v-text-field {
  margin: 0;
  padding: 0;
}
::v-deep .v-text-field input {
  padding: 5px 0 5px;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
  color: #c9f4f8 !important;
}
.v-input--selection-controls {
  margin: 0px !important;
  padding: 0px !important;
}
::v-deep .v-input__control {
  display: block !important;
  flex-wrap: nowrap !important;
  margin: 5px 0;
}

::v-deep .v-messages {
  display: none !important;
}

.checkbox {
  margin: 5px 0;
}

#first-option {
  padding-top: 15px;
}

.checkbox-label {
  font-weight: 400;
  font-size: 20px !important;
  color: black;
  padding-bottom: 5px;
  letter-spacing: -0.03em;
  line-height: 26px;
}

.meal-category-title {
  text-align: left;
  font-weight: 500;
  font-size: 20px;
  line-height: 26px;
  /* identical to box height */
  letter-spacing: -0.03em;
  color: #000000;
  padding: 10px 0px;
}

.opinion-comment-text {
  color: #a7a7a7;
}

.disabled-checkbox {
  opacity: 0.5;
}

::v-deep .v-text-field .v-label {
  top: -30px !important;
  font-weight: 500;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 12px !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}

::v-deep .v-input__slot {
  margin: 0;
  align-items: flex-start;
}

::v-deep .v-input--selection-controls__input {
  padding-top: 7px;
}
</style>
