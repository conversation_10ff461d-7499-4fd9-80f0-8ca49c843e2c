<template>
  <div class="container">
    <div class="survey__wrapper">
      <!-- <div class="form__wrapper" v-for="(list, idx) in answerLists" :key="idx">
        <div class="form-title">{{ list.title }}</div>
        <div class="multi-title">{{ list.multiTitle }}</div>
        <v-checkbox
        color="#41d8e6"
        off-icon="$check_box"
        on-icon="$check_box_inactive"
        v-for="(answer, id) in list.answers"
        :key="id"
        >
        <template v-slot:label>
          <div class="checkbox-label">{{ answer.ko }}</div>
        </template>
      </v-checkbox>
    </div> -->

      <!-- 사용목적 -->
      <div class="form__wrapper">
        <!-- {{ list.isChecked }} -->
        <div class="form-title">{{ $t(purposeArr.text) }}</div>
        <div class="multi-title">{{ $t("duplicate_check") }}</div>
        <div class="checkbox">
          <v-checkbox
            color="#41d8e6"
            off-icon="$check_box"
            v-for="list in purposeArr.choice"
            :key="list.id"
            :multiple="purposeArr.isMultipleChoice"
            on-icon="$check_box_inactive"
            v-model="checkedPurpose"
            :label="list.text"
            :value="list.id"
          >
            <template v-slot:label>
              <div class="checkbox-label">{{ $t(list.text) }}</div>
            </template>
          </v-checkbox>
        </div>
        <!-- @click="purposeInputHandler" -->
        <v-checkbox v-model="showPurposeInput" off-icon="$check_box" on-icon="$check_box_inactive" color="#41D8E6">
          <template v-slot:label>
            <div class="checkbox-label">{{ $t("input_etc") }}</div>
          </template>
        </v-checkbox>
        <template>
          <v-textarea
            v-if="showPurposeInput"
            outlined
            color="#41D8E6"
            :placeholder="this.$i18n.t('diet_input_etc_placeholder')"
            minlength="5"
            maxlength="50"
            v-model="purposeInput"
          ></v-textarea>
          <!-- :disabled="noDisease" -->
          <!-- :class="{ 'disabled-checkbox': noDisease }" -->
        </template>
      </div>

      <!-- 운동량 -->
      <div class="form__wrapper">
        <!-- {{ list.isChecked }} -->
        <div class="form-title">{{ $t(exerciseArr.text) }}</div>
        <div class="multi-title">{{ $t("single_check") }}</div>
        <div class="checkbox">
          <v-checkbox
            color="#41d8e6"
            off-icon="$check_box"
            v-for="list in exerciseArr.choice"
            :key="list.id"
            :multiple="exerciseArr.isMultipleChoice"
            on-icon="$check_box_inactive"
            v-model="checkedExercise"
            :label="list.text"
            :value="list.id"
          >
            <template v-slot:label>
              <div class="checkbox-label">{{ $t(list.text) }}</div>
            </template>
          </v-checkbox>
        </div>
      </div>

      <!-- 만성질환 -->
      <div class="form__wrapper">
        <!-- {{ list.isChecked }} -->
        <div class="form-title">{{ $t(chronicArr.text) }}</div>
        <div class="multi-title">{{ $t("duplicate_check") }}</div>
        <v-checkbox
          class="no-disease"
          off-icon="$check_box"
          on-icon="$check_box_inactive"
          v-model="noDisease"
          color="#41D8E6"
        >
          <template v-slot:label>
            <div class="checkbox-label">
              {{ $t("no_disease") }}
            </div>
          </template>
        </v-checkbox>
        <div class="checkbox">
          <v-checkbox
            color="#41d8e6"
            off-icon="$check_box"
            v-for="list in chronicArr.choice"
            :key="list.id"
            :multiple="chronicArr.isMultipleChoice"
            on-icon="$check_box_inactive"
            v-show="list.id !== 10"
            v-model="checkedChronic"
            :label="list.text"
            :value="list.id"
            :disabled="noDisease"
            :class="{ 'disabled-checkbox': noDisease }"
          >
            <template v-slot:label>
              <div class="checkbox-label">{{ $t(list.text) }}</div>
            </template>
          </v-checkbox>
        </div>
        <v-checkbox
          :disabled="noDisease"
          :class="{ 'disabled-checkbox': noDisease }"
          v-model="showChronicInput"
          off-icon="$check_box"
          on-icon="$check_box_inactive"
          color="#41D8E6"
        >
          <template v-slot:label>
            <div class="checkbox-label">{{ $t("input_etc") }}</div>
          </template>
        </v-checkbox>
        <template>
          <v-textarea
            v-if="showChronicInput"
            outlined
            color="#41D8E6"
            :placeholder="this.$i18n.t('diet_input_etc_placeholder')"
            minlength="5"
            maxlength="50"
            v-model="chronicInput"
            :disabled="noDisease"
            :class="{ 'disabled-checkbox': noDisease }"
          ></v-textarea>
        </template>
      </div>

      <!-- 음주량 -->
      <div class="form__wrapper">
        <div class="form-title">{{ $t(drinkArr.text) }}</div>
        <div class="multi-title">{{ $t("single_check") }}</div>
        <div class="checkbox">
          <v-checkbox
            color="#41d8e6"
            off-icon="$check_box"
            v-for="list in drinkArr.choice"
            :key="list.id"
            :multiple="drinkArr.isMultipleChoice"
            on-icon="$check_box_inactive"
            v-model="checkedDrink"
            :label="list.text"
            :value="list.id"
          >
            <template v-slot:label>
              <div class="checkbox-label">{{ $t(list.text) }}</div>
            </template>
          </v-checkbox>
        </div>
      </div>

      <!-- 흡연량 -->
      <div class="form__wrapper">
        <!-- {{ list.isChecked }} -->
        <div class="form-title">{{ $t(smokeArr.text) }}</div>
        <div class="multi-title">{{ $t("single_check") }}</div>
        <div class="checkbox">
          <v-checkbox
            color="#41d8e6"
            off-icon="$check_box"
            v-for="list in smokeArr.choice"
            :key="list.id"
            :multiple="smokeArr.isMultipleChoice"
            on-icon="$check_box_inactive"
            v-model="checkedSmoke"
            :label="list.text"
            :value="list.id"
          >
            <template v-slot:label>
              <div class="checkbox-label">{{ $t(list.text) }}</div>
            </template>
          </v-checkbox>
        </div>
      </div>

      <!-- <div class="form__wrapper" v-for="survey in lifeStyle" :key="survey.id" :multiple="survey.isMultipleChoice">
        <div class="form-title">{{ $t(survey.text) }}</div>
        <div v-for="choice in survey.choice" :key="choice.id">
          <v-checkbox
            color="#41d8e6"
            off-icon="$check_box"
            on-icon="$check_box_inactive"
            v-model="choice.isChecked"
            :label="choice.text"
            ><template v-slot:label>
              <div class="checkbox-label">
                {{ $t(choice.text) }}
              </div>
            </template></v-checkbox
          >
        </div>
      </div> -->
    </div>
    <div class="save-btn__wrapper">
      <v-btn color="#41D8E6" :disabled="!infoValid" class="save-btn" elevation="0" @click="submitHandler">
        {{ $t("save") }}
      </v-btn>
    </div>
    <div class="snackbar">
      <v-snackbar v-model="saveSuccess" timeout="2000">{{ successContent }}</v-snackbar>
      <v-snackbar v-model="successFail" timeout="2000" color="#EE0000">{{ failContent }}</v-snackbar>
    </div>
  </div>
</template>

<script>
import API from "@/api/survey/index.js";

export default {
  props: { lifeStyle: Array },
  data() {
    return {
      saveSuccess: false,
      successContent: this.$i18n.t("save_success"),
      successFail: false,
      failContent: this.$i18n.t("save_fail"),
      choice: [],
      purposeArr: [],
      initialPurpose: [],
      checkedPurpose: [],
      initialPurposeInput: false,
      showPurposeInput: false,
      purposeInput: "",
      initialPurposeText: "",

      exerciseArr: [],
      initialExercise: null,
      checkedExercise: null,

      chronicArr: [],
      initialChronic: [],
      checkedChronic: [],
      initialChronicInput: false,
      showChronicInput: false,
      initialChronicText: "",
      chronicInput: "",
      initialNoDisease: false,
      noDisease: false,

      drinkArr: [],
      initialDrink: null,
      checkedDrink: null,

      smokeArr: [],
      initialSmoke: null,
      checkedSmoke: null,
    };
  },
  computed: {
    infoValid() {
      return (
        this.purposeValidation() ||
        this.exerciseValidation() ||
        this.chronicValidation() ||
        this.drinkValidation() ||
        this.smokeValidation()
      );
    },
  },
  watch: {
    checkedPurpose(newVal) {
      // console.log(newVal);
    },
    checkedExercise(newVal) {
      // console.log(newVal);
    },
    noDisease(newVal) {
      // console.log(newVal);
    },
    lifeStyle(newVal) {
      this.makeQuestion();
    },
    showPurposeInput(newVal) {
      newVal !== this.initialPurposeInput ? (this.purposeInput = "") : null;
    },
    showChronicInput(newVal) {
      newVal !== this.initialChronicInput ? (this.chronicInput = "") : null;
    },
  },
  methods: {
    purposeValidation() {
      const sortedInitialArr = [...this.initialPurpose].sort();
      const sortedChangedArr = [...this.checkedPurpose].sort();
      if (this.checkedPurpose.length !== 0) {
        if (sortedInitialArr.length !== sortedChangedArr.length) {
          if (this.showPurposeInput) {
            return this.purposeInput.length >= 5 || this.initialPurposeText !== this.purposeInput;
          }
          return true;
        } else if (
          sortedInitialArr.length === sortedChangedArr.length &&
          this.initialPurposeInput !== this.showPurposeInput
        ) {
          if (this.showPurposeInput) {
            return this.purposeInput.length >= 5 && this.initialPurposeText !== this.purposeInput;
          }
          return true;
        }
        if (this.showPurposeInput) {
          return this.purposeInput.length >= 5 && this.initialPurposeText !== this.purposeInput;
        }
        return sortedInitialArr.some((value, index) => value !== sortedChangedArr[index]);
      } else {
        if (this.showPurposeInput) {
          return this.purposeInput.length >= 5 && this.initialPurposeText !== this.purposeInput;
        }
        return false;
      }
    },
    exerciseValidation() {
      return this.initialExercise !== this.checkedExercise && this.checkedExercise !== null;
    },
    chronicValidation() {
      const sortedInitialArr = [...this.initialChronic].sort();
      const sortedChangedArr = [...this.checkedChronic].sort();
      if (!this.noDisease) {
        if (this.checkedChronic.length !== 0) {
          if (sortedInitialArr.length !== sortedChangedArr.length) {
            return true;
          } else if (
            sortedInitialArr.length === sortedChangedArr.length &&
            this.initialChronicInput !== this.showChronicInput
          ) {
            if (this.showChronicInput) {
              return this.chronicInput.length >= 5 && this.initialChronicText !== this.chronicInput;
            }
            return true;
          }
          return sortedInitialArr.some((value, index) => value !== sortedChangedArr[index]);
        } else {
          if (this.showChronicInput) {
            return this.chronicInput.length >= 5 && this.initialChronicText !== this.chronicInput;
          }
          return sortedChangedArr.length !== 0;
        }
      }
      return this.initialNoDisease !== this.noDisease;
    },
    drinkValidation() {
      return this.initialDrink !== this.checkedDrink && this.checkedDrink !== null;
    },
    smokeValidation() {
      return this.initialSmoke !== this.checkedSmoke && this.checkedSmoke !== null;
    },
    submitHandler() {
      this.saveSurvey();
    },
    saveSurvey() {
      const createAnswers = [];
      const deleteAnswers = [];

      // 사용목적
      const sortedInitialArr = [...this.initialPurpose].sort();
      const sortedChangedArr = [...this.checkedPurpose].sort();
      // if (this.checkedPurpose.length !== 0) {
      // 객관식 선택한 경우
      const addedPurposeAnswers = sortedChangedArr.filter((choiceId) => !sortedInitialArr.includes(choiceId));
      const deletedPurposeAnswers = sortedInitialArr.filter((choiceId) => !sortedChangedArr.includes(choiceId));
      if (this.initialPurposeInput !== this.showPurposeInput) {
        if (this.showPurposeInput) {
          // text 없었는데, 선택한 경우(기타)
          if (this.purposeInput.length >= 5 && this.initialPurposeText !== this.purposeInput) {
            addedPurposeAnswers.length !== 0
              ? createAnswers.push({
                  questionId: 1,
                  choiceAnswers: addedPurposeAnswers,
                  textAnswer: this.purposeInput,
                })
              : createAnswers.push({ questionId: 1, textAnswer: this.purposeInput });
          }
          deletedPurposeAnswers.length !== 0
            ? deleteAnswers.push({ questionId: 1, choiceAnswers: deletedPurposeAnswers })
            : null;
        } else {
          // text 있었는데, 기타 비활성화 한 경우
          addedPurposeAnswers.length !== 0
            ? createAnswers.push({ questionId: 1, choiceAnswers: addedPurposeAnswers })
            : null;
          deletedPurposeAnswers.length !== 0
            ? deleteAnswers.push({
                questionId: 1,
                choiceAnswers: deletedPurposeAnswers,
                textAnswer: this.initialPurposeText,
              })
            : deleteAnswers.push({
                questionId: 1,
                textAnswer: this.initialPurposeText,
              });
        }
      } else if (this.initialPurposeText !== this.purposeInput) {
        // text 있었고, 내용 변경된 경우
        deletedPurposeAnswers.length !== 0
          ? deleteAnswers.push({
              questionId: 1,
              choiceAnswers: deletedPurposeAnswers,
              textAnswer: this.initialPurposeText,
            })
          : deleteAnswers.push({
              questionId: 1,
              textAnswer: this.initialPurposeText,
            });
        addedPurposeAnswers.length !== 0
          ? createAnswers.push({ questionId: 1, choiceAnswers: addedPurposeAnswers, textAnswer: this.purposeInput })
          : createAnswers.push({ questionId: 1, textAnswer: this.purposeInput });
      } else {
        addedPurposeAnswers.length !== 0
          ? createAnswers.push({ questionId: 1, choiceAnswers: addedPurposeAnswers })
          : null;
        deletedPurposeAnswers.length !== 0
          ? deleteAnswers.push({ questionId: 1, choiceAnswers: deletedPurposeAnswers })
          : null;
      }
      // }

      // 운동량
      if (this.exerciseValidation()) {
        const deletedExerciseAnswer = { questionId: 2, choiceAnswers: [this.initialExercise] };
        const addedExerciseAnswer = { questionId: 2, choiceAnswers: [this.checkedExercise] };
        deleteAnswers.push(deletedExerciseAnswer);
        createAnswers.push(addedExerciseAnswer);
      }

      // 만성 질환
      if (this.checkedChronic.length !== 0) {
        // 객관식 선택한 경우
        // console.log(addedChronicAnswers, deletedChronicAnswers);
        // 기존에 객관식 선택했어도 없음 클릭 시 객관식 기록되어 있음
        if (this.noDisease) {
          // console.log(this.initialChronicText);
          this.checkedChronic = [10];
          this.chronicInput = "";
          this.showChronicInput = false;
          if (this.initialChronicText === "") {
            if (!this.initialChronic.includes(10)) {
              createAnswers.push({ questionId: 3, choiceAnswers: this.checkedChronic });
              deleteAnswers.push({ questionId: 3, choiceAnswers: this.initialChronic });
            }
          } else {
            createAnswers.push({ questionId: 3, choiceAnswers: this.checkedChronic });
            deleteAnswers.push({
              questionId: 3,
              choiceAnswers: this.initialChronic,
              textAnswer: this.initialChronicText,
            });
          }
        } else {
          const filteredArr = this.checkedChronic.includes(10)
            ? this.checkedChronic.filter((i) => i !== 10)
            : this.checkedChronic;
          const addedChronicAnswers = filteredArr.filter((choiceId) => !this.initialChronic.includes(choiceId));
          const deletedChronicAnswers = this.initialChronic.filter((choiceId) => !filteredArr.includes(choiceId));
          if (this.initialChronicInput !== this.showChronicInput) {
            if (this.showChronicInput) {
              if (this.chronicInput.length >= 5 && this.initialChronicText !== this.chronicInput) {
                addedChronicAnswers.length !== 0
                  ? createAnswers.push({
                      questionId: 3,
                      choiceAnswers: addedChronicAnswers,
                      textAnswer: this.chronicInput,
                    })
                  : createAnswers.push({ questionId: 3, textAnswer: this.chronicInput });
                deletedChronicAnswers.length !== 0
                  ? deleteAnswers.push({ questionId: 3, choiceAnswers: deletedChronicAnswers })
                  : null;
              }
            } else {
              addedChronicAnswers.length !== 0
                ? createAnswers.push({ questionId: 1, choiceAnswers: addedChronicAnswers })
                : null;
              deletedChronicAnswers.length !== 0
                ? deleteAnswers.push({
                    questionId: 3,
                    choiceAnswers: deletedChronicAnswers,
                    textAnswer: this.initialChronicText,
                  })
                : deleteAnswers.push({
                    questionId: 3,
                    textAnswer: this.initialChronicText,
                  });
            }
          } else if (this.initialChronicText !== this.chronicInput) {
            deletedChronicAnswers.length !== 0
              ? deleteAnswers.push({
                  questionId: 3,
                  choiceAnswers: deletedChronicAnswers,
                  textAnswer: this.initialChronicText,
                })
              : deleteAnswers.push({
                  questionId: 3,
                  textAnswer: this.initialChronicText,
                });
            addedChronicAnswers.length !== 0
              ? createAnswers.push({ questionId: 3, choiceAnswers: addedChronicAnswers, textAnswer: this.chronicInput })
              : createAnswers.push({ questionId: 3, textAnswer: this.chronicInput });
          } else {
            addedChronicAnswers.length !== 0
              ? createAnswers.push({ questionId: 3, choiceAnswers: addedChronicAnswers })
              : null;
            deletedChronicAnswers.length !== 0
              ? deleteAnswers.push({ questionId: 3, choiceAnswers: deletedChronicAnswers })
              : null;
          }
        }
      } else {
        // console.log(this.checkedChronic.length, this.initialChronicText);
        if (this.noDisease) {
          // console.log(this.initialChronicText);
          this.checkedChronic = [10];
          this.chronicInput = "";
          this.showChronicInput = false;
          if (this.initialChronicText === "") {
            createAnswers.push({ questionId: 3, choiceAnswers: this.checkedChronic });
            deleteAnswers.push({ questionId: 3, choiceAnswers: this.initialChronic });
          } else {
            createAnswers.push({ questionId: 3, choiceAnswers: this.checkedChronic });
            deleteAnswers.push({
              questionId: 3,
              textAnswer: this.initialChronicText,
            });
          }
        }
      }

      // 음주량
      if (this.drinkValidation()) {
        const deletedDrinkAnswer = { questionId: 4, choiceAnswers: [this.initialDrink] };
        const addedDrinkAnswer = { questionId: 4, choiceAnswers: [this.checkedDrink] };
        deleteAnswers.push(deletedDrinkAnswer);
        createAnswers.push(addedDrinkAnswer);
      }

      // 흡연량
      if (this.smokeValidation()) {
        const deletedSmokeAnswer = { questionId: 5, choiceAnswers: [this.initialSmoke] };
        const addedSmokeAnswer = { questionId: 5, choiceAnswers: [this.checkedSmoke] };
        deleteAnswers.push(deletedSmokeAnswer);
        createAnswers.push(addedSmokeAnswer);
      }

      const subjectId = localStorage.getItem("subjectId");
      const reqBody = { subjectId: Number(subjectId) };
      createAnswers.length !== 0 ? (reqBody.createAnswers = createAnswers) : null;
      deleteAnswers.length !== 0 ? (reqBody.deleteAnswers = deleteAnswers) : null;
      // console.log(Number(subjectId), reqBody);
      this.putSurveyData(reqBody);
    },
    async putSurveyData(body) {
      try {
        const { data, status } = await API.putSurveyData(body);
        // console.log(data, status);
        if (status === 201) {
          this.saveSuccess = true;
          this.resetData();
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        } else this.successFail = true;
      }
    },

    resetData() {
      this.choice = [];
      this.purposeArr = [];
      this.initialPurpose = [];
      this.checkedPurpose = [];
      this.initialPurposeInput = false;
      this.showPurposeInput = false;
      this.purposeInput = "";
      this.initialPurposeText = "";

      this.exerciseArr = [];
      this.initialExercise = null;
      this.checkedExercise = null;

      this.chronicArr = [];
      this.initialChronic = [];
      this.checkedChronic = [];
      this.initialChronicInput = false;
      this.showChronicInput = false;
      this.initialChronicText = "";
      this.chronicInput = "";
      this.initialNoDisease = false;
      this.noDisease = false;

      this.drinkArr = [];
      this.initialDrink = null;
      this.checkedDrink = null;

      this.smokeArr = [];
      this.initialSmoke = null;
      this.checkedSmoke = null;
      this.$emit("reloadSurveyData");
    },

    makeQuestion() {
      this.purposeArr = this.lifeStyle[0];
      this.lifeStyle[0].choice.map((q) => {
        if (q.choiceAnswer.length !== 0) {
          this.checkedPurpose.push(q.id);
          this.initialPurpose.push(q.id);
        }
      });
      if (this.lifeStyle[0]?.textAnswer[0]?.text !== undefined) {
        this.initialPurposeInput = true;
        this.showPurposeInput = true;
        this.purposeInput = this.lifeStyle[0].textAnswer[0].text;
        this.initialPurposeText = this.lifeStyle[0].textAnswer[0].text;
      }

      this.exerciseArr = this.lifeStyle[1];
      this.lifeStyle[1].choice.map((q) => {
        if (q.choiceAnswer.length !== 0) {
          this.checkedExercise = q.id;
          this.initialExercise = q.id;
        }
      });

      this.chronicArr = this.lifeStyle[2];
      this.lifeStyle[2].choice.map((q) => {
        if (q.choiceAnswer.length !== 0) {
          this.checkedChronic.push(q.id);
          this.initialChronic.push(q.id);
        }
      });

      if (this.lifeStyle[2]?.textAnswer[0]?.text !== undefined) {
        this.showChronicInput = true;
        this.initialChronicInput = true;
        this.chronicInput = this.lifeStyle[2].textAnswer[0].text;
        this.initialChronicText = this.lifeStyle[2].textAnswer[0].text;
      }

      this.drinkArr = this.lifeStyle[3];
      this.lifeStyle[3].choice.map((q) => {
        if (q.choiceAnswer.length !== 0) {
          // console.log(q.id);
          this.checkedDrink = q.id;
          this.initialDrink = q.id;
        }
      });

      this.smokeArr = this.lifeStyle[4];
      this.lifeStyle[4].choice.map((q) => {
        if (q.choiceAnswer.length !== 0) {
          this.checkedSmoke = q.id;
          this.initialSmoke = q.id;
        }
      });
      if (this.checkedChronic[0] === 10) {
        this.noDisease = true;
        this.initialNoDisease = true;
      } else {
        this.noDisease = false;
        this.initialNoDisease = false;
      }
      // console.log(this.lifeStyle[2]);
    },
  },
  mounted() {
    // console.log(this.lifeStyle);
    this.makeQuestion();
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  padding: 40px 0;
  display: flex;
  flex-direction: column;
}

.survey__wrapper {
  height: 70vh;
  overflow: auto;
  padding: 0 30px 100px;
}
.form__wrapper {
  width: 100%;
  text-align: left;
  margin-bottom: 5vh;
}

.form-title {
  font-weight: 500;
  font-size: 20px;
  margin-bottom: 20px;
}

.multi-title {
  font-size: 16px;
  color: #646464;
  margin-bottom: 10px;
}

.save-btn__wrapper {
  width: 100%;
  position: fixed;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 20.83%, #ffffff 100%);
  bottom: 0;
  height: calc(5vh + 90px);
  left: 0;
  padding: 30px 30px 0px 30px;
}

.save-btn {
  width: 100%;
  max-width: 390px !important;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
  color: #c9f4f8 !important;
}
.v-input--selection-controls {
  margin: 0px !important;
  padding: 0px !important;
}
::v-deep .v-input__control {
  display: block !important;
  flex-wrap: nowrap !important;
  margin: 5px 0;
}

::v-deep .v-messages {
  display: none !important;
}

.checkbox {
  margin: 5px 0;
}

.checkbox-label {
  font-weight: 400;
  font-size: 20px !important;
  color: black;
  padding-bottom: 5px;
  letter-spacing: -0.03em;
  line-height: 26px;
}

.disabled-checkbox {
  opacity: 0.5;
}

.no-disease {
  width: 100%;
  z-index: 9;
}

::v-deep .v-text-field .v-label {
  top: -30px !important;
  font-weight: 500;
}

::v-deep .v-textarea.v-text-field--enclosed.v-text-field--outlined:not(.v-input--dense) textarea {
  margin-top: 5px !important;
}
::v-deep .v-text-field__slot {
  height: 85px;
  input::placeholder {
    font-size: 12px !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}

::v-deep .v-input__slot {
  margin-bottom: 0 !important;
  align-items: flex-start;
}

::v-deep .v-input--selection-controls__input {
  padding-top: 7px;
}
</style>
