<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <CompleteAlert :content="this.content" v-show="showCompleteAlert" />
    <FormSubTitle :currentStateIdx="currentStateIdx" />
    <div class="name-form__wrapper">
      <NameForm @nameInputHandler="nameInputHandler" @validName="validName" />
    </div>
    <div class="save-btn">
      <v-btn elevation="0" color="#41D8E6" type="submit" @click="updateUsername" :disabled="!isAble">{{
        $t("change")
      }}</v-btn>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import FormSubTitle from "@/components/Mypage/Common/FormSubTitle.vue";
import NameForm from "@/components/Mypage/ProfileView/Subpages/NameForm.vue";
import CompleteAlert from "@/components/Common/Modal.vue";
import { updateSubjectInfo } from "@/api/user/index";
import { getSubjectId } from "@/components/Common/getSubjectId.js";

export default {
  name: "NameEdit",

  components: {
    HeaderNav,
    FormSubTitle,
    NameForm,
    CompleteAlert,
  },

  data() {
    return {
      pageName: this.$i18n.t("profile_name"),
      currentStateIdx: 0,
      isAble: false,
      name: "",
      showCompleteAlert: false,
      content: this.$i18n.t("success_message_name"),
    };
  },

  methods: {
    nameInputHandler(fromChild) {
      this.name = fromChild;
    },
    validName(fromChild) {
      // console.log(fromChild);
      this.isAble = fromChild;
    },
    editUserNickname() {
      // console.log(this.name);
    },
    async updateUsername() {
      try {
        const subjectId = getSubjectId();
        const name = { nickname: this.name };
        const res = await updateSubjectInfo(subjectId, name);
        // console.log(res);
        if (res.status === 200) {
          this.showCompleteAlert = true;
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.name-form__wrapper {
  padding: 0 30px;
  margin-top: 30px;
}

.save-btn {
  padding: 0 30px;
  button {
    width: 100%;
    height: 50px !important;
    border-radius: 10px;
    color: #fff !important;
    font-size: 20px !important;
    font-weight: 700 !important;
    letter-spacing: -0.03em;
  }
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #c9f4f8 !important;
}

::v-deep .v-text-field__slot {
  font-size: 12px;
}
.theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}
</style>
