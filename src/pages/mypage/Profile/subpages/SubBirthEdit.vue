<template>
  <div class="subname__wrapper">
    <CompleteAlert
      :content="content"
      :btnText="this.$i18n.t('confirm_btn')"
      v-show="showCompleteAlert"
      @isConfirmed="isConfirmed"
    />
    <ErrorModal v-show="showErrModal" :error="error" @isClicked="isClicked" />
    <div class="name-edit__wrapper">
      <div class="close-icon__wrapper" @click="exitHandler">
        <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
      </div>
      <div class="form-container">
        <div class="edit-form">
          <div class="edit-title">{{ $t("edit_birth") }}</div>
          <div class="input__wrapper">
            <div class="name-input__item">
              <v-text-field
                type="number"
                inputmode="numeric"
                pattern="[0-9]*"
                v-model="year"
                color="#41d8e6"
                :placeholder="year"
                :error-messages="yearError"
                @change="birthHandler"
              >
              </v-text-field>
            </div>
            <div class="name-input__item age">
              <v-text-field
                type="number"
                inputmode="numeric"
                pattern="[0-9]*"
                v-model="month"
                color="#41d8e6"
                :placeholder="month"
                :error-messages="monthError"
                @change="birthHandler"
              >
              </v-text-field>
            </div>
            <div class="name-input__item">
              <v-text-field
                type="number"
                inputmode="numeric"
                pattern="[0-9]*"
                v-model="day"
                color="#41d8e6"
                :placeholder="day"
                :error-messages="dayError"
                @change="birthHandler"
              >
              </v-text-field>
            </div>
          </div>
        </div>
        <div>
          <v-btn
            class="main-large-btn"
            elevation="0"
            color="#41D8E6"
            type="submit"
            @click="saveHandler"
            :disabled="!validBirth"
            >{{ $t("save") }}</v-btn
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CompleteAlert from "@/components/Common/ConfirmModal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";
import { updateSubUserData } from "@/api/user/index";

export default {
  props: { subName: String },
  components: {
    CompleteAlert,
    ErrorModal,
  },
  data() {
    return {
      birth: "",
      year: null,
      month: null,
      day: null,
      max: 4,
      yearError: "",
      monthError: "",
      dayError: "",
      validBirth: false,
      showCompleteAlert: false,
      showErrModal: false,
      content: this.$i18n.t("change_birth_success"),
      error: this.$i18n.t("change_birth_error"),
    };
  },
  mounted() {
    // console.log("===!====", this.$route.params.id);
    // console.log("sub user is", this.$store.state.subUserBirth);
    setTimeout(() => {
      const birth = this.$store.state.subUserBirth.split("-");
      this.year = birth[0];
      this.month = birth[1];
      this.day = birth[2];
    }, 1000);
  },
  watch: {
    year(newVal) {
      // console.log(this.yearValidation(newVal));
      if (this.year.length === 0 || this.yearValidation(newVal)) this.yearError = "";
      else this.yearError = this.$i18n.t("invalid");
    },
    month(newVal) {
      // console.log(this.monthValidation(newVal));
      if (this.month.length === 0 || this.monthValidation(newVal)) this.monthError = "";
      else this.monthError = this.$i18n.t("invalid");
    },
    day(newVal) {
      // console.log(this.dayValidation(newVal));
      if (this.day.length === 0 || this.dayValidation(newVal)) this.dayError = "";
      else this.dayError = this.$i18n.t("invalid");
    },
    birth(newVal) {
      const birthday = newVal.replace(/-/gi, ""); // '-' 문자 모두 '' 변경

      const year = Number(birthday.substr(0, 4)); // 입력한 값의 0~4자리까지 (연)
      const month = Number(birthday.substr(4, 2)); // 입력한 값의 4번째 자리부터 2자리 숫자 (월)
      const day = Number(birthday.substr(6, 2)); // 입력한 값 6번째 자리부터 2자리 숫자 (일)
      const today = new Date(); // 오늘 날짜를 가져옴
      const yearNow = today.getFullYear(); // 올해 연도 가져옴

      if (birthday.length <= 8) {
        if (1900 > year || year > yearNow) {
          // 연도의 경우 1900 보다 작거나 yearNow 보다 크다면 false를 반환합니다.
          this.validBirth = false;
        } else if (month < 1 || month > 12) {
          this.validBirth = false;
        } else if (day < 1 || day > 31) {
          this.validBirth = false;
        } else if ((month == 4 || month == 6 || month == 9 || month == 11) && day == 31) {
          this.validBirth = false;
        } else if (month == 2) {
          // 2월달일때
          // 2월 29일(윤년) 체크
          const isleap = year % 4 == 0 && (year % 100 != 0 || year % 400 == 0);
          if (day > 29 || (day == 29 && !isleap)) {
            this.validBirth = false;
          } else {
            this.validBirth = true;
          } //end of if (day>29 || (day==29 && !isleap))
        } else {
          this.validBirth = true;
        } //end of if
      } else {
        // 입력된 생년월일이 8자 초과할때 : false
        this.validBirth = false;
      }
      // console.log("validBirth", this.validBirth);
    },
  },
  methods: {
    async updateUserBirth() {
      const birth = `${this.year}-${this.month}-${this.day}`;
      if (this.$store.state.subUserBirth !== birth) {
        // console.log("true!!");
        try {
          const birth = { birth: this.birth };
          const id = this.$route.params.id;
          const res = await updateSubUserData(id, birth);
          // console.log(res);
          if (res.status === 200) {
            this.showCompleteAlert = true;
          }
        } catch (error) {
          console.log(error);
          if (error.response.status === 429) {
            this.$store.commit("setoverReqModal", true);
          } else this.showErrModal = true;
        }
      } else {
        this.showErrModal = true;
      }
    },
    saveHandler() {
      this.updateUserBirth();
    },
    birthHandler() {
      const birth = `${this.year}-${this.month}-${this.day}`;
      if (this.year !== null && this.month !== null && this.day !== null) {
        this.birth = birth;
        // console.log(this.birth);
      }
    },
    yearValidation(year) {
      const today = new Date();
      const yearNow = today.getFullYear();
      if (1900 > year || year > yearNow) return false;
      else return true;
    },
    monthValidation(month) {
      if (month < 1 || month > 12 || month.length === 1) return false;
      else return true;
    },
    dayValidation(day) {
      if (day < 1 || day > 31 || day.length === 1) return false;
      else return true;
    },
    exitHandler() {
      this.$emit("birthClose", false);
    },
    isConfirmed(clicked) {
      this.showCompleteAlert = false;
      this.$emit("birthConfirmed", true);
    },
    isClicked(boolean) {
      this.showErrModal = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.subname__wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
  width: 100%;
  height: 100%;
}

.name-edit__wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: flex-end;
}

.close-icon__wrapper {
  text-align: left;
  position: absolute;
  top: 55px;
  img {
    width: 25px;
  }
}

.form-container {
  width: 100%;
}
.edit-form {
  background-color: #ededed;
  border-radius: 10px;
  margin-bottom: 10px;
}

.edit-title {
  font-size: 14px;
  color: #646464;
  border-bottom: 0.5px solid #a7a7a7;
  padding: 10px 0;
}

.input__wrapper {
  color: #000000;
  font-size: 18px;
  background-color: #fff;
  padding: 0 15px;
  border-radius: 0 0 10px 10px;
  display: flex;
}

.main-large-btn {
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

::v-deep .v-text-field .v-label {
  // top: -10px !important;
  font-size: 12px;
  color: #41d8e6;
}

::v-deep .v-text-field input {
  padding: 8px 0 0;
}
::v-deep .v-text-field {
  margin-top: 0;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 16px !important;
    letter-spacing: -0.03em;
    line-height: 15px !important;
  }
}
::v-deep .v-input {
  margin: 0 10px !important;
}
::v-deep .v-input input {
  font-size: 18px !important;
  line-height: 23px;
  text-align: center !important;
  font-family: GilroyMedium;
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}

// ::v-deep .v-text-field__slot {
//   font-size: 12px;
// }
.theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}
</style>
