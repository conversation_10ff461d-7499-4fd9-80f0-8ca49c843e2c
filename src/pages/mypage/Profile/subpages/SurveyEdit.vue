<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div class="tab-container">
      <v-tabs
        v-model="tabName"
        color="#C9F4F8"
        class="history-tabs pa-0"
        center-active
        grow
        @change="changeTabs"
        mobile-breakpoint="xs"
        slider-color="#41D8E6"
        slider-size="3"
        height="40px"
      >
        <!-- <v-tab class="px-0 mx-0" href="#tab-basicProfile">{{ $t("basic_profile_edit") }}</v-tab> -->
        <v-tab class="px-0 mx-0" href="#tab-lifestyle">{{ $t("lifestyle_profile_edit") }}</v-tab>
        <v-tab class="px-0 mx-0" href="#tab-eatingHabits">{{ $t("diet_profile_edit") }}</v-tab>
      </v-tabs>
    </div>
    <v-tabs-items v-model="tabName" color="transparent">
      <!-- <v-tab-item value="tab-basicProfile" class="tab-item">
        <BasicProfile />
      </v-tab-item> -->
      <v-tab-item value="tab-lifestyle" class="tab-item">
        <!-- <SurveyForm /> -->
        <LifeStyle v-if="loaded" :lifeStyle="lifeStyle" @reloadSurveyData="reloadSurveyData" />
      </v-tab-item>
      <v-tab-item value="tab-eatingHabits" class="tab-item">
        <EatingHabits v-if="loaded" :eatingHabits="eatingHabits" @reloadSurveyData="reloadSurveyData" />
      </v-tab-item>
    </v-tabs-items>
    <Loading v-if="loading" />
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
// import SurveyForm from "@/components/Forms/SurveyForm.vue";
// import BasicProfile from "./sections/BasicProfile.vue";
import LifeStyle from "./sections/LifeStyle.vue";
import EatingHabits from "./sections/EatingHabits.vue";

import Loading from "@/components/Common/Loading.vue";
import API from "@/api/survey/index.js";

export default {
  name: "SurveyEdit",
  components: {
    HeaderNav,
    // BasicProfile,
    LifeStyle,
    EatingHabits,
    // SurveyForm,
    Loading,
  },
  data() {
    return {
      pageName: this.$i18n.t("profile_survey_title"),
      tabName: "tab-basicProfile",
      currentTabIndex: "",
      count: "recent",
      subjectId: 0,
      surveyIdx: 1,
      questions: [],
      lifeStyle: [],
      eatingHabits: [],
      loaded: false,
      loading: false,
      checkedData: {},
    };
  },
  methods: {
    changeTabs(tabIndex) {
      this.currentTabIndex = tabIndex;
    },
    reloadSurveyData() {
      this.getSurveyList();
      // this.getSurveyData();
    },
    async getSurveyData() {
      try {
        const { data, status } = await API.getSurveyData(this.surveyIdx);
        // console.log(data, status);
        if (status === 200) {
          this.questions = data.survey.question;
          // console.log(this.questions);
          this.$nextTick(() => this.getSurveyCheckedData());
        }
      } catch (error) {
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
        console.log(error);
      }
    },
    async getSurveyCheckedData() {
      try {
        const query = `subjectId=${this.subjectId}&surveyId=${this.surveyIdx}`;
        const { data, status } = await API.getSurveyCheckedData(query);
        // console.log(data);
        if (status === 200) {
          this.checkedData = data;
          this.loaded = true;
          this.loading = false;
        }
      } catch (error) {
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
        console.log(error);
      }
    },
    async getSurveyList() {
      try {
        const query = `subjectId=${this.subjectId}&surveyId=${this.surveyIdx}`;
        const { data, status } = await API.getSurveyCheckedData1(query);
        // console.log(data.resultAnswers.question.slice(5));
        if (status === 200) {
          this.lifeStyle = data.resultAnswers.question.slice(0, 5);
          this.lifeStyle.forEach((survey) => {
            survey.choice.forEach((choice) => {
              choice.isChecked = choice.choiceAnswer.length > 0;
            });
          });
          this.eatingHabits = data.resultAnswers.question.slice(5);
          // console.log(this.lifeStyle);
          this.loaded = true;
          this.loading = false;
        }
      } catch (error) {
        console.log(error);
        this.loaded = true;
        this.loading = false;
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
  },
  beforeMount() {
    this.loading = true;
  },
  created() {
    window.scrollTo(0, 0);
    let tab = this.$route.query.tab;

    if (tab === 0) {
      this.tabName = "tab-lifestyle";
    } else if (tab == 1) {
      this.tabName = "tab-eatingHabits";
    }
    // else if (tab == 2) {
    //   this.tabName = "tab-eatingHabits";
    // }
    this.subjectId = Number(localStorage.getItem("subjectId"));
    this.getSurveyList();
    this.getSurveyData();
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  /* text-align: left; */
}
.tab-container {
  background-color: #c9f4f8;
  padding: 20px 30px 0;
}

::v-deep .v-tabs-slider-wrapper {
  color: #41d8e6;
  height: 3px !important;
}

::v-deep .v-tabs-slider {
  border-radius: 3px 3px 0px 0px !important;
}

::v-deep .v-tab {
  color: rgba(0, 0, 0, 0.3) !important;
  font-weight: 500 !important;
  line-height: 40px;
  letter-spacing: -0.03em !important;
  text-transform: none !important;
  font-size: 18px !important;
}
::v-deep .v-tab--active {
  font-size: 18px !important;
  color: rgba(0, 0, 0, 0.87) !important;
}

::v-deep .v-tabs-bar__content {
  background: transparent !important;
}

::v-deep .theme--light.v-tabs-items {
  background-color: transparent !important;
}

::v-deep .theme--light.v-tabs > .v-tabs-bar {
  background-color: transparent !important;
}

::v-deep .v-toolbar__content {
  height: auto !important;
}

::v-deep .v-tab {
  min-width: 60px !important;
  background-color: transparent !important;
}

// tab ripple 제거
::v-deep .v-ripple__container {
  display: none !important;
}

::v-deep .v-input--selection-controls__ripple {
  display: none !important;
}

::v-deep .v-tab:before {
  display: none !important;
}

::v-deep .theme--light.v-tabs-items {
  background-color: transparent !important;
}
</style>
