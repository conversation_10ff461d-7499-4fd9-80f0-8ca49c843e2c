<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <FormSubTitle :currentStateIdx="currentStateIdx" />
    <PhoneForm />
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import FormSubTitle from "@/components/Mypage/Common/FormSubTitle.vue";
import PhoneForm from "@/components/Mypage/ProfileView/Subpages/PhoneEditForm.vue";

export default {
  components: {
    HeaderNav,
    PhoneForm,
    FormSubTitle,
  },
  data() {
    return {
      pageName: this.$i18n.t("profile_phone_title"),
      currentStateIdx: 1,
    };
  },
};
</script>

<style lang="scss" scoped>
::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

::v-deep .theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}
</style>
