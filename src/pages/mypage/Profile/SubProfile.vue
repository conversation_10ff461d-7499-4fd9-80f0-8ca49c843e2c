<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div v-if="loaded" class="profile-main-container">
      <SubUserImageUpload :profileImg="profile_img" />
      <SubUserCategories :username="userName" @deleteSubUser="deleteSubUser" />
      <DeleteForm v-show="showDeleteForm" @closeDeleteForm="closeDeleteForm" @cancel="cancel" />
    </div>
    <Loading v-if="loading" />
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import SubUserImageUpload from "@/components/Mypage/ProfileView/SubUserImageUpload.vue";
import SubUserCategories from "@/components/Mypage/ProfileView/SubUserCategories.vue";
import DeleteForm from "./subpages/DeleteSubUser.vue";
import { fetchGetSubjectInfo } from "@/api/user/index";
import Loading from "@/components/Common/Loading.vue";
import { getSubjectId } from "@/components/Common/getSubjectId.js";

export default {
  name: "SubUserProfile",

  components: {
    HeaderNav,
    SubUserCategories,
    SubUserImageUpload,
    DeleteForm,
    Loading,
  },

  data() {
    return {
      loading: true,
      loaded: false,
      profile_img: "",
      pageName: this.$i18n.t("profile_title"),
      showDeleteForm: false,
      userName: "",
    };
  },

  mounted() {
    this.getSubUser();
    this.pageName = this.$i18n.t("profile_title");
    // console.log(this.$route.params);
  },

  methods: {
    async getSubUser() {
      try {
        const subjectId = getSubjectId();
        const { data } = await fetchGetSubjectInfo(subjectId);
        // console.log(data);

        if (data) {
          const userData = data.subject;
          this.profile_img = userData.image || require("@/assets/images/mypage-icon/profile.png");
          this.userName = userData.nickname;

          this.$store.commit("getUserName", userData.nickname);
          // console.log(this.$store.state.username);
        }
        this.loading = false;
        this.loaded = true;
      } catch (error) {
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        } else {
          this.loading = false;
          this.loaded = true;
          console.log(error);
        }
      }
    },

    deleteSubUser() {
      // console.log("clicked");
      this.showDeleteForm = true;
    },

    closeDeleteForm() {
      this.showDeleteForm = false;
    },

    cancel() {
      this.showDeleteForm = false;
    },
  },
};
</script>

<style scoped>
.profile-main-container {
  height: 100%;
}
</style>
