<template>
  <div class="profile-main-container">
    <!-- =============================================== -->
    <HeaderNav :pageName="pageName" />
    <ImageUpload :profileImg="profile_img" />
    <MenuCategories :username="username" :usermail="usermail" :userphone="userphone" :isSns="isSns" />
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import ImageUpload from "@/components/Mypage/ProfileView/ImageUpload.vue";
import MenuCategories from "@/components/Mypage/ProfileView/MenuCategories.vue";
import { fetchGetSubjectInfo } from "@/api/user/index";
import { getSubjectId } from "@/components/Common/getSubjectId.js";

export default {
  name: "MainUserProfile",
  components: {
    HeaderNav,
    ImageUpload,
    MenuCategories,
  },
  data() {
    return {
      loading: false,
      profile_img: "",
      pageName: this.$i18n.t("profile_title"),
      username: "",
      usermail: "",
      userphone: "",
      mymenu: false,
      clicked: false,
      isSns: false,
    };
  },
  computed: {
    allCheck() {
      return this.confirmPouch && this.confirmCapture && this.confirmBright;
    },
  },
  mounted() {
    // this.pageName = this.$i18n.t("profile_title");
    // this.username = this.$store.state.username;
    // this.userphone = this.$store.state.phone;
    // this.profile_img = this.$store.state.userImg;
    this.loadData();
  },

  methods: {
    clickHandler() {
      this.clicked = true;
    },
    async loadData() {
      try {
        const subjectId = getSubjectId();
        const { data } = await fetchGetSubjectInfo(subjectId);
        // console.log(data);

        if (data) {
          // console.log(data);
          const userData = data.subject;
          this.profile_img = userData.image || require("@/assets/images/mypage-icon/profile.png");
          this.username = userData.nickname;
          this.$store.commit("getUserName", userData.nickname);
          this.usermail = userData.email;
          this.userphone = userData.phone;

          // this.isSns = userData.sns === "false" ? false : true;

          // // 전화번호 형식 변경(기본 11자리 기준)
          // this.userphone =
          //   this.userphone.substring(0, 3) +
          //   "-" +
          //   this.userphone.substring(3, 5) +
          //   "**-" +
          //   this.userphone.substring(7, 9) +
          //   "**";
        }
      } catch (error) {
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
        this.loading = false;
        console.log(error);
      }
    },

    // gobackHome() {
    //   this.$router.push({ path: "/home" });
    // },

    checkPouch() {
      if (this.confirmPouch) {
        this.confirmPouch = false;
      } else {
        this.confirmPouch = true;
      }
    },
    checkCapture() {
      if (this.confirmCapture) {
        this.confirmCapture = false;
      } else {
        this.confirmCapture = true;
      }
    },
    checkBright() {
      if (this.confirmBright) {
        this.confirmBright = false;
      } else {
        this.confirmBright = true;
      }
    },
  },
};
</script>

<style scoped>
.profile-main-container {
  height: 100%;
}
</style>
