<template>
  <div class="add-user__container">
    <CompleteAlert
      :content="content"
      :btnText="this.$i18n.t('confirm_btn')"
      v-show="showCompleteAlert"
      @isConfirmed="isConfirmed"
    />
    <ErrorModal v-show="showErrModal" :error="error" @isClicked="isClicked" />
    <HeaderNav class="empty-background" :pageName="pageName" />
    <div class="content__wrapper">
      <div class="sub-title">{{ $t("add_user_description") }}</div>
      <GenderForm @genderInputHandler="genderInputHandler" />
      <div class="name-form__wrapper">
        <NameForm @nameInputHandler="nameInputHandler" />
      </div>
      <div class="form__wrapper">
        <BirthForm @birthInputHandler="birthHandler" />
      </div>
      <div class="form__wrapper">
        <HeightForm @heightInputHandler="heightInputHandler" />
      </div>
      <div class="form__wrapper">
        <WeightForm @weightInputHandler="weightInputHandler" />
      </div>
      <div class="form__wrapper">
        <WeightGoalForm @weightGoalInputHandler="weightGoalInputHandler" />
      </div>
      <div class="form__wrapper">
        <KetoneModeForm :initialKetoneMode="null" @ketoneModeHandler="ketoneModeHandler" />
      </div>
      <div class="input__wrapper">
        <div class="input-title">{{ $t("relationships") }}</div>

        <div>
          <v-checkbox
            :key="type.id"
            v-for="type in filteredUserTypesArr"
            color="#41d8e6"
            :value="type.id"
            off-icon="$check_box"
            on-icon="$check_box_inactive"
            v-model="userType"
          >
            <!-- @click="userTypeHandler" -->
            <template v-slot:label>
              <div class="input-label">{{ type.text }}</div>
            </template>
          </v-checkbox>
        </div>
        <!-- <div v-if="showTextArea">
            <template>
              <v-textarea
                outlined
                rows="3"
                color="#41D8E6"
                v-model="customUser"
                :error-messages="customUserErrMsg"
                :placeholder="this.$i18n.t('input_nickname')"
              ></v-textarea>
            </template>
          </div> -->
      </div>
      <CustomGroupForm
        v-if="type === 'kardio'"
        :title="this.$i18n.t('disease_group')"
        :groupArr="groupArr"
        @selectedGroup="selectedGroup"
      />
      <div class="btn__wrapper">
        <v-btn
          class="save-btn"
          :disabled="type === 'kardio' ? !isGroupSelected : !isAvailableSave"
          elevation="0"
          color="#41D8E6"
          type="submit"
          @click="saveBtnHandler"
          >{{ $t("save") }}</v-btn
        >
      </div>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import NameForm from "@/components/Forms/NameForm.vue";
// import GenderForm from "@/pages/mypage/SubUser/Gender.vue";
// import BirthForm from "@/pages/mypage/SubUser/Birth.vue";
import GenderForm from "@/components/Forms/GenderForm.vue";
import BirthForm from "@/components/Forms/BirthForm.vue";
import HeightForm from "@/components/Forms/HeightForm.vue";
import WeightForm from "@/components/Forms/WeightForm.vue";
import WeightGoalForm from "@/components/Forms/WeightGoalForm.vue";
import KetoneModeForm from "@/components/Forms/KetoneModeForm.vue";
import CustomGroupForm from "@/components/Forms/CustomGroupForm.vue";

import CompleteAlert from "@/components/Common/ConfirmModal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";
import { postSubUser } from "@/api/user/index";

export default {
  name: "Adduser",
  components: {
    HeaderNav,
    GenderForm,
    NameForm,
    BirthForm,
    HeightForm,
    WeightForm,
    WeightGoalForm,
    KetoneModeForm,
    CustomGroupForm,
    CompleteAlert,
    ErrorModal,
  },
  data() {
    return {
      loading: false,
      profile_img: "",
      pageName: this.$i18n.t("add_user"),
      name: "",
      validName: false,
      gender: "",
      validGender: false,
      birth: null,
      validBirth: false,
      height: 0,
      validHeight: false,
      weight: 0,
      validWeight: false,
      goalWeight: 0,
      validGoalWeight: false,
      ketoneMode: null,
      showCompleteAlert: false,
      showErrModal: false,
      userType: null,
      validUserType: false,
      userTypesArr: [
        { id: 10, text: this.$i18n.t("patient"), type: "kardio" },
        { id: 11, text: this.$i18n.t("caregiver"), type: "kardio" },
        { id: 12, text: this.$i18n.t("other"), type: "kardio" },
        { id: 2, text: this.$i18n.t("parents"), type: "human" },
        { id: 3, text: this.$i18n.t("spouse"), type: "human" },
        { id: 4, text: this.$i18n.t("child"), type: "human" },
        { id: 5, text: this.$i18n.t("grandparents"), type: "human" },
        { id: 6, text: this.$i18n.t("brothers"), type: "human" },
        { id: 7, text: this.$i18n.t("relative"), type: "human" },
        { id: 8, text: this.$i18n.t("friend"), type: "human" },
        { id: 9, text: this.$i18n.t("acquaintance"), type: "human" },
      ],
      type: "",
      groupArr: [
        { id: 42, text: "Healthy" },
        { id: 43, text: "Hypertension" },
        { id: 44, text: "Diabetes" },
        { id: 45, text: "CKD" },
      ],
      selectedGroupId: [],
      // isAvailableSave: false,
      content: this.$i18n.t("success_add_user"),
      error: this.$i18n.t("error_add_user"),
    };
  },
  methods: {
    clickHandler() {
      this.clicked = true;
    },
    // userTypeHandler() {
    //   console.log(this.userType);
    // },
    // maleCheckBoxHandler(fromChild) {
    //   fromChild ? (this.gender = "m") : (this.gender = "");
    // },
    // femaleCheckBoxHandler(fromChild) {
    //   fromChild ? (this.gender = "f") : (this.gender = "");
    //   console.log(fromChild);
    // },
    genderInputHandler(gender) {
      // console.log(gender);
      this.gender = gender;
    },
    nameInputHandler(inputName) {
      // console.log("name input", inputName);
      this.name = inputName.name;
      this.validName = inputName.valid;
    },
    birthHandler(fromChild) {
      // console.log(fromChild);
      this.birth = fromChild.birth;
      this.validBirth = fromChild.valid;
    },
    heightInputHandler(inputHeight) {
      // console.log(inputHeight);
      this.height = inputHeight.height;
      this.validHeight = inputHeight.valid;
    },
    weightInputHandler(inputWeight) {
      // console.log(inputWeight);
      this.weight = inputWeight.weight;
      this.validWeight = inputWeight.valid;
    },
    weightGoalInputHandler(inputGoalWeight) {
      // console.log(inputGoalWeight);
      // console.log(inputGoalWeight.goal);
      this.goalWeight = inputGoalWeight.goal;
      this.validGoalWeight = inputGoalWeight.valid;
    },
    ketoneModeHandler(ketoneMode) {
      // console.log(ketoneMode);
      this.ketoneMode = ketoneMode;
    },
    selectedGroup(id) {
      // console.log(id);
      this.selectedGroupId = id;
    },
    saveBtnHandler() {
      this.postSubUser();
    },
    inputValidation(name) {
      if (/^[ㄱ-ㅎ|가-힣|a-z|A-Z]{1,10}$/.test(name)) return true;
      else return false;
    },
    isConfirmed() {
      this.$router.go(-1);
    },
    isClicked() {
      this.showErrModal = false;
    },

    async postSubUser() {
      try {
        const userData = {
          subjectTypeId: Number(this.userType),
          nickname: this.name,
          sex: this.gender,
          birth: this.birth,
          initialWeight: Number(this.weight),
          height: Number(this.height),
          ketoneMode: this.ketoneMode,
        };
        this.selectedGroupId = this.selectedGroupId.map((i) => Number(i));
        this.goalWeight !== 0 ? (userData.targetWeight = Number(this.goalWeight)) : null;
        this.type === "kardio" ? (userData.surveyChoiceIds = this.selectedGroupId) : null;
        // console.log(userData);
        const res = await postSubUser(userData);
        // console.log(res);
        if (res.status === 201) {
          this.showCompleteAlert = true;
        } else {
          this.showErrModal = true;
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        } else this.showErrModal = true;
      }
    },
  },
  computed: {
    isAvailableSave() {
      return (
        this.validName &&
        this.validGender &&
        this.validBirth &&
        this.validHeight &&
        this.validWeight &&
        this.ketoneMode !== null &&
        this.userType !== null
      );
    },
    isGroupSelected() {
      return (
        this.validName &&
        this.validGender &&
        this.validBirth &&
        this.validHeight &&
        this.validWeight &&
        this.ketoneMode !== null &&
        this.userType !== null &&
        (this.selectedGroupId !== undefined ? this.selectedGroupId.length !== 0 : false)
      );
    },
    filteredUserTypesArr() {
      if (this.type === "kardio") {
        return this.userTypesArr.filter((item) => item.type === "kardio");
      } else {
        return this.userTypesArr.filter((item) => item.type === "human");
      }
    },
  },
  watch: {
    userType(newVal) {
      // console.log(this.selectedGroupId);
      if (this.userType === null) {
        this.validUserType = false;
      } else {
        this.validUserType = true;
      }
    },
    name(newVal) {
      /^[ㄱ-ㅎ|가-힣|a-z|A-Z|0-9|]{2,20}$/.test(newVal) ? (this.validName = true) : (this.validName = false);
      // console.log("validName", this.validName);
    },
    gender(newVal) {
      this.gender === "" ? (this.validGender = false) : (this.validGender = true);
      // console.log("validGender", this.validGender);
    },
  },
  mounted() {
    // console.log(this.$route.params.type);
    this.type = localStorage.getItem("type") ?? "human";
    // console.log(this.selectedGroupId.length);
    // !== undefined
  },
};
</script>

<style lang="scss" scoped>
.add-user__container {
  width: 100%;
  height: 100%;
}

.content__wrapper {
  width: 100%;
  padding: 40px 30px 80px;
  overflow: auto;
}

.sub-title {
  font-size: 18px;
  font-weight: 500;
  color: #646464;
  text-align: left;
  letter-spacing: -0.03em;
  padding-bottom: 30px;
}
.profile__wrapper {
  width: 100%;
  display: flex;
  // justify-content: space-between;
  // align-items: center;
  margin-top: 20px;
}
.profile-img__box {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: #fff;
  position: relative;
  border: 5px solid #41d8e6;
}

.profile-img__wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    width: 35%;
  }
}

.check-box {
  // display: flex;
  color: #646464;
  margin-left: 20px;
  width: 65%;
  // display: flex;
}

.form__wrapper {
  // padding: 20px 0;
  margin-bottom: 30px;
}

.name-form__wrapper {
  margin: 30px 0;
}

.input__wrapper {
  margin-bottom: 30px;
}

.input-title {
  width: 100%;
  text-align: left;
  color: #646464;
  font-weight: 500;
  font-size: 18px;
  margin-bottom: 10px;
}

.input-label {
  color: #646464;
  font-size: 18px;
  line-height: 26px;
  margin-left: 10px;
}

.btn__wrapper {
  width: 100%;
  position: fixed;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 20.83%, #ffffff 100%);
  bottom: 0;
  height: calc(5vh + 90px);
  left: 0;
  padding: 30px 30px 0px 30px;
}

.save-btn {
  letter-spacing: -0.03em;
  height: 50px !important;
  position: relative;
  z-index: 2;
  max-width: 340px;
  width: 100%;
  border-radius: 10px;
  line-height: 50px;
  font-size: 20px !important;
  font-weight: bold !important;
  color: #ffffff !important;
  margin: 0 auto;
}

::v-deep .v-input--selection-controls {
  margin: 0;
  padding: 0;
}

::v-deep .v-input__slot {
  margin: 0;
}

::v-deep .v-input--selection-controls__input {
  padding-top: 5px;
}

::v-deep .v-messages {
  min-height: 10px;
}

::v-deep .v-input--selection-controls__input {
  margin: 0;
}

::v-deep .v-text-field__slot {
  font-size: 12px;
}
.theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}
::v-deep .v-text-field__details {
  padding-top: 5px;
}
// input[type="file"] {
//   position: absolute;
//   width: 1px;
//   height: 1px;
//   padding: 0;
//   margin: -1px;
//   overflow: hidden;
//   clip: rect(0, 0, 0, 0);
//   border: 0;
// }
</style>
