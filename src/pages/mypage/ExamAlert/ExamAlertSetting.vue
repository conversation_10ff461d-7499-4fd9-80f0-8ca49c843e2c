<template>
  <div class="container">
    <HeaderNav :pageName="pageName" />
    <SuccesModal v-if="openModal" />
    <div class="exam-alert__wrapper">
      <div :class="isIos ? 'save-btn__wrapper' : 'save-btn__wrapper-android'">
        <!-- <div class="switch__wrapper"> -->
        <v-switch
          @click="isUrineTestAlert"
          v-model="onUrineTestAlert"
          class="mt-0"
          color="#41d8e6"
          height="20px"
          inset
        ></v-switch>
      </div>
      <div class="time-settings__wrapper">
        <div class="time-btn__wrapper">
          <button @click="amBtnHandler" :class="[amActive, 'inactive-btn']">{{ $t("am") }}</button>
          <button @click="pmBtnHandler" :class="[pmActive, 'inactive-btn']">{{ $t("pm") }}</button>
        </div>
        <div v-if="!onUrineTestAlert" class="inactive-btn__wrapper">
          <div class="select-box">
            <button class="hour-btn">{{ defaultHour }}</button>
            <div class="colon">:</div>
            <button class="hour-btn">{{ defaultMinute }}</button>
            <div class="arrow-icon__wrapper">
              <img src="@/assets/images/mypage-icon/bottom_arrow.png" alt="arrow" />
            </div>
          </div>
        </div>
        <div v-else class="time-select__wrapper">
          <div class="select-box">
            <button @click="hourBtnClicked" class="hour-btn">{{ defaultHour }}</button>
            <div class="colon">:</div>
            <button @click="minuteBtnClicked" class="hour-btn">{{ defaultMinute }}</button>
            <ul v-if="showHourList" class="hour-list">
              <li v-for="hh in hour" v-bind:key="hh" class="hour-item" @click="hourBtnHandler(hh)">
                {{ hh }}
              </li>
            </ul>
            <ul v-if="showMinuteList" class="minute-list">
              <li v-for="(mm, id) in minute" v-bind:key="id" class="minute-item" @click="minuteHandler(mm)">
                {{ mm }}
              </li>
            </ul>
            <div class="arrow-icon__wrapper">
              <img src="@/assets/images/mypage-icon/bottom_arrow.png" alt="arrow" />
            </div>
          </div>
        </div>
      </div>
      <div class="underline"></div>
      <div class="cycle__wrapper">
        <div class="cycle-title">{{ $t("cycle") }}</div>
        <div class="weekly-cycle__wrapper">
          <div class="cycle-subtitle">{{ $t("weekly_cycle") }}</div>
          <div class="switch__wrapper">
            <v-switch
              class="pa-1 mt-0 mr-2"
              color="#41d8e6"
              height="20px"
              v-model="isWeeklyCycle"
              inset
              @click="setWeeklyCycleMode"
            ></v-switch>
          </div>
        </div>
        <DayPicker :activeWeeklyCycle="activeWeeklyCycle" @clickedDayButtons="clickedDayButtons" />
        <div class="weekly-cycle__wrapper">
          <div class="cycle-subtitle">{{ $t("monthly_cycle") }}</div>
          <div class="switch__wrapper">
            <v-switch
              class="pa-1 mt-0 mr-2"
              color="#41d8e6"
              height="20px"
              v-model="isMonthlyCycle"
              inset
              @click="setMonthlyCycleMode"
            ></v-switch>
          </div>
        </div>
        <DatePicker :activeMonthlyCycle="activeMonthlyCycle" @datePickerHandler="datePickerHandler" />
      </div>
    </div>
    <div class="btn__wrapper">
      <button :class="[saveActive, 'save-btn']" @click="saveHandler">{{ $t("save") }}</button>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import DayPicker from "@/pages/mypage/ExamAlert/DayPicker.vue";
import DatePicker from "@/pages/mypage/ExamAlert/DatePicker.vue";
import SuccesModal from "@/pages/mypage/ExamAlert/SuccesModal.vue";

export default {
  name: "ExamAlertSettings",
  components: {
    HeaderNav,
    DatePicker,
    DayPicker,
    SuccesModal,
  },
  data() {
    return {
      pageName: this.$i18n.t("test_alert_settings"),
      defaultHour: 8,
      defaultMinute: "00",
      hour: 12,
      minute: ["00", "10", "20", "30", "40", "50"],
      minuteListClicked: false,
      minuteItemClicked: false,
      onUrineTestAlert: false,
      showHourList: false,
      showMinuteList: false,
      amBtnClicked: true,
      pmBtnClicked: false,
      isWeeklyCycle: false,
      activeWeeklyCycle: false,
      isMonthlyCycle: false,
      activeMonthlyCycle: false,
      selectedDays: [],
      postDays: [],
      selectedDate: 1,
      month: 25,
      openModal: false,
      timeInfo: JSON.parse(localStorage.getItem("UrineTestTime")) || "",
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
  methods: {
    datePickerHandler(date) {
      // console.log(date);
      this.selectedDate = date;
    },
    clickedDayButtons(days) {
      if (this.selectedDays.includes(days)) {
        this.selectedDays = this.selectedDays.filter((i) => i !== days);
      } else {
        this.selectedDays = [...this.selectedDays, days];
      }
      // console.log("selectedDays:", this.selectedDays);
    },

    hourBtnHandler(id) {
      this.defaultHour = id;
      this.showHourList = false;
    },
    minuteHandler(idx) {
      this.defaultMinute = idx;
      this.showMinuteList = false;
    },
    hourBtnClicked() {
      if (this.showMinuteList === true) {
        this.showHourList = true;
        this.showMinuteList = false;
      } else if (this.showMinuteList === false) {
        this.showHourList = !this.showHourList;
      }
    },
    minuteBtnClicked() {
      if (this.showHourList === true) {
        this.showHourList = false;
        this.showMinuteList = true;
      } else if (this.showHourList === false) {
        this.showMinuteList = !this.showMinuteList;
      }
    },
    amBtnHandler() {
      if (this.pmBtnClicked === true) {
        this.pmBtnClicked = false;
        this.amBtnClicked = true;
      }
      this.amBtnClicked = true;
    },
    pmBtnHandler() {
      if (this.amBtnClicked === true) {
        this.amBtnClicked = false;
        this.pmBtnClicked = true;
      }
      this.pmBtnClicked = true;
    },

    isUrineTestAlert() {
      const onMessage = {
        action: "onUrineTestAlert",
      };
      const offMessage = {
        action: "offUrineTestAlert",
      };
      if (!this.onUrineTestAlert) {
        this.isWeeklyCycle = false;
        this.activeMonthlyCycle = false;
        this.isMonthlyCycle = false;
        this.activeWeeklyCycle = false;
        localStorage.setItem("UrineTestAlert", false);
        localStorage.removeItem("UrineTestTime");
        localStorage.removeItem("selectedDate");
        localStorage.removeItem("selectedDays");
        Webview.offUrineTestAlert(offMessage);
      } else if (this.onUrineTestAlert) {
        localStorage.setItem("UrineTestAlert", true);
        Webview.onUrineTestAlert(onMessage);
      }
    },
    setWeeklyCycleMode() {
      if (this.onUrineTestAlert) {
        // when weekly cycle on
        if (this.isWeeklyCycle) {
          this.isMonthlyCycle = false;
          this.activeWeeklyCycle = true;
          this.activeMonthlyCycle = false;
        } // when weekly cycle off
        else if (!this.isWeeklyCycle) {
          this.activeWeeklyCycle = false;
        }
      }
    },
    setMonthlyCycleMode() {
      if (this.onUrineTestAlert) {
        // when Monthly cycle on
        if (this.isMonthlyCycle) {
          this.isWeeklyCycle = false;
          this.activeMonthlyCycle = true;
          this.activeWeeklyCycle = false;
        } // when Monthly cycle off
        else if (!this.isMonthlyCycle) {
          this.activeMonthlyCycle = false;
        }
      }
    },

    saveHandler() {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      const selectHour = this.pmBtnClicked ? this.defaultHour + 12 : this.defaultHour;
      const postDays = new Array(0);
      if (this.isWeeklyCycle) {
        this.selectedDays.map((item) => {
          if (item === 0) postDays.push("mon");
          if (item === 1) postDays.push("tue");
          if (item === 2) postDays.push("wed");
          if (item === 3) postDays.push("thu");
          if (item === 4) postDays.push("fri");
          if (item === 5) postDays.push("sat");
          if (item === 6) postDays.push("sun");
        });
      }
      const message = {
        action: "setUrineTestTimeAlert",
        cycle: this.isMonthlyCycle ? "monthly" : "weekly",
        days: this.activeWeeklyCycle ? Array.from(new Set(postDays)) : this.selectedDate,
        hour: String(selectHour),
        min: this.defaultMinute,
      };
      if (this.onUrineTestAlert) {
        if (this.isWeeklyCycle) {
          // console.log("weekly setting:", message);
          localStorage.setItem("UrineTestTime", JSON.stringify(message));
          localStorage.setItem("selectedDays", JSON.stringify(this.selectedDays));
          // localStorage.setItem("UrineTestAlert", true);
          // localStorage.setItem("cycle", JSON.stringify())
          Webview.setUrineTestTimeAlert(message);
          this.openModal = true;
        }
        if (this.isMonthlyCycle) {
          // console.log("monthly setting:", message);
          localStorage.setItem("UrineTestTime", JSON.stringify(message));
          localStorage.setItem("selectedDate", JSON.stringify(this.selectedDate));
          Webview.setUrineTestTimeAlert(message);
          this.openModal = true;
        } else console.log("nothing selected!", this.isWeeklyCycle, this.isMonthlyCycle);
      }
    },
    notiIsAuthorized() {
      Webview.notiAuthorized({ action: "notiAuthorized" });
      this.$nextTick(() => this.loadNotiStatus());
    },
    loadNotiStatus() {
      this.selectedDays = JSON.parse(localStorage.getItem("selectedDays")) || [];
      this.selectedDate = JSON.parse(localStorage.getItem("selectedDate")) || 1;
      // console.log(this.timeInfo.days);
      if (this.onUrineTestAlert) {
        if (this.timeInfo.cycle === "weekly") {
          this.isWeeklyCycle = true;
          this.activeWeeklyCycle = true;
        } else if (this.timeInfo.cycle === "monthly") {
          this.isMonthlyCycle = true;
          this.activeMonthlyCycle = true;
        }
      }
      if (Number(this.timeInfo.hour) >= 12) {
        this.amBtnClicked = false;
        this.pmBtnClicked = true;
        const num = Number(this.timeInfo.hour) - 12;
        this.defaultHour = num;
        // console.log(typeof num);
        // console.log(toString(Number(this.timeInfo.hour) - 12));
      } else if (Number(this.timeInfo.hour) < 12) {
        this.amBtnClicked = true;
        this.pmBtnClicked = false;
        this.defaultHour = this.timeInfo.hour;
      }
      this.defaultMinute = this.timeInfo.min || "00";
    },
  },

  computed: {
    amActive() {
      return this.amBtnClicked ? "time-btn" : false;
    },
    pmActive() {
      return this.pmBtnClicked ? "time-btn" : false;
    },
    saveActive() {
      return this.onUrineTestAlert && (this.isWeeklyCycle || this.isMonthlyCycle) ? "active_save-btn" : "save-btn";
    },
    notiStatus() {
      return this.$store.state.notiStatus;
    },
  },

  watch: {
    notiStatus(newVal) {
      // console.log(newVal);
      this.onUrineTestAlert = newVal;
    },
  },

  mounted() {
    // console.log("UrineTestAlert on:", JSON.parse(localStorage.getItem("UrineTestAlert")));
    this.onUrineTestAlert = JSON.parse(localStorage.getItem("UrineTestAlert")) && this.notiStatus;
    this.notiIsAuthorized();
  },
};
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}

.exam-alert__wrapper {
  width: 100%;
  padding: 40px 30px 150px 30px;
}
.save-btn__wrapper {
  position: absolute;
  top: 64px;
  right: 30px;
}
.save-btn__wrapper-android {
  position: absolute;
  top: 28px;
  right: 30px;
}
.btn__wrapper {
  width: 100%;
  position: fixed;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 20.83%, #ffffff 100%);
  bottom: 0;
  height: calc(5vh + 90px);
  left: 0;
  padding: 30px 30px 0px 30px;
}

.save-btn {
  height: 50px;
  position: relative;
  z-index: 2;
  max-width: 390px !important;
  width: 100%;
  border-radius: 10px;
  line-height: 50px;
  font-size: 20px;
  font-weight: bold;
  background-color: #41d8e6;
  color: #c9f4f8;
  margin: 0 auto;
}

.active_save-btn {
  color: #fff;
}

.time-settings__wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.time-btn__wrapper {
  width: 140px;
  height: 55px;
  background-color: #ededed;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.time-btn {
  width: 55px;
  height: 37.5px;
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  margin: 0 5px;
  color: #000000 !important;
  font-size: 20px;
  font-weight: 500;
}

.inactive-btn {
  width: 55px;
  height: 37.5px;
  margin: 0 5px;
  font-size: 20px;
  font-weight: 500;
  color: #a7a7a7;
}

.inactive-btn__wrapper {
  width: 140px;
  height: 55px;
  background-color: #fff;
  border-radius: 5px;
  border: 1px solid #fff;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: GilroyMedium;
}

.time-select__wrapper {
  width: 140px;
  height: 55px;
  background-color: #fff;
  border-radius: 5px;
  border: 1px solid #ededed;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: GilroyMedium;
  letter-spacing: -0.03em;
}
.time-select__wrapper:hover {
  border: 2px solid #41d8e6;
}

.colon {
  font-size: 24px;
  font-weight: 500;
  color: #a7a7a7;
  margin: 0 5px;
}

.arrow-icon__wrapper {
  width: 14px;
  display: flex;
  align-items: center;
  margin-left: 5px;
  img {
    width: 100%;
  }
}
.select-box {
  width: 140px;
  display: flex;
  padding: 10px;
}
.hour-btn {
  width: 50%;
  letter-spacing: -0.03em;
  font-size: 24px;
  font-weight: 500;
}

.hour-list {
  background-color: #fff;
  width: 140px;
  height: 100%;
  min-height: 310px;
  max-height: 65vh;
  overflow: scroll;
  position: absolute;
  top: 203px;
  right: 30px;
  border-radius: 5px;
  border: 2px solid #41d8e6;
}

ul {
  padding: 0 !important;
  list-style: none;
  z-index: 5;
}

.hour-item {
  font-family: GilroyMedium;
  letter-spacing: -0.03em;
  font-size: 24px;
  font-weight: 500;
  text-align: left;
  width: 100%;
  padding: 5px 10px 5px 30px;
}
.hour-item:hover {
  background-color: #c9f4f8;
}

.minute-list {
  background-color: #fff;
  width: 140px;
  height: 340px;
  max-height: 70vh;
  overflow: scroll;
  position: absolute;
  top: 203px;
  right: 30px;
  border-radius: 5px;
  border: 2px solid #41d8e6;
}
.minute-item {
  font-family: GilroyMedium;
  letter-spacing: -0.03em;
  font-size: 24px;
  font-weight: 500;
  text-align: right;
  width: 100%;
  padding: 10px 30px 10px 10px;
}

.minute-item:hover {
  background-color: #c9f4f8;
}

.underline {
  border-bottom: 0.5px solid #a7a7a7;
}

.cycle__wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.cycle-title {
  // width: 100%;
  font-weight: 500;
  font-size: 20px;
  margin: 20px 0;
  text-align: left;
  color: #000000;
}

.weekly-cycle__wrapper {
  display: flex;
  justify-content: space-between;
}

.cycle-subtitle {
  font-size: 18px;
  color: #646464;
}

.switch__wrapper {
  display: flex;
  width: 40px;
  align-items: center;
  justify-content: flex-end;
}

.monthly-setting__wrapper {
  display: flex;
  justify-content: space-between;
}

.monthly-button__wrapper {
  display: flex;
  background-color: #ededed;
  width: 165px;
  height: 50px;
  border-radius: 5px;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.monthly-button {
  height: 100%;
  width: 90%;
  color: #a7a7a7;
  font-size: 24px;
  font-family: GilroyMedium;
}

.active-monthly-button__wrapper {
  display: flex;
  width: 165px;
  height: 50px;
  border-radius: 5px;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.monthly-text {
  font-weight: 500;
  font-size: 16px;
  color: #a7a7a7;
  height: 50px;
  display: flex;
  align-items: center;
}

::v-deep .v-input--selection-controls__input {
  width: 36px !important;
}

::v-deep .theme--light.v-input--switch .v-input--switch__track {
  color: #dadada;
}

::v-deep .v-input--switch__track {
  opacity: 1 !important;
  width: 45px !important;
  height: 25px !important;
}

::v-deep .v-input--switch__thumb {
  color: #ffffff !important;
  width: 17px !important;
  height: 17px !important;
}

::v-deep .v-messages {
  min-height: 5px;
}
</style>
