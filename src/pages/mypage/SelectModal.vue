<template>
  <div>
    <div class="bg-modal">
      <div class="alert-window">
        <div class="alert-window__content" v-if="isKo">{{ userName }} {{ $t("select_modal") }}</div>
        <div class="alert-window__content" v-else>{{ $t("select_modal") }} {{ userName }}?</div>
        <div class="btn__wrapper">
          <div class="left-btn" @click="closeAlertHandler">{{ $t("no") }}</div>
          <div class="right-btn" @click="selectUser">{{ $t("yes") }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    userName: String,
  },
  data() {
    return {
      selectModal: true,
      switchUser: false,
      isKo: true,
    };
  },
  methods: {
    closeAlertHandler() {
      this.selectModal = false;
      this.$emit("selectModal", this.selectModal);
    },

    selectUser() {
      this.switchUser = true;
      this.$emit("switchUser", this.switchUser);
    },
  },
  mounted() {
    this.isKo = this.$i18n.locale === "ko";
  },
};
</script>
<style scoped>
.bg-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 450px;
  height: 100%;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.alert-window {
  background-color: #fff;
  width: 100%;
  /* height: 180px; */
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 35px 15px 25px 15px;
}

.alert-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.alert-window__content {
  font-size: 18px;
  padding-top: 15px;
}

.btn__wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.left-btn {
  width: 45%;
  height: 55px;
  background: #c8c8c8;
  border-radius: 5px;
  margin: 30px 8px 0 8px;
  font-weight: 700;
  font-size: 20px;
  line-height: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  letter-spacing: -0.03em;
  color: #fff;
}
.right-btn {
  width: 45%;
  height: 55px;
  background: #41d8e6;
  border-radius: 5px;
  margin: 30px 8px 0 8px;
  font-weight: 700;
  font-size: 20px;
  line-height: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  letter-spacing: -0.03em;
  color: #fff;
}
</style>
