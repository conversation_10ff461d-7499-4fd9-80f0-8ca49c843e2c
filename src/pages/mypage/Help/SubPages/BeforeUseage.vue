<template>
  <div class="accordion-container">
    <Accordion>
      <AccordionItem v-for="(item, idx) in accordion" :key="idx">
        <template class="accordion-menu" slot="accordion-trigger">
          <div class="question-title">
            Q
            <div class="accordion-title" v-html="item.title"></div>
          </div>
        </template>
        <template slot="accordion-content">
          <span v-html="item.content"></span>
        </template>
      </AccordionItem>
    </Accordion>
  </div>
</template>

<script>
import Accordion from "@/components/Common/Accordion.vue";
import AccordionItem from "@/components/Common/AccordionItem.vue";
export default {
  name: "BeforeUseage",
  components: { Accordion, AccordionItem },
  data() {
    return {
      accordion: [
        {
          title: this.$i18n.t("about_cym_title"),
          content: this.$i18n.t("about_cym"),
        },
        {
          title: this.$i18n.t("what_is_cym_title"),
          content: this.$i18n.t("what_is_cym"),
        },
        {
          title: this.$i18n.t("where_buy_title"),
          content: this.$i18n.t("where_buy"),
        },
        {
          title: this.$i18n.t("about_cym_results_title"),
          content: this.$i18n.t("about_cym_results"),
        },
        {
          title: this.$i18n.t("how_to_use_title"),
          content: this.$i18n.t("how_to_use"),
        },
        {
          title: this.$i18n.t("do_not_use_case_title"),
          content: this.$i18n.t("do_not_use_case"),
        },
        {
          title: this.$i18n.t("until_use_title"),
          content: this.$i18n.t("until_use"),
        },
        {
          title: this.$i18n.t("how_many_test_title"),
          content: this.$i18n.t("how_many_test"),
        },
        {
          title: this.$i18n.t("how_to_store_title"),
          content: this.$i18n.t("how_to_store"),
        },
        {
          title: this.$i18n.t("when_medication_title"),
          content: this.$i18n.t("when_medication"),
        },
        {
          title: this.$i18n.t("when_period_title"),
          content: this.$i18n.t("when_period"),
        },
        {
          title: this.$i18n.t("what_is_solution_title"),
          content: this.$i18n.t("what_is_solution"),
        },
        {
          title: this.$i18n.t("is_safe_title"),
          content: this.$i18n.t("is_safe"),
        },
      ],
    };
  },
};
</script>

<style lang="scss">
.question-title {
  font-family: "GilroyMedium";
  width: 95%;
  height: 100%;
  color: #41d9e6;
  font-size: 20px;
  display: flex;
  align-items: flex-start;
  text-align: left;
}
.accordion-title {
  font-size: 18px;
  letter-spacing: -0.03em;
  display: flex;
  color: #000000;
  margin-left: 10px;
  padding-bottom: 2px;
}

.fold {
  width: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

img {
  width: 100%;
}
</style>
