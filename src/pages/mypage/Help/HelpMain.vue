<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div class="help_container">
      <div class="menu__wrapper">
        <router-link to="/help/faq">
          <div class="menu-card">
            <div class="menu-card-header">
              <div class="menu-card-title">{{ $t("faq") }}</div>
              <div class="icon__wrapper">
                <img src="@/assets/images/bottom-arrow.png" alt="right arrow" />
              </div>
            </div>
          </div>
        </router-link>
        <div class="menu-card">
          <div @click="goKakaoChannel" class="menu-card-header">
            <div class="menu-card-title">{{ $t("kakao_inquiry") }}</div>
            <div class="icon__wrapper">
              <img src="@/assets/images/bottom-arrow.png" alt="right arrow" />
            </div>
          </div>
          <div class="underline"></div>
        </div>
        <div class="menu-card">
          <div class="menu-card-header">
            <div class="menu-card-title">{{ $t("terms_info") }}</div>
          </div>
        </div>
        <div class="terms-card">
          <div v-for="(terms, idx) in terms" :key="idx">
            <router-link :to="{ path: terms.path }" class="menu-card-header">
              <div class="menu-card-content">{{ terms.title }}</div>
              <div class="icon__wrapper">
                <img src="@/assets/images/bottom-arrow.png" alt="right arrow" />
              </div>
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";

export default {
  name: "HelpMain",
  components: { HeaderNav },
  data() {
    return {
      pageName: this.$i18n.t("category_customer_service"),
      terms: [
        {
          title: this.$i18n.t("service_terms"),
          path: "/help/terms/service",
        },
        {
          title: this.$i18n.t("privacy_terms"),
          path: "/help/terms/privacy",
        },
        {
          title: this.$i18n.t("marketing_terms"),
          path: "/help/terms/marketing",
        },
        {
          title: this.$i18n.t("sensitive_terms"),
          path: "/help/terms/sensitive",
        },
      ],
    };
  },
  methods: {
    goKakaoChannel() {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      const message = {
        action: "goKakaoChannel",
        url: "https://pf.kakao.com/_bxbQrs/chat",
      };
      Webview.goKakaoChannel(message);
    },
  },
};
</script>

<style lang="scss" scoped>
.help_container {
  width: 100%;
  text-align: left;
}

.menu__wrapper {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  padding: 40px 30px;
}

.menu-card {
  width: 100%;
}

.terms-card {
  margin: 20px 0;
}

.menu-card-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.menu-card-title {
  font-size: 18px;
  font-weight: 500;
  color: #000000;
}

.icon__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 14px;
    transform: rotate(-90deg);
  }
}

.underline {
  border-bottom: 0.5px solid #a7a7a7;
  margin-bottom: 20px;
}

.menu-card-content {
  padding-left: 10px;
  color: #646464;
  font-size: 16px;
  line-height: 23px;
  /* margin-bottom: 10px; */
}
</style>
