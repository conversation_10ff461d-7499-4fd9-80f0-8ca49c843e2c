<template>
  <div>
    <!-- cym-color-zone -->
    <ErrModal v-show="isFulled" @showModal="showModal" />
    <SelectModal
      v-show="showSelectModal"
      :userName="selectedName"
      @selectModal="selectModal"
      @switchUser="switchUser"
    />
    <AlertModal
      v-if="showAlertModal"
      @closeBtnHandler="closeBtnHandler"
      @nextHandler="surveyHandler"
      :btnText="goToSurvey"
      :error="error"
    />
    <div class="cym-color-zone">
      <HeaderNav />
      <div v-if="type === 'kardio'">
        <CustomMyPage @selectedGroupId="selectedGroupId" />
      </div>

      <div v-else>
        <!-- <div> -->
        <div class="profile_wrapper">
          <div v-if="isSelectUser === 0" class="active-profile-animation"></div>
          <div class="profile-img" @click="isSelected(0)">
            <img
              :src="profile_img"
              alt="user profile"
              class="profile-img-tg"
              @error="replaceImage"
            />
          </div>
        </div>
        <div class="profile_name_wrapper">
          <div class="profile-name">{{ username }}</div>
        </div>
        <!-- my solution area -->
        <div class="user-edit-section">
          <div class="sub-profile_wrapper">
            <div v-if="isSelectUser === 1" class="active-sub1"></div>
            <div v-if="isSelectUser === 2" class="active-sub2"></div>
            <div v-if="isSelectUser === 3" class="active-sub3"></div>
            <div
              class="sub-profile-img_box"
              v-for="(sub, idx) in subUser"
              :key="idx"
              @click="isSelected(idx + 1)"
            >
              <div class="sub-profile-img_wrapper">
                <img :src="sub.image" />
              </div>
              <div class="sub-user-name">
                {{
                  sub.name.length > 5 ? `${sub.name.slice(0, 4)}...` : sub.name
                }}
              </div>
            </div>
          </div>
          <div class="user-edit-wrapper">
            <!-- <router-link class="edit-btn__wrapper" to="/profile"> -->
            <div class="edit-btn__wrapper" @click="checkSelcetUser">
              <v-icon class="edit-icon">$edit</v-icon>
              <div class="btn-title">{{ $t("profile_title") }}</div>
            </div>
            <!-- </router-link> -->
            <div class="edit-btn__wrapper" @click="checkSubUser">
              <v-icon class="edit-icon"> $user_plus </v-icon>
              <div class="btn-title">{{ $t("add_user") }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- menu-zone -->
    <div v-if="type !== 'kardio'" class="menu-container__wrapper">
      <div class="menu-container">
        <!-- =================================================================== -->
        <!-- 🚨 Mypage category-guide -->
        <div class="mymenu-wrapper" v-for="(item, i) in mypageMenus" :key="i">
          <div class="menu-icon">
            <img :src="item.img" alt="" id="guide-icon" />
          </div>
          <router-link :to="{ path: item.path }" class="menu-title">
            {{ item.title }}
          </router-link>
        </div>
        <div class="underline-wrapper">
          <div class="underline"></div>
        </div>

        <!-- =================================================================== -->
        <!-- 🚨 Mypage category-guide -->
        <!-- <div class="mymenu-wrapper">
          <div class="menu-icon">
            <img src="@/assets/images/mypage-icon/push_icon.png" alt="" />
          </div>
          <div class="menu-title">알림설정</div>
        </div> -->

        <!-- =================================================================== -->
        <!-- 🚨 Mypage category-about-us -->
        <div class="company-logo-wrapper">
          <div class="logo-icon">
            <img src="@/assets/images/mypage-icon/yellosis_icon.png" />
          </div>
          <div class="company-name" @click="goCym702">
            {{ $t("category_about_company") }}
          </div>
        </div>
      </div>
    </div>
    <div v-else class="menu-container__wrapper">
      <CustomUsers v-if="loaded" :groupUsers="groupUsers[groupId].subjects" />
    </div>
    <navigation :path="path"></navigation>
    <Loading v-if="loading" />
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/HeaderNav.vue";
import { fetchGetUserInfo } from "@/api/user/index";
import ErrModal from "./ErrModal.vue";
import SelectModal from "./SelectModal.vue";
import AlertModal from "@/components/Common/AlertModal.vue";
import CustomMyPage from "@/components/Mypage/ProfileView/CustomMyPage.vue";
import CustomUsers from "@/components/Mypage/ProfileView/CustomUsers.vue";
import Loading from "@/components/Common/Loading.vue";

export default {
  name: "Mypage",

  components: {
    HeaderNav,
    ErrModal,
    SelectModal,
    AlertModal,
    CustomMyPage,
    CustomUsers,
    Loading,
  },

  data() {
    return {
      type: "",
      loading: false,
      loaded: false,
      path: "/mypage",
      mypageMenus: [
        {
          img: require("@/assets/images/mypage-icon/cym-report.png"),
          title: "Cym Report",
          path: "/mypage/cym-report",
        },
        {
          img: require("@/assets/images/mypage-icon/guide_icon.png"),
          title: this.$i18n.t("category_guide"),
          path: "/mypage/guide",
        },
        {
          img: require("@/assets/images/mypage-icon/push_icon.png"),
          title: this.$i18n.t("category_alert"),
          path: "/mypage/examalert",
        },
        {
          img: require("@/assets/images/mypage-icon/notice_icon.png"),
          title: this.$i18n.t("category_notice"),
          path: "/mypage/notice",
        },
        {
          img: require("@/assets/images/mypage-icon/help_icon.png"),
          title: this.$i18n.t("category_customer_service"),
          path: "/help",
        },
      ],
      username: "",
      userphone: "",
      profile_img: "",
      selectedName: "",
      isPlural: false,
      isFulled: false,
      showSelectModal: false,
      subUser: [
        {
          id: 1,
          subId: 1,
          image: require("@/assets/images_assets/mypage-icon/sub-default.png"),
          name: "",
        },
        {
          id: 2,
          subId: 2,
          image: require("@/assets/images_assets/mypage-icon/sub-default.png"),
          name: "",
        },
        {
          id: 3,
          subId: 3,
          image: require("@/assets/images_assets/mypage-icon/sub-default.png"),
          name: "",
        },
      ],
      isSubUser: true,
      isSelectUser: 0,
      choiceId: 0,
      error: this.$i18n.t("need_survey_message"),
      goToSurvey: this.$i18n.t("go_to_survey"),
      groupId: 0,
      groupUsers: [],
    };
  },

  computed: {
    showAlertModal() {
      return this.$store.state.surveyModalOpen;
    },
    surveyStatus() {
      return this.$store.state.surveyStatus;
    },
  },

  watch: {
    surveyStatus(newVal) {
      this.survey =
        newVal === "" ? localStorage.getItem("surveyStatus") : newVal;
    },
  },

  methods: {
    transformData(data) {
      const groupUsers = [
        { groupName: "healthy", subjects: [] },
        { groupName: "hypertension", subjects: [] },
        { groupName: "diabetes", subjects: [] },
        { groupName: "ckdEsrdDialysis", subjects: [] },
      ];
      groupUsers.map((groupUser) => {
        const { groupName } = groupUser;
        if (data[groupName] && Array.isArray(data[groupName].subjects)) {
          groupUser.subjects = [...data[groupName].subjects];
        } else {
          groupUser.subjects = [];
        }

        // Ensure length of subjects is 9
        while (groupUser.subjects.length < 9) {
          groupUser.subjects.push({});
        }
      });
      // console.log(groupUsers);
      return groupUsers;
    },
    arrangeData(data) {
      try {
        if (this.type === "kardio") {
          // this.groupUsers = data;
          // this.groupUsers[0].subjects = data.healthy.subjects;
          // const groups = data.map(group => this.groupUsers.;
          this.groupUsers = this.transformData(data);
          if (
            Object.keys(this.groupUsers[this.groupId].subjects[0]).length !== 0
          ) {
            sessionStorage.setItem(
              "subjects",
              JSON.stringify(this.groupUsers[this.groupId].subjects)
            );
          } else {
            sessionStorage.setItem("subjects", null);
          }
          // console.log(this.groupUsers);
          this.loading = false;
        } else {
          this.$store.commit("getUserName", data.subjects[0].nickname);
          sessionStorage.setItem("subjects", JSON.stringify(data.subjects));
          const subjectsData = [];

          data.subjects.map((user, idx) => {
            subjectsData.push(user);
          });
          subjectsData.shift();
          // console.log("subject data", subjectsData);
          const mainUserData = data.subjects[0];
          this.username = mainUserData.nickname;
          // this.$store.commit("getUsername", mainUserData.nickname);
          this.profile_img =
            mainUserData.image ||
            require("@/assets/images/mypage-icon/profile.png");
          this.$store.commit("getUserImage", mainUserData.image);
          this.userPhone = mainUserData.phone;
          this.$store.commit("getUserPhone", mainUserData.phone);

          subjectsData.map((sub, idx) => {
            // console.log(sub);
            this.subUser[idx].image =
              sub.image ||
              require("@/assets/images_assets/mypage-icon/sub-default.png");
            this.subUser[idx].name = sub.nickname || "";
            this.subUser[idx].subId = sub.id || "";
          });
          this.loaded = true;
          // console.log("+++++", this.subUser);
          this.loading = false;
        }
      } catch (e) {
        console.error(e);
      }
    },
    async loadData() {
      this.loaded = false;
      this.loading = true;
      try {
        const { data } = await fetchGetUserInfo();
        // console.log(data);

        if (data) {
          this.arrangeData(data);
        }
        this.loaded = true;
        this.loading = false;
      } catch (error) {
        this.loaded = true;
        this.loading = false;
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    isSelected(idx) {
      if (idx > 0) {
        this.choiceId = idx;
        if (this.subUser[idx - 1].name !== "") {
          if (this.isSelectUser !== idx) {
            this.selectedName = this.subUser[idx - 1].name;
            this.showSelectModal = true;
          }
        } else {
          this.showSelectModal = false;
        }
      } else if (idx === 0) {
        if (this.isSelectUser !== idx) {
          this.choiceId = idx;
          this.selectedName = this.username;
          this.showSelectModal = true;
        }
      }
    },
    selectModal(fromChild) {
      this.showSelectModal = false;
    },
    switchUser(fromChild) {
      // console.log(fromChild);
      this.isSelectUser = this.choiceId;
      sessionStorage.setItem("selectUser", this.choiceId);
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const ketoneMode = subjects[this.choiceId].ketoneMode;
      localStorage.setItem("ketoneMode", ketoneMode);
      this.$router.go();
      // this.loadData();
      // this.showSelectModal = false;
    },
    replaceImage(e) {
      e.target.src = require("@/assets/images/mypage-icon/profile.png");
    },
    /*global Webview*/
    /*eslint no-undef: "error"*/
    goCym702() {
      // 회사 소개 페이지로 이동.
      const bridge_msg = {
        action: "goCym702",
        url: "https://yellosis.com/company.html",
      };
      Webview.goCym702(bridge_msg);
      const message = "https://smartstore.naver.com/cym702";
      Webview.openUrl(message);
    },
    showModal(fromChild) {
      // console.log(fromChild);
      this.isFulled = fromChild;
    },
    checkSelcetUser() {
      if (this.isSelectUser > 0) {
        if (this.subUser[this.isSelectUser - 1].name !== "") {
          const id = this.subUser[this.isSelectUser - 1].subId;
          this.$router.push({ name: "SubProfile", params: { id: id } });
        }
      } else {
        this.$router.push({ name: "Myprofile" });
      }
    },
    checkSubUser() {
      const status =
        this.surveyStatus === ""
          ? localStorage.getItem("surveyStatus")
          : this.surveyStatus;
      // console.log(status);
      if (status === "survey_ongoing" || status === "survey_ready") {
        this.$store.commit("setSurveyModal", true);
      } else if (this.subUser[2].name !== "") {
        this.isFulled = true;
      } else {
        this.$router.push({ name: "SubUserAdd" });
      }
    },

    closeBtnHandler(fromChild) {
      this.$store.commit("setSurveyModal", false);
    },
    surveyHandler(fromChild) {
      this.$router.push("/survey");
    },

    // kardio custom
    selectedGroupId(id) {
      // console.log(this.groupUsers[id].subjects.length);
      // console.log(Object.keys(this.groupUsers[id].subjects).length !== 0);
      this.groupId = id;
      this.$store.commit("setGroupId", id);
      sessionStorage.setItem("groupId", id);
      if (Object.keys(this.groupUsers[id].subjects[0]).length !== 0) {
        sessionStorage.setItem(
          "subjects",
          JSON.stringify(this.groupUsers[this.groupId].subjects)
        );
      } else sessionStorage.setItem("subjects", JSON.stringify(null));
      sessionStorage.setItem("selectUser", JSON.stringify(null));
      this.loading = true;
      this.$router.go();
    },
  },

  mounted() {
    this.loadData();
    // sessionStorage에 저장된 값으로 설정
    // const selectUser = this.$store.state.selectUser > 0 ? this.$store.state.subUserId : this.$store.state.selectUser;
    this.type = localStorage.getItem("type") ?? "human";
    this.isSelectUser = Number(sessionStorage.getItem("selectUser")) || 0;
    this.groupId = Number(sessionStorage.getItem("groupId"));
  },
};
</script>

<style lang="scss" scoped>
.bg-main-color {
  background-color: #c9f4f8 !important;
}

.cym-color-zone {
  background-color: #c9f4f8;
  padding: 0 30px;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.1);
}

.profile_wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}
.profile_name_wrapper {
  padding: 0 30px;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-top: 10px;

  .profile-name {
    color: #000000;
    font-weight: 500;
    font-size: 22px;
  }
}

.profile-img {
  width: 130px; // 140px
  height: 130px; // 140px
  border-radius: 50%;
  background-color: #fff;
  position: relative;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    // border: 5px solid #41d8e6;
  }
}

.menu-container__wrapper {
  padding: 25px 30px 90px 30px;
  height: 100%;
}

.user-edit-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px 30px;
}

.user-edit-wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.edit-btn__wrapper {
  display: flex;
  height: 29px;
}

.edit-icon {
  margin-top: 2px;
}

.go-solution-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

.mysolution-title {
  margin-left: 10px;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 0px;
  letter-spacing: -0.03em;
}

.solution-icon-img {
  width: 10px;
  margin-top: 6px;
  img {
    width: 100%;
    object-fit: contain;
  }
}

.btn-title {
  font-size: 16px;
  font-weight: 400;
  color: #646464;
  letter-spacing: -0.03em;
}
.sub-profile_wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0 50px;
}

.sub-profile-img_box {
  background: #fff;
  width: 70px;
  max-width: 80px;
  height: 70px;
  border-radius: 100%;
}

.sub-profile-img_wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    z-index: 3;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    object-fit: cover;
  }
}

.sub-user-name {
  font-size: 14px;
  font-weight: 500;
  width: 70px;
  display: flex;
  justify-content: center;
  letter-spacing: -0.03em;
  line-height: 14px;
  padding-top: 7px;
}

.sub-profile-img {
  width: 100%;
  height: 100%;
  margin: 3px 0 0 5px;
}

.right-icon {
  margin-left: 3px;
}

.solution_section {
  padding: 0px 30px 30px 30px;
}

.my-solution-box {
  width: 100%;
  height: 70px;
  background-color: #f8f8f8;
  border-radius: 10px;
  margin-top: 13px;
  line-height: 70px;
  color: #41d8e6;
  font-size: 13px;
  font-weight: 500;
}
.mymenu-wrapper {
  padding: 0px 30px 20px 30px;
  display: flex;
  flex-direction: center;
  align-items: center;

  a {
    text-decoration: none;
    color: #000;
  }
}

.menu-icon {
  width: 22px;
  display: flex;
  flex-direction: center;
  align-items: center;
  img {
    width: 100%;
    object-fit: contain;
  }
}
#guide-icon {
  width: 18px;
  margin-right: 4px;
}

.menu-title {
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 22px;
  letter-spacing: -0.03em;
  margin-left: 10px;
}

// .menu-container {
//   border-bottom: 1px solid #a7a7a7;
// }

.underline-wrapper {
  padding: 0 30px;
}

.underline {
  border-top: 0.5px solid #a7a7a7;
}
.company-logo-wrapper {
  display: flex;
  padding: 22px 30px;
  align-items: center;
}

.logo-icon {
  width: 20px;
}

.logo-icon > img {
  width: 100%;
  object-fit: contain;
}

.company-name {
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 23px;
  letter-spacing: -0.03em;
  margin-left: 14px !important;
}

// active profile animation
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes rotate {
  from {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}

.active-profile-animation {
  z-index: 0;
  position: absolute;
  margin: 0 auto;
  width: 140px;
  height: 140px;
  border: 5px solid transparent;
  border-radius: 50%;
  background-image: linear-gradient(#fff, #fff),
    linear-gradient(180deg, #41d8e6 0%, #cdf5f9 85%, #c9f4f8 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  -webkit-transition: all 1s ease-in;
  -webkit-animation-name: rotate;
  -webkit-animation-duration: 1.5s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  // transition: all 1s ease-in;
  animation-name: rotate;
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}
.active-sub1 {
  z-index: 0;
  position: absolute;
  margin: 0 auto;
  width: 80px;
  height: 80px;
  border: 5px solid transparent;
  border-radius: 50%;
  left: 55px;
  background-image: linear-gradient(#fff, #fff),
    linear-gradient(180deg, #41d8e6 0%, #cdf5f9 85%, #c9f4f8 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  // -webkit-transition: all 1s ease-in;
  -webkit-animation-name: rotate;
  -webkit-animation-duration: 1.5s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  // transition: all 1s ease-in;
  animation-name: rotate;
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}
.active-sub2 {
  z-index: 0;
  position: absolute;
  margin: 0 auto;
  width: 80px;
  height: 80px;
  border: 5px solid transparent;
  border-radius: 50%;
  left: 50%;
  margin-left: -40px;
  background-image: linear-gradient(#fff, #fff),
    linear-gradient(180deg, #41d8e6 0%, #cdf5f9 85%, #c9f4f8 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  // -webkit-transition: all 1s ease-in;
  -webkit-animation-name: rotate;
  -webkit-animation-duration: 1.5s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  // transition: all 1s ease-in;
  animation-name: rotate;
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}
.active-sub3 {
  z-index: 0;
  position: absolute;
  margin: 0 auto;
  width: 80px;
  height: 80px;
  border: 5px solid transparent;
  border-radius: 50%;
  right: 55px;
  background-image: linear-gradient(#fff, #fff),
    linear-gradient(180deg, #41d8e6 0%, #cdf5f9 85%, #c9f4f8 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  // -webkit-transition: all 1s ease-in;
  -webkit-animation-name: rotate;
  -webkit-animation-duration: 1.5s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  // transition: all 1s ease-in;
  animation-name: rotate;
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}
</style>
