import API from "../../api/auth/index";
import router from "../../router/index";
import store from "../../store/index";
import base64 from "base-64";

export default {
  async LOGIN({ commit }, { type, account, password }) {
    try {
      const { data, status } = await API.fetchLoginRequest(type, { account: account, password: password });
      store.commit("setLoginError", false);
      // console.log(response.data);
      if (status === 201) {
        /*global Webview*/
        /*eslint no-undef: "error"*/
        // Webview.setCookie({ action: "setCookie" });
        const token = data?.accessToken;
        const payload = token.substring(token.indexOf(".") + 1, token.lastIndexOf("."));
        const decoded = base64.decode(payload);
        const { exp } = JSON.parse(decoded);
        localStorage.removeItem("snsType");
        localStorage.subjectId = data.mainSubjectId;
        localStorage.auth = token;
        localStorage.exp = exp;
        store.commit("LOGIN", token);
        router.push({ path: "/home" });
      }
    } catch (error) {
      // localStorage.setItem("loginError", true);
      // console.log(error.response.data.error.message[0]);
      error.response.data.error.message[0] === "C006"
        ? store.commit("setLoginErrorMsg", "delete")
        : store.commit("setLoginErrorMsg", "login");
      store.commit("setLoginError", true);
      new Error("LOGIN request failed");
    }
  },

  async SNS_LOGIN({ commit }, { snsType, appType, account }) {
    try {
      localStorage.setItem("loginError", false);
      const response = await API.fetchSnsLoginRequest(snsType, appType, account);
      console.log("sns login response");
      console.log(response.data);
      if (response.status === 201) {
        // Webview.setCookie({ action: "setCookie" });
        localStorage.setItem("subjectId", response.data.mainSubjectId);
        // localStorage.setItem("surveyStatus", response.data.status);
        localStorage.setItem("loginError", false);
        store.commit("SNS_LOGIN", response.data.accessToken);
        router.push({ path: "/home" });
      }
    } catch (error) {
      store.commit("setLoginErrorMsg", "login");
      localStorage.setItem("loginError", true);
      console.log(error);
      new Error("SNSLOGIN request failed");
    }
  },

  SET_TOKEN({ commit }, token) {
    return commit("SET_TOKEN", token);
  },
};
