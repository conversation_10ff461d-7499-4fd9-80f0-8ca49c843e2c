export default {
  GET_PHONE(state, phone) {
    state.join.phone = phone;
  },

  GET_COUNTRY(state, country) {
    state.join.country = country;
  },

  GET_USERID(state, userId) {
    state.join.account = userId;
  },

  GET_PASSWORD(state, password) {
    state.join.password = password;
  },

  // GET_NICKNAME(state, nickname) {
  //   state.join.nickname = nickname;
  // },

  GET_MARKETING_ALLOW(state, terms) {
    state.join.marketingAllow = terms.marketingAllow;
  },

  GET_USER_INFO(state, userInfo) {
    state.userImage = userInfo.image;
    state.username = userInfo.username;
  },

  GET_USER_DETAIL(state, userInfo) {
    state.join.userDetail.nickname = userInfo.nickname;
    state.join.userDetail.sex = userInfo.sex;
    state.join.userDetail.birth = userInfo.birth;
    state.join.userDetail.height = userInfo.height;
    state.join.userDetail.initialWeight = userInfo.initialWeight;
    state.join.userDetail.targetWeight = userInfo.targetWeight;
    state.join.userDetail.targetWater = userInfo.targetWater;
  },

  GET_HEALTH_INFO_PURPOSE(state, purpose) {
    state.join.health_info.purpose = purpose;
  },
  GET_HEALTH_INFO_EXERCISE(state, exercise) {
    state.join.health_info.exercise = exercise;
  },
  GET_HEALTH_INFO_CHRONIC(state, chronic) {
    state.join.health_info.chronic = chronic;
  },
  GET_HEALTH_INFO_DRINKING(state, drinking) {
    state.join.health_info.drinking = drinking;
  },
  GET_HEALTH_INFO_SMOKING(state, smoking) {
    state.join.health_info.smoking = smoking;
  },
  GET_HEALTH_INFO(state, info) {
    state.join.health_info.purpose = info.purpose;
    state.join.health_info.exercise = info.exercise;
    state.join.health_info.chronic = info.chronic;
    state.join.health_info.drinking = info.drinking;
    state.join.health_info.smoking = info.smoking;
  },

  GET_MEAL_INFO(state, info) {
    state.join.meal_info = info;
  },

  GET_JOIN_OPINION(state, opinion) {
    state.join.opinion = opinion;
  },
};
