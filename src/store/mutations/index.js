import authMutations from "./auth";
import joinMutation from "./join";
import cym702 from "./cym702.js";
import { careActions } from "./care.actions.js";

export default {
  ...authMutations,
  ...joinMutation,
  ...cym702,
  ...careActions,
  setAppType(state, type) {
    state.appType = type;
  },
  setDeviceId(state, id) {
    state.deviceId = id;
  },
  setNotiStatus(state, status) {
    state.notiStatus = status;
  },
  setHistoryTabChanged(state, status) {
    state.historyTabChanged = status;
  },
  setBackBtnClicked(state, status) {
    state.backBtnClicked = status;
  },
  setSubjectId(state, id) {
    state.subjectId = id;
  },
  setGroupId(state, id) {
    state.groupId = id;
  },
  setSurveyStatus(state, status) {
    state.surveyStatus = status;
  },
  setSurveyModal(state, isOpen) {
    state.surveyModalOpen = isOpen;
  },
  setAnalysisWarningModal(state, status) {
    state.analysisWarningModalOpen = status;
  },
  setLastAnalysisTime(state, createdAt) {
    state.lastAnalysisTime = createdAt;
  },
  setoverReqModal(state, isOpen) {
    state.overReqModalOpen = isOpen;
  },
  setLoginError(state, status) {
    state.loginError = status;
  },
  setLoginErrorMsg(state, msg) {
    state.loginErrorMsg = msg;
  },
  IMAGEUPLOAD(state, image) {
    state.userImage = image;
  },
  SHOWGUIDE(state) {
    state.showCymscore = true;
  },

  CLOSEGUIDE(state) {
    state.showCymscore = false;
  },

  EMAILSIGN(state) {
    state.alreadySigned = true;
  },

  setCurPw(state, pw) {
    state.password = pw;
  },

  closeAlert(state) {
    state.alreadySigned = false;
  },

  completeAlert(state) {
    state.showCompleteModal = true;
  },

  setIsBoat(state, status) {
    state.isBoat = status;
  },

  closeOverlayHandler(state) {
    state.isCompleted = false;
  },

  bluetoothSuccess(state) {
    state.isSuccess = true;
  },

  openKetoneEditModal(state, status) {
    state.openKetoneEditModal = status;
  },

  openGuideModal(state) {
    state.openGuide = true;
  },

  closeGuideModal(state) {
    state.openGuide = false;
  },

  getCardTitle(state, title) {
    state.cardTitle = title;
  },

  enableTouch(state) {
    state.isEnableTouch = false;
  },

  disableTouch(state) {
    state.isEnableTouch = true;
  },

  openSelectModal(state) {
    state.showSelectModal = true;
  },

  closeSelectModal(state) {
    state.showSelectModal = false;
  },
  showSelectBtn(state) {
    state.showSelectBtn = true;
  },
  hideSelectBtn(state) {
    state.showSelectBtn = false;
  },
  getFoodCard(state, foodInfo) {
    state.selectedFood = foodInfo;
  },
  selectedFoodId(state, foodId) {
    state.selectedFoodId = foodId;
  },
  clickTitle(state, isClicked) {
    state.clickTitle = isClicked;
  },
  selectionBtn(state, status) {
    state.selectionBtn = status;
  },
  recordSelectBtn(state, status) {
    state.recordSelectBtn = status;
  },
  showRecordSelectModal(state, status) {
    state.showRecordSelectModal = status;
  },
  recordSelectType(state, status) {
    state.recordSelectType = status;
  },
  filterBtn(state, status) {
    state.filterBtn = status;
  },
  setAmountRatio(state, percentage) {
    state.FoodAmountPercentage = [...state.FoodAmountPercentage, percentage];
  },
  getUserName(state, username) {
    state.username = username;
  },
  GET_CUR_WEIGHT(state, weight) {
    state.weight = weight;
  },
  getUserPhone(state, phone) {
    state.phone = phone;
  },
  getUserImage(state, img) {
    state.userImg = img;
  },
  setSelectUser(state, id) {
    state.selectUser = id;
  },
  setSubUserId(state, id) {
    state.subUserId = id;
  },
  setSubUser(state, name) {
    state.subUser = name;
  },
  setSubUserGender(state, gender) {
    state.subUserGender = gender;
  },
  setSubUserBirth(state, birth) {
    state.subUserBirth = birth;
  },
  setSubUserType(state, type) {
    state.subUserType = type;
  },
  SET_CONNECTION_MODAL(state, status) {
    state.snsConnectionModal = status;
  },
  SET_SNS_UID(state, uid) {
    state.snsUid = uid;
  },
  SET_SELECT_DATE_MODAL(state, status) {
    state.setting.showSelectDateModal = status;
  },
  SET_BMI_INFO_MODAL(state, status) {
    state.setting.showBmiInfoModal = status;
  },
  SET_BLOOD_PRESSURE_CATEGORY_MODAL(state, status) {
    state.setting.showBloodPressureCategoryModal = status;
  },
};
