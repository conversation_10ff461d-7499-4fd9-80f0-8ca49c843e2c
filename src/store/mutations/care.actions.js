export const careActions = {
  /**
   * Sets the visibility of blood pressure record modal.
   * @param {string} state - Vuex state key
   * @param {boolean} status - true to show, false to hide
   */
  setRecordBloodPressureData(state, status) {
    state.care.recordModalVisible = status;
  },

  /**
   * Sets the visibility of blood pressure goal modal.
   *
   * @params {string} state - Vuex state key
   * @params {boolean} status - true to show, false to hide (default: false)
   */
  setIsShowBloodPressurePopup(state, status = false) {
    state.care.isShowBloodPressurePopup = status;
  },
};
