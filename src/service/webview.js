const userAgent = navigator.userAgent.toLowerCase();
// console.log(userAgent)

// Web -> App Bridge
export default {
  AndroidQuitApp() {
    if (userAgent.indexOf("android") !== -1) window.Android.goBack();
  },
  getDeviceId(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.getDeviceId();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  notiAuthorized(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.notiAuthorized();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // 카카오 로그인 브릿지
  kakaoLogin(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.loginKakao();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // 회원 탈퇴 -> 카카오 회원 탈퇴, 카카오 서비스 연결 해제용 브릿지
  // message => { action: kakaoMemberOut }
  kakaoMemberOut(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.kakaoMemberOut();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // 구글 로그인 브릿지
  googleLogin(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.loginGoogle();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // 애플 로그인 브릿지
  appleLogin(message) {
    if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // 검사하기 카메라 기능 열기 브릿지
  openCamera(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.openCamera(JSON.stringify(message));
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // 유저프로필 이미지 변경 시 갤러리 접근 권한 허용 시점
  // allowUseGallery(message) {
  //   if (userAgent.indexOf("android") !== -1) {
  //     window.Android.allowUseGallery();
  //   } else if (userAgent.indexOf("iphone") !== -1 || userAgent.indexOf("ipad") !== -1) {
  //     window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
  //   }
  // },
  // 고객센터 문의하기 카카오 브릿지
  goKakaoChannel(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.openBrowser(JSON.stringify(message));
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // 회사 소개 페이지 이동을 위한 앱 내의 기본 브라우저 활용 브릿지
  goCym702(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.openBrowser(JSON.stringify(message));
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // 네이버 스마트 스토어 연결
  goCym702Store(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.openBrowser(JSON.stringify(message));
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // 앱 외부 링크로 연결되어있을 때 브릿지
  goOutLick(message) {
    const isAndroid = userAgent.indexOf("android") !== -1;
    const isIos =
      userAgent.indexOf("iphone") !== -1 || userAgent.indexOf("ipad") !== -1;
    if (isAndroid) {
      window.Android.openBrowser(JSON.stringify(message));
      return;
    }

    if (isIos) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
      return;
    }
  },
  // 디바이스 연결 BLE 사용 브릿지
  blueConnect(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.bluetoothEnable(JSON.stringify(message));
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      // console.log("iphone: " + window.webkit.messageHandlers.webViewScriptHandler);
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },

  callDeviceInfo(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.callDeviceInfo();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // 네이티브 영역 언어 설정 브릿지
  setLanguage(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.setLanguage(JSON.stringify(message));
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  connectHealthApp(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.connectHealthApp();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  disconnectHealthApp(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.disconnectHealthApp();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  onUrineTestAlert(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.onUrineTestAlert();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  offUrineTestAlert(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.offUrineTestAlert();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  setUrineTestTimeAlert(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.setUrineTestTimeAlert(JSON.stringify(message));
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  onPushAlert(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.onPushAlert();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // 마케팅 알림 수신 브릿지(topic: marketing)
  onMarketing(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.onMarketing();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // 공지사항 알림 수신 브릿지(topic: notice)
  onNotice(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.onNotice();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  offPushAlert(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.offPushAlert();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // 마케팅 알림 수신 거부 브릿지(topic: marketing)
  offMarketing(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.offMarketing();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // 공지사항 알림 수신 거부 브릿지(topic: notice)
  offNotice(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.offNotice();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  shareSns(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.shareSns();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  setCookie(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.setCookie();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  clearCookie(message) {
    if (userAgent.indexOf("android") !== -1) {
      window.Android.clearCookie();
    } else if (
      userAgent.indexOf("iphone") !== -1 ||
      userAgent.indexOf("ipad") !== -1
    ) {
      window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
    }
  },
  // Samsung Health
  sendUPH() {
    window.Android.sendUPH();
  },

  // 데이터 형식
  // userInfo: access_token, date: 2000-01-01
  getSteps(userInfo, subjectId, date) {
    window.Android.getSteps(userInfo, subjectId, date);
  },
  getBloodPressures(userInfo, subjectId, date) {
    window.Android.getBloodPressures(userInfo, subjectId, date);
  },
};
