import router from "../router/index.js";
import store from "../store/index.js";
import i18n from "../../src/i18n.js";
import webview from "./webview.js";

// App -> Web Bridge
export default {
  backBtnClicked() {
    // console.log("back button history length");
    // console.log(window.history.length);
    const path = window.location.pathname;
    if (window.history.length === 1) {
      if (
        path === "/exam/intro" ||
        path === "/exam/video" ||
        path === "/exam/guide1" ||
        path === "/exam/guide2" ||
        path === "/exam/wait" ||
        path === "/solution" ||
        path === "/mypage"
      ) {
        router.push("/home");
      } else if (
        path.includes("subprofile") ||
        path === "/profile" ||
        path === "/help" ||
        path === "/mypage/notice" ||
        path === "/mypage/settings" ||
        path === "/mypage/examalert"
      ) {
        router.push("/mypage");
      } else if (path === "/mypage/settings/alert") {
        router.push("/mypage/settings");
      } else if (path === "/join") {
        router.go(-1);
      } else if (path === "/home/<USER>") {
        if (store.state.openKetoneEditModal) {
          store.commit("openKetoneEditModal", false);
        } else if (store.state.openGuide) {
          store.commit("closeGuideModal");
        } else router.go(-1);
      } else if (path === "/home" || path === "/login") {
        webview.AndroidQuitApp();
      }
    } else if (path === "/mypage" || path === "/solution" || path === "/exam/intro") {
      router.push("/home");
    } else if (path === "/find") {
      router.push("/login");
    } else if (path === "/join") {
      router.go(-1);
    } else if (path === "/home" || path === "/login") {
      webview.AndroidQuitApp();
    } else if (path === "/home/<USER>") {
      if (store.state.openKetoneEditModal) {
        store.commit("openKetoneEditModal", false);
      } else if (store.state.openGuide) {
        store.commit("closeGuideModal");
      } else router.go(-1);
    } else if (path === "/home/<USER>/weight" || path === "/home/<USER>/water" || path === "/home/<USER>/pee") {
      // alert(store.state.backBtnClicked);
      !store.state.backBtnClicked ? store.commit("setBackBtnClicked", true) : router.go(-1);
    } else router.go(-1);

    // 모달 보이는 페이지에서 뒤로가기 처리
    // path === "/home" || path === "/login"
    //   ? webview.AndroidQuitApp()
    //   : router.go(-1);
  },
  getDeviceId(obj) {
    // console.log("device id:");
    // console.log(obj.id);
    store.commit("setDeviceId", obj.id);
  },
  notiAuthorized(status) {
    // console.log("native noti status:", status);
    store.commit("setNotiStatus", JSON.parse(status));
  },
  openAlertModal() {
    // console.log("alert clicked");
    // router.push("/exam/intro");
    // for WADIZ
    router.push("/home");
  },
  getLanguage(lang) {
    // console.log(lang);
    localStorage.setItem("locale", lang);
    i18n.locale = lang;
    // console.log("localStorage get value locale:");
    // console.log(localStorage.getItem("locale"));
  },
  getType(type) {
    // console.log("App type = ");
    // console.log(type);
    store.commit("setAppType", type);
    localStorage.setItem("type", type);
  },
  // 검사 완료에 대한 response 브릿지
  successAnalysis(message) {
    // console.log("success Analysis message:");
    // console.log(message);
    message === "boat" ? store.commit("setIsBoat", true) : store.commit("setIsBoat", false);
    store.commit("completeAlert");
    router.push("/home?analysis=done");
  },
  pageMainReload() {
    router.go(router.currentRoute);
    store.commit("bluetoothSuccess");
  },
  writeDeviceInfo(device) {
    store.dispatch("GETDEVICEINFO", device);
    // console.log("deviceInfo:", device);
  },

  // ? 카카오 로그인 완료 후 데이터 받기용 브릿지
  kakaoSignResponse(kakaoSignData) {
    // console.log("kakao data:");
    // console.log(kakaoSignData);
    // * kakaoSignResponse: id, profile, name, email, birthyear, birthday, gender, valid, status;
    // ! status === 201 DB에 미존재(유효함): 회원가입, 연동
    if (kakaoSignData.status === 201) {
      if (!localStorage.auth) {
        // 회원가입
        router.push({
          name: "Join",
          params: {
            id: kakaoSignData.id,
            snsType: "kakao",
            email: kakaoSignData.email || "",
          },
        });
      } else {
        // SNS 연동
        console.log("kakao uid");
        console.log(kakaoSignData.id);
        store.commit("SET_SNS_UID", String(kakaoSignData.id));
      }
    }
    // ! status === 403 이미 DB에 존재(유효하지 않음): 로그인, 연동 불가
    else if (kakaoSignData.status === 403) {
      if (!localStorage.auth) {
        const account = { account: String(kakaoSignData.id) };
        const snsType = "kakao";
        const appType = store.state.appType !== "" ? store.state.appType : "human";
        store
          .dispatch("SNS_LOGIN", { snsType, appType, account })
          .then((res) => {
            localStorage.setItem("snsType", snsType);
            localStorage.setItem("isSnsLogin", true);
            router.push({ path: "/home" });
          })
          .catch((message) => {
            console.log(message);
          });
      } else {
        store.commit("SET_CONNECTION_MODAL", true);
      }
    } else {
      console.log("유저정보 조회 실패");
    }
  },

  // ? 구글 로그인 후 데이터 수신
  googleSignResponse(googleSignData) {
    // console.log("google data:");
    // console.log(googleSignData);
    // ! status === 201 DB에 미존재(유효함): 회원가입
    if (googleSignData.status === 201) {
      if (!localStorage.auth) {
        router.push({
          name: "Join",
          params: {
            id: String(googleSignData.uid),
            snsType: "google",
            email: googleSignData.email || "",
            profileImg: googleSignData.photoURL || "",
            name: googleSignData.displayName || "",
          },
        });
      } else {
        store.commit("SET_SNS_UID", String(googleSignData.uid));
      }
    }
    // ! status === 403 이미 DB에 존재(유효하지 않음): 로그인
    else if (googleSignData.status === 403) {
      // console.log("google uid:");
      // console.log(googleSignData.uid);
      if (!localStorage.auth) {
        const account = { account: String(googleSignData.uid) };
        const snsType = "google";
        const appType = store.state.appType !== "" ? store.state.appType : "human";
        store
          .dispatch("SNS_LOGIN", { snsType, appType, account })
          .then((res) => {
            localStorage.setItem("snsType", snsType);
            localStorage.setItem("isSnsLogin", true);
            router.push({ path: "/home" });
          })
          .catch((message) => {
            console.log(message);
          });
      } else {
        store.commit("SET_CONNECTION_MODAL", true);
      }
    } else {
      console.log("유저정보 조회 실패");
    }
  },

  // * appleSignResponse: useridentifier, fullName, email, valid, status
  // ? 애플 로그인 후 데이터 수신
  appleSignResponse(appleSignData) {
    // console.log("apple data:");
    // console.log(appleSignData);

    if (appleSignData.status === 201) {
      if (!localStorage.auth) {
        router.push({
          name: "Join",
          params: {
            id: appleSignData.useridentifier,
            snsType: "apple",
            email: appleSignData.email || "",
            profileImg: appleSignData.photoURL || "",
            name: appleSignData.fullName || "",
          },
        });
      } else {
        store.commit("SET_SNS_UID", String(appleSignData.useridentifier));
      }
    }
    // ! status === 403 이미 DB에 존재(유효하지 않음): 로그인
    else if (appleSignData.status === 403) {
      if (!localStorage.auth) {
        const account = { account: String(appleSignData.useridentifier) };
        const snsType = "apple";
        const appType = store.state.appType !== "" ? store.state.appType : "human";
        store
          .dispatch("SNS_LOGIN", { snsType, appType, account })
          .then((res) => {
            localStorage.setItem("snsType", snsType);
            localStorage.setItem("isSnsLogin", true);
            router.push({ path: "/home" });
          })
          .catch((message) => {
            console.log(message);
          });
      } else {
        store.commit("SET_CONNECTION_MODAL", true);
      }
    } else {
      console.log("유저정보 조회 실패");
    }
  },

  // 카메라 검사하기에서 뒤로가기 시 검사 시작하기 페이지 이동용 브릿지
  gotoAnalizeStart() {
    router.push("/exam/wait");
  },
  // 자동 로그인 브릿지
  autologin(user) {
    return user;
  },

  // 알림 허용여부
  notificationStatus(status) {
    // store.dispatch("")
    // console.log("noti:");
    // console.log(status);
  },
};
