<template>
  <div>
    <div class="wrapper">
      <div class="container">
        <div class="water-controller">
          <div class="water-subtract__btn">
            <img @click="subtractPee" src="@/assets/images/minus_circle.png" />
          </div>
          <div class="glass-mask"></div>

          <div class="water-plus__btn">
            <img @click="addPee" src="@/assets/images/plus_circle.png" />
          </div>
        </div>
        <div class="water-glass__img--large">
          <img src="@/assets/images_assets/images/glass_mask.png" alt="glass_mask" />
        </div>
        <div class="circle">
          <div class="wave" ref="water"></div>
          <div class="value-txt" ref="valueTxt">{{ waterValue }}ml</div>
        </div>
        <div class="save-btn" v-if="showSaveBtn" @click="saveBtnHandler">
          <img src="@/assets/images_assets/icons/care-save-btn-ic.png" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    type: String,
    volume: Number,
  },
  data() {
    return {
      waterQuantity: 0,
      // totalValue: 2000,
      amount: 0,
      waterValue: 0,
      topPosition: 255,
      plusCount: 0,
      count: 0,
      showSaveBtn: false,
    };
  },
  computed: {
    glassImgWidthValue() {
      return this.path === "home" ? "70px" : "270px";
    },
  },
  watch: {
    waterValue(newVal) {
      newVal >= 1100 ? (this.$refs.valueTxt.style.color = "#fff") : null
      this.volume === newVal ? (this.showSaveBtn = false) : null;
    },
  },

  methods: {
    addPee() {
      this.showSaveBtn = true;
      if (this.count < 10) {
        this.count = this.count += 1;
        this.plusCount = this.plusCount += 1;
        this.waterValue = this.waterValue + 100;
        this.topPosition = this.computedTopPosition(this.waterValue);
        this.$refs.water.style.top = `${this.topPosition}px`;
        if (this.waterValue >= 1100) {
          this.$refs.valueTxt.style.color = "#fff";
        }
      } else {
        this.showSaveBtn = false;
      }
    },

    subtractPee() {
      this.showSaveBtn = true;
      if (this.waterValue > 0) {
        if (this.waterValue > this.volume) {
          this.count = this.count -= 1;
          this.plusCount = this.plusCount -= 1;
          this.waterValue = this.waterValue - 100;
          this.topPosition = this.computedTopPosition(this.waterValue);
          this.$refs.water.style.top = `${this.topPosition}px`;
          if (this.plusCount < 1100) {
            this.$refs.valueTxt.style.color = "#41d8e6";
          }
        } else {
          this.showSaveBtn = false;
        }
      }
    },
    async saveBtnHandler() {
      this.showSaveBtn = false;
      const waterValue = this.count * 100;
      this.$emit("saveBtnHandler", "water", waterValue);
    },
    computedTopPosition(val) {
      const increaseValue = 255 / 20;
      const currentVolume = val > 2000 ? 2000 / 100 : val / 100;
      const calculateVolume = currentVolume * increaseValue;
      const topPosition = 255 - (calculateVolume + 12.5);

      return topPosition;
    },
  },
  mounted() {
    this.waterValue = this.volume;
    this.topPosition = this.computedTopPosition(this.waterValue);
    this.$refs.water.style.top = `${this.topPosition}px`;
    this.waterValue >= 1100 ? (this.$refs.valueTxt.style.color = "#fff") : null;
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  height: 250px;
  margin-bottom: 40px;
}

.water-controller {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 100%;
  padding: 0 20px;
}

.water-glass__img--large {
  position: absolute;
  z-index: 1;
  overflow: hidden;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  img {
    width: 270px;
  }
}

.water-subtract__btn {
  position: absolute;
  z-index: 2;
  bottom: -15px;
  left: 20px;
  padding-left: 10px;
  img {
    width: 50px;
  }
}

.water-plus__btn {
  position: absolute;
  z-index: 2;
  bottom: -15px;
  right: 20px;
  padding-right: 10px;
  img {
    width: 50px;
  }
}

.circle {
  background-color: #f8f8f8;
  position: absolute;
  width: 200px;
  max-width: 100%;
  height: 240px;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
  overflow: hidden;
  transition: all 1s;
}

.wave {
  position: absolute;
  width: 300px;
  height: 300px;
  border-radius: 43%;
  left: 50%;
  transform: translateX(-50%);
  background: #41d8e6;
  animation: wave 6s infinite linear;
  transition: all 1s;
  opacity: 0.7;
}

.value-txt {
  position: absolute;
  font-family: GilroyBold;
  top: 55%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s;
  font-size: 26px;
  color: #41d8e6;
  z-index: 99;
}

.save-btn {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 99;

  img {
    width: 60px;
  }
}

@keyframes wave {
  0% {
    transform: translate(-50%) rotate(-90deg);
  }

  100% {
    transform: translate(-50%) rotate(360deg);
  }
}
</style>
