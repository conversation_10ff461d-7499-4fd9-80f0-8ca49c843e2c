<template>
  <div>
    <ErrorModal v-show="showErrorModal" :error="error" @isClicked="isClicked" />
    <div class="wrapper">
      <div class="container">
        <div class="water-controller">
          <div class="water-subtract__btn">
            <img @click="subtractPee" src="@/assets/images/minus_circle.png" />
          </div>
          <div class="waterdrop-mask__img--large">
            <img src="@/assets/images_assets/images/pee_mask.png" alt="glass_mask" />
          </div>

          <div class="water-plus__btn">
            <img @click="addPee" src="@/assets/images/plus_circle.png" />
          </div>
        </div>
        <div class="circle">
          <div class="wave" ref="pee"></div>
          <div class="value-txt" ref="valueTxt">{{ peeValue }} {{ $t("times") }}</div>
        </div>
        <div class="save-btn" v-if="showSaveBtn" @click="saveBtnHandler">
          <img src="@/assets/images_assets/icons/care-save-btn-ic.png" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ErrorModal from "@/components/Common/ErrorModal.vue";

export default {
  components: { ErrorModal },
  props: {
    type: String,
    volume: Number,
  },
  data() {
    return {
      waterQuantity: 0,
      totalValue: 8,
      amount: 0,
      peeValue: 0,
      topPosition: 245,
      showSaveBtn: false,
      count: 0,
      showErrorModal: false,
      error: this.$i18n.t("pee_error_msg"),
    };
  },

  computed: {
    setPeeValue() {
      return this.computedTopPosition(this.peeValue);
    },
  },
  watch: {
    peeValue(newVal) {
      newVal >= 5 ? (this.$refs.valueTxt.style.color = "#fff") : null;
      this.volume === newVal ? (this.showSaveBtn = false) : null;
    },
    count(n) {
      // console.log(n);
    },
  },

  methods: {
    addPee() {
      this.showSaveBtn = true;
      if (this.count === 0) {
        this.count = this.count += 1;
        this.peeValue = this.peeValue += 1;
        this.topPosition = this.computedTopPosition(this.peeValue);
        this.$refs.pee.style.top = `${this.topPosition}px`;
        if (this.peeValue >= 5) {
          this.$refs.valueTxt.style.color = "#fff";
        }
      } else {
        this.showErrorModal = true;
      }
    },
    subtractPee() {
      if (this.peeValue > 0) {
        if (this.peeValue > this.volume) {
          this.showSaveBtn = true;
          this.count = this.count -= 1;
          this.peeValue = this.peeValue -= 1;
          this.topPosition = this.computedTopPosition(this.peeValue);
          this.$refs.pee.style.top = `${this.topPosition}px`;
          if (this.peeValue < 5) {
            this.$refs.valueTxt.style.color = "#ffcc00";
          }
        } else if (this.count === 0) {
          this.showSaveBtn = false;
          // this.showErrorModal = true;
        } else {
          this.showErrorModal = true;
        }
      }
    },
    saveBtnHandler() {
      this.showSaveBtn = false;
      this.$emit("saveBtnHandler", "urine", this.peeValue);
    },
    computedTopPosition(val) {
      //4. 2000 / 100 => 8번을 클릭해야 물이 꽉찬다.
      const totalAmount = this.totalValue;

      //5. 그럼 어느정도 감소해야하는지? 200 / 8 =? 12.5
      const increaseValue = 150 / totalAmount;

      //6. 그럼 어느정도 감소해야하는지? 18
      const currentVolume = val;
      const calculateVolume = currentVolume * increaseValue;
      const topPosition = 245 - calculateVolume;

      if (topPosition < 0) {
        return 0;
      } else {
        return topPosition;
      }
    },
    isClicked() {
      this.showErrorModal = false;
    },
  },
  mounted() {
    this.peeValue = this.volume;
    this.topPosition = this.computedTopPosition(this.peeValue);
    this.$refs.pee.style.top = `${this.setPeeValue}px`;
    this.peeValue >= 5 ? (this.$refs.valueTxt.style.color = "#ffffff") : (this.$refs.valueTxt.style.color = "#ffcc00");
  },
};
</script>

<style lang="scss" scoped>
.chart__wrapper {
  padding-bottom: 10px;
}

.gauge-chart__wrapper {
  position: absolute;
  top: 0;
}

.wrapper {
  max-height: 300px;
}

.container {
  position: relative;
  height: 250px;
  margin-bottom: 40px;
}

.water-controller {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 100%;
  padding: 0 20px;
}

.waterdrop-mask__img--large {
  width: 100%;
  max-width: 260px;
  // min-height: 260px;
  position: absolute;
  z-index: 1;
  top: 0px;
  overflow: hidden;
  img {
    object-fit: contain;
    width: 260px;
  }
}

.water-subtract__btn {
  position: absolute;
  z-index: 9;
  bottom: 0px;
  left: 10px;
  padding-left: 20px;
  img {
    width: 50px;
  }
}

.water-plus__btn {
  position: absolute;
  z-index: 2;
  bottom: 0px;
  right: 10px;
  padding-right: 20px;
  img {
    width: 50px;
  }
}

.circle {
  background-color: #f8f8f8;
  position: absolute;
  width: 200px;
  max-width: 100%;
  height: 250px;
  top: 0px;
  left: 50%;
  transform: translateX(-50%);
  overflow: hidden;
  transition: all 1s;
}

.wave {
  position: absolute;
  width: 300px;
  height: 300px;
  background: #ffcc00;
  border-radius: 43%;
  left: 50%;
  transform: translateX(-50%);
  animation: wave 6s infinite linear;
  transition: all 1s;
  opacity: 0.7;
}

.value-txt {
  position: absolute;
  letter-spacing: -0.03em;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  font-weight: bold;
  color: #ffcc00;
  z-index: 99;
}

.save-btn {
  position: absolute;
  top: 55%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 99;

  img {
    width: 60px;
  }
}
@keyframes wave {
  0% {
    transform: translate(-50%) rotate(-90deg);
  }

  100% {
    transform: translate(-50%) rotate(360deg);
  }
}
</style>
