<template>
  <router-link class="router-link" :to="{ path: routeLink }">
    <div class="card__wrapper" :class="{ lastItem: isLast }">
      <div class="left-container">
        <div class="img-container">
          <img
            :src="require(`@/assets/images/care/main_${title}.png`)"
            :alt="`${title}_icon_img`"
            loading="lazy"
          />
        </div>
        <div :class="isKo ? `careTitle` : `careTitle_en`">
          {{ $t(`${title}_title`) }}<span class="careTitle_en"> Care</span>
        </div>
      </div>
      <div class="right-arrow">
        <img src="@/assets/images/right_chevron.png" />
      </div>
    </div>
  </router-link>
</template>

<script>
export default {
  props: {
    title: String,
    routeLink: String,
    isLast: Boolean,
  },

  data: () => ({
    isKo: false,
    snackbar: false,
    overOnce: false,
  }),

  mounted() {
    this.isKo = this.$i18n.locale === "ko";
  },
};
</script>

<style lang="scss" scoped>
.router-link {
  cursor: pointer;
}

.card__wrapper {
  width: 100%;
  display: flex;
  padding: 40px 25px 0 30px !important;
  justify-content: space-between;
  align-items: center;
}

.lastItem {
  padding: 40px 25px 50px 30px !important;
}

.left-container {
  display: flex;
  align-items: center;
  color: #646464;
  gap: 20px;
  img {
    height: 60px;
    object-fit: contain;
  }
}

.img-container {
  width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.right-arrow {
  img {
    height: 15px;
    object-fit: contain;
  }
}

.careTitle {
  font-size: 18px;
  line-height: 22px;
  font-weight: 700;
}

.careTitle_en {
  font-size: 21.5px;
  line-height: 22px;
  font-family: GilroyBold !important;
}
</style>
