<template>
  <div>
    <apexchart
      width="100%"
      height="70%"
      type="line"
      :options="chartOptions"
      :series="series"
      ref="homeChart"
    ></apexchart>
  </div>
</template>

<script>
export default {
  props: {
    chartData: Array,
  },
  data() {
    const vm = this;
    return {
      loaded: false,
      series: [
        {
          name: "chart",
          data: vm.chartData,
        },
      ],
      chartOptions: {
        chart: {
          toolbar: {
            show: false,
          },
          dropShadow: {
            enabled: true,
            color: "#A7A7A7",
            top: 5,
            left: 0,
            blur: 2,
            opacity: 0.15,
          },
          parentHeightOffset: -5,
        },
        markers: {
          size: 4.5,
          strokeColor: ["#41d8e6"],
          colors: ["#fff"],
          hover: {
            size: 5.5,
          },
        },
        stroke: {
          width: 2.5,
          lineCap: "butt",
        },
        xaxis: {
          tooltip: {
            enabled: false,
          },
          labels: {
            show: false,
          },
          axisBorder: {
            show: false,
          },
          axisTicks: {
            show: false,
          },
        },
        yaxis: {
          labels: {
            show: false,
          },
          min: 0,
          max: 100,
          tickAmount: 4,
        },
        colors: ["#41d8e6"],

        grid: {
          yaxis: {
            lines: {
              show: true,
              colors: ["#a7a7a7"],
            },
          },
          strokeDashArray: 2,
        },
        tooltip: {
          enabled: true,
          custom({ series, seriesIndex, dataPointIndex, w }) {
            const classNameAddW30RemoveW22 = (element) => {
              return element.classList.add("w-30"), element.classList.remove("w-22");
            };
            const classNameRemoveW30AddW22 = (element) => {
              return element.classList.remove("w-30"), element.classList.add("w-22");
            };

            const bloodDroplets = document.querySelectorAll(".blood");
            const proteinDroplets = document.querySelectorAll(".protein");
            const glucoseDroplets = document.querySelectorAll(".glucose");
            const phDroplets = document.querySelectorAll(".ph");
            const ketoneDroplets = document.querySelectorAll(".ketone");

            const pointIdx = vm.chartData.length < 5 ? dataPointIndex + 5 - vm.chartData.length : dataPointIndex;

            bloodDroplets.forEach((_, index) => {
              // console.log(pointIdx, index);
              if (pointIdx === index) {
                classNameAddW30RemoveW22(bloodDroplets[index].children[0]);
                classNameAddW30RemoveW22(proteinDroplets[index].children[0]);
                classNameAddW30RemoveW22(glucoseDroplets[index].children[0]);
                classNameAddW30RemoveW22(phDroplets[index].children[0]);
                classNameAddW30RemoveW22(ketoneDroplets[index].children[0]);
              } else {
                classNameRemoveW30AddW22(bloodDroplets[index].children[0]);
                classNameRemoveW30AddW22(proteinDroplets[index].children[0]);
                classNameRemoveW30AddW22(glucoseDroplets[index].children[0]);
                classNameRemoveW30AddW22(phDroplets[index].children[0]);
                classNameRemoveW30AddW22(ketoneDroplets[index].children[0]);
              }
            });
            vm.$store.commit("GET_ACTIVE_DROPLET_IDX", pointIdx);
            // console.log("origin data", dataPointIndex, vm.chartData.length);
            return "";
          },
        },
      },
    };
  },
  watch: {
    chartData(old, newVal) {
      // console.log(newVal)
      this.chartData = old;
      this.updateSeriesLine();
    },
  },
  methods: {
    updateSeriesLine() {
      this.$refs.homeChart.updateSeries([{ data: this.chartData }], false, true);
    },
  },
};
</script>

<style scoped>
::v-deep .apexcharts-xcrosshairs {
  stroke: transparent !important;
}
::v-deep path {
  filter: none !important;
}
</style>
