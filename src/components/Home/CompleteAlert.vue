<template>
  <div>
    <div class="complete-exam__bg-modal">
      <div class="alert-window">
        <div class="alert-window__txt">
          <span>{{ $t("success_analysis") }}</span> <br />
          <span v-if="isBoat && isKo">{{ username }}{{ $t("health_score") }}</span>
          <span v-if="isBoat && !isKo" v-html="this.$i18n.t('health_score')"></span>
        </div>
        <div v-if="isBoat" class="emblem-score-container">
          <div class="cym-emblem__wrapper">
            <img src="@/assets/images/cym702_logo/emblem.png" alt="" />
          </div>
          <div class="cym-score__wrapper">
            <span class="score">{{ returnTotalScore }}</span>
            <span class="standard-score">/100</span>
          </div>
        </div>
        <div v-else-if="isPortable">
          <div class="portable-done" v-html="content"></div>
        </div>
        <div class="alert-window__btn">
          <button @click="closeModalWindow">{{ $t("go_to_result") }}</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    totalScore: Number,
    username: String,
    // isBoat: Boolean,
  },
  data() {
    return { content: this.$i18n.t("portable_modal_message"), isKo: false };
  },
  computed: {
    returnTotalScore() {
      return this.$store.state.totalScore;
    },
    isBoat() {
      return this.$store.state.isBoat;
    },
    isPortable() {
      return !this.$store.state.isBoat;
    },
  },
  methods: {
    closeModalWindow() {
      this.$store.commit("CLOSE_COMPLETE_MODAL");
      this.$emit("closeOverlayHandler");
      /*global Webview*/
      /*eslint no-undef: "error"*/
      Webview.sendUPH();
    },
  },
  mounted() {
    this.isBoat = this.$store.state.isBoat;
    this.isKo = this.$i18n.locale === "ko";
  },
};
</script>

<style lang="scss" scoped>
.complete-exam__bg-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 450px;
  height: 100%;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-window {
  background-color: #fff;
  width: 300px;
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 37px 6px;
}

.alert-window__txt {
  font-size: 16px;
  line-height: 23px;
  text-align: center;
  letter-spacing: -0.03em;
}

.alert-window__btn > button {
  width: 170px;
  background: #41d8e6;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  border: none;
  text-align: center;
  letter-spacing: -0.03em;
  color: #ffffff;
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 26px;
  padding: 10px 15px;
}

.emblem-score-container {
  margin: 37px 0px;
  position: relative;
}

.cym-emblem__wrapper > img {
  max-width: 158px;
  width: 100%;
}

.cym-score__wrapper {
  width: 100%;
  position: absolute;
  text-align: center;
  text-indent: 35px;
  top: 50%;
  transform: translateY(-50%);
}

.score {
  font-size: 36px;
  font-family: GilroyBold;
  font-weight: bold;
}

.standard-score {
  font-size: 14px;
  font-family: GilroyMedium;
}

.portable-done {
  background-color: #c9f4f8;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  font-weight: 500;
  font-size: 24px;
  line-height: 35px;
  margin: 25px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
