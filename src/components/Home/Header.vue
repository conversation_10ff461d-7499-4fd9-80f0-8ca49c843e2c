<template>
  <div>
    <AlertSign :position="'top'" v-if="isBTClicked" />
    <fixed-header :threshold="50">
      <div :class="isIos ? 'nav-space' : 'nav-space-android'">
        <CymLogo class="mylogo" />
      </div>
    </fixed-header>

    <div :class="isIos ? 'cym-header' : 'cym-header-android'">
      <div>
        <CymLogo @click="goToHomePage" class="mylogo" />
        <!-- 개발용 -->
        <strong v-if="isDevelopment">dev</strong>
      </div>
      <div class="float-right">
        <div>
          <img
            class="ic mr-3"
            src="@/assets/images/calendar-ic.png"
            alt="calendar"
            @click="handleClickCalendar"
            loading="lazy"
          />
          <img
            class="ic"
            @click="clickBluetooth"
            src="@/assets/images/toilet_icon.png"
            alt="toilet"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CymLogo from "@/assets/svg/cym702_logo.svg";
import FixedHeader from "@/components/Common/FixedHeader.vue";
import AlertSign from "@/components/Common/AlertSign.vue";

import { getSubjectId } from "../Common/getSubjectId.js";

export default {
  components: {
    FixedHeader,
    CymLogo,
    AlertSign,
  },

  data() {
    return {
      isBTClicked: false,
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
      isDevelopment: process.env.NODE_ENV === "development",
    };
  },

  updated() {
    setTimeout(() => {
      this.isBTClicked = false;
    }, 2000);
  },

  methods: {
    goToHomePage() {
      // 회사 소개 페이지로 이동.
      /*global Webview*/
      /*eslint no-undef: "error"*/
      const bridge_msg = {
        action: "goCym702",
        url: "https://yellosis.com/company.html",
      };
      Webview.goCym702(bridge_msg);
      const message = "https://smartstore.naver.com/cym702";
      Webview.openUrl(message);
    },
    async clickBluetooth() {
      const NODE_ENV = process.env.NODE_ENV; // production || development || localAdd commentMore actions

      // seat 연결 function
      function connectBluetooth() {
        const subjectId = getSubjectId();
        const message = {
          action: "connectBluetooth",
          accessToken: localStorage.auth,
          subjectId: subjectId,
        };

        /*eslint no-undef: "error"*/
        Webview.blueConnect(message);
      }

      try {
        switch (NODE_ENV) {
          // 운영 (seat off)
          case "production": {
            this.isBTClicked = true;
            break;
          }
          // 개발 (seat on)
          case "development": {
            connectBluetooth();
            break;
          }
          // 로컬 (seat on)
          case "local": {
            this.isBTClicked = true;
            // connectBluetooth();
            break;
          }
          // else
          default: {
            this.isBTClicked = true;
            throw new Error("NODE_ENV is not defined");
          }
        }
      } catch (_error) {
        console.error(_error);
      }
    },
    handleClickCalendar() {
      this.$router.push("/mypage/cym-report");
    },
  },
};
</script>

<style lang="scss" scoped>
.nav-space {
  width: 100%;
  padding-top: 55px;
  padding-bottom: 20px;
}
.nav-space-android {
  width: 100%;
  padding-top: 20px;
  padding-bottom: 20px;
}
.cym-header {
  padding: 100px 30px 25px;
  display: flex;
  width: 100%;
  justify-content: space-between;
}
.cym-header-android {
  padding: 70px 30px 25px;
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.ic {
  width: 16.5px;
}

.mylogo {
  margin-top: 5px;
}
</style>
