<template>
  <div>
    <div class="chart__wrapper">
      <!-- width="410" -->
      <apexchart
        height="250"
        type="line"
        :options="chartOptions"
        :series="series"
        ref="historyChart"
        @clickEventHandler="clickEventHandler"
      ></apexchart>
      <!-- @change="indexHandler" -->
      <div class="tooltip">
        <div class="tooltip-title" :class="`${tooltipTitleColor}-txt`">
          {{ tooltipTitle }}
          <!-- <span v-if="type === 'ph' && chartData.length !== 0" class="ph-sub-title">pH{{ level + 4 }}</span> -->
        </div>
        <div class="tooltip-time">{{ tooltipDate }}</div>
      </div>
    </div>
    <!-- <div class="chart-btn__wrapper">
      <ChartBtn :count="count" @changeCount="changeCount" />
    </div> -->
  </div>
</template>

<script>
import { computeKorScore, computeEnScore, computeColor } from "./utils/computedChartOptions.js";
// import ChartBtn from "./ChartBtn.vue";
import chartOptions from "../../utils/ChartOptions.js";
import { mapGetters } from "vuex";

export default {
  props: {
    type: String,
    historyData: Array,
    // count: String,
  },
  components: {
    // ChartBtn,
  },
  data() {
    return {
      tooltipTitle: "",
      tooltipDate: "",
      tooltipTitleColor: "",
      count: "recent",
      series: [
        {
          name: "Series 1",
          data: [],
        },
      ],
      chartData: [],
      chartOptions: {},
      chartLabels: [],
      historyItemDates: [],
      mouseMovingNow: false,
      level: 2,
      isKo: true,
    };
  },

  computed: {
    historyTabChanged() {
      return this.$store.state.historyTabChanged;
    },
    ...mapGetters(["tooltipIdx"]),
    // tooltipIndex() {
    //   return this.$store.state.tooltipIndex;
    // },
  },

  watch: {
    historyData(newVal) {
      // console.log(newVal);
      this.updateSeriesLine(newVal);
      this.getUrineTestItemsHistory(newVal);
      this.loaded = true;
    },
    historyTabChanged(newVal) {
      // console.log(newVal);
      this.updateSeriesLine(this.historyData);
      this.getUrineTestItemsHistory(this.historyData);
      this.loaded = true;
    },
    tooltipIndex(newVal) {
      // console.log("tooltipIndex: ", newVal);
    },
  },

  methods: {
    // indexHandler(idx) {
    //   console.log(idx)
    //   this.$store.commit("SET_TOOLTIP_INDEX", idx);
    // },
    clickEventHandler() {},
    getChartOptions(type) {
      const tooltipIndex = this.tooltipIdx;
      return {
        xcrossHairsXposition: (getLastPointXPosition) => {
          const xcrosshairs = document.querySelectorAll(".apexcharts-xcrosshairs");

          xcrosshairs.forEach((line) => {
            line.setAttribute("x1", `${getLastPointXPosition}`);
            line.setAttribute("x2", `${getLastPointXPosition}`);
            line.setAttribute("y1", `-10`);
          });
        },

        tooltipInitPosition: (getLastPointXPosition) => {
          const tooltip = document.querySelectorAll(".tooltip");

          tooltip.forEach((item) => {
            if (getLastPointXPosition > 240) {
              item.style.left = `${getLastPointXPosition - 30}px`;
            } else if (getLastPointXPosition < 5) {
              item.style.left = `${getLastPointXPosition + 30}px`;
            } else {
              item.style.left = `${getLastPointXPosition + 15}px`;
            }
          });
        },

        setTooltipPosition: (seriesXvalue, dataPointIndex) => {
          const tooltip = document.querySelectorAll(".tooltip");

          tooltip.forEach((item) => {
            if (seriesXvalue[dataPointIndex] > 240) {
              item.style.left = `${seriesXvalue[dataPointIndex] - 30}px`;
            } else if (seriesXvalue[dataPointIndex] < 5) {
              item.style.left = `${seriesXvalue[dataPointIndex] + 30}px`;
            } else {
              item.style.left = `${seriesXvalue[dataPointIndex] + 15}px`;
            }
          });
        },

        getChartPointColor: (dataPointIndex) => {
          const urineTestItemLevel = Number(this.chartData[dataPointIndex]);
          const koreanScoring = computeKorScore(type, urineTestItemLevel);
          const points = document.querySelectorAll(".apexcharts-marker");
          points.forEach((point, idx) => {
            if (idx === dataPointIndex) {
              point.style.stroke = computeColor(koreanScoring, this.type);
            } else {
              point.style.stroke = "#a7a7a7";
            }
          });
          // console.log(urineTestItemLevel);
          this.resetCymScore(urineTestItemLevel);
        },
      };
    },

    resetCymScore(level) {
      const koreanScoring = computeKorScore(this.type, level);
      const englishScoring = computeEnScore(koreanScoring, this.type);

      this.$store.commit("GET_CYM_SCORE", englishScoring);
    },

    getTooltipContents(type) {
      const tooltipIndex = this.tooltipIdx;
      return {
        tooltipTitle: (level) => computeKorScore(type, level),
        tooltipTitleColor: (level) => {
          this.level = level;
          const korScore = computeKorScore(type, level);
          return computeEnScore(korScore, this.type);
        },
        tooltipDate: (datePointIndex) => {
          const idx = tooltipIndex !== null ? tooltipIndex : datePointIndex;
          const fullDate = this.historyItemDates[datePointIndex];
          return fullDate;
        },
        setTooltipTitle: (title) => (this.tooltipTitle = title),
        setTooltipDate: (date) => (this.tooltipDate = date),
        setTooltipTitleColor: (color) => (this.tooltipTitleColor = color),
        getScore: (level) => {
          // const korScore = computeKorScore(type, level);
          // if (!this.mouseMovingNow) {
          //   // this.$store.commit("GET_CYM_SCORE", computeEnScore(korScore));
          // }
          // document.addEventListener("touchend", (e) => {
          //   // console.log(e);
          //   this.mouseMovingNow = false;
          //   if (!this.mouseMovingNow) {
          //     // this.$store.commit("GET_CYM_SCORE", computeEnScore(korScore));
          //   }
          // });
          // document.addEventListener("touchmove", (e) => {
          //   this.mouseMovingNow = true;
          // });
        },
      };
    },

    setChartOptions(type, chartData) {
      const getChartOptions = this.getChartOptions(type);
      const getTooltipContents = this.getTooltipContents(type);
      const store = this.$store;
      const tooltipIndex = this.tooltipIdx;
      const resetScore = (score) => this.$store.commit("GET_CYM_SCORE", score);
      const yaxis = chartOptions.yaxisOptions(this.type);

      return {
        chart: {
          ...chartOptions.apexchartOptions(),
          events: {
            mounted: function (chartContext, config) {
              // console.log("chart mouted");
              // console.log("here-------", tooltipIndex);
              // console.log(config.globals.seriesXvalues[0][chartData.length - 1]);
              const chartDataLen = chartData.length - 1;
              const getLastPointXPosition =
                tooltipIndex !== null
                  ? config.globals.seriesXvalues[0][tooltipIndex]
                  : config.globals.seriesXvalues[0][chartDataLen];
              // console.log("ooooooooooooo", config.globals.seriesXvalues[0][chartDataLen]);
              // const points = document.querySelectorAll(".apexcharts-marker");
              // points[points.length - 1].style.stroke = "#000";
              getChartOptions.xcrossHairsXposition(2);
              getChartOptions.tooltipInitPosition(2);
              if (getLastPointXPosition) {
                getChartOptions.xcrossHairsXposition(getLastPointXPosition);
                getChartOptions.tooltipInitPosition(getLastPointXPosition);
              }
            },
            updated: function (chartContext, config) {
              // console.log("chart updated");
              const getLastPointXPosition =
                tooltipIndex !== null
                  ? config.globals.seriesXvalues[0][tooltipIndex]
                  : config.globals.seriesXvalues[0][chartData.length - 1];
              // console.log(getLastPointXPosition);
              if (getLastPointXPosition) {
                getChartOptions.xcrossHairsXposition(getLastPointXPosition);
                getChartOptions.tooltipInitPosition(getLastPointXPosition);
              }
              // const points = document.querySelectorAll(".apexcharts-marker");
              // console.log(points);
              // points[points.length - 1].style.stroke = "#000";
              const index = tooltipIndex !== null ? tooltipIndex : chartData.length - 1;
              const level = tooltipIndex !== null ? Number(chartData[tooltipIndex]) : Number(chartData[index]);
              // console.log("###", tooltipIndex, level);
              resetScore(getTooltipContents.tooltipTitleColor(level));
              const tooltipTitle = getTooltipContents.tooltipTitle(level);
              const tooltipDate = getTooltipContents.tooltipDate(index);
              const tooltipTitleColor = getTooltipContents.tooltipTitleColor(level);
              getTooltipContents.setTooltipTitle(tooltipTitle);
              getTooltipContents.setTooltipDate(tooltipDate);
              getTooltipContents.setTooltipTitleColor(tooltipTitleColor);
            },
            mouseMove: function (event, chartContext, { seriesIndex, dataPointIndex, config }) {
              // store.commit("SET_TOOLTIP_INDEX", dataPointIndex);
            },
          },
        },
        dataLabels: {
          enabled: false,
        },
        colors: this.type === "cym702" ? ["#41D8E6"] : ["#A7A7A7"],
        markers: {
          strokeColor: this.type === "cym702" ? ["#41D8E6"] : ["#A7A7A7"],
          colors: ["#fff"],
          size: 5,
          hover: {
            size: 5.5,
          },
        },
        stroke: {
          width: 2.5,
          lineCap: "butt",
          curve: "straight",
        },
        xaxis: { ...chartOptions.xaxisOptions([...this.chartLabels]) },
        yaxis: { ...yaxis },
        grid: {
          yaxis: {
            lines: {
              show: true,
            },
          },
          strokeDashArray: 2,
        },
        tooltip: {
          enabled: true,
          custom: function ({ series, seriesIndex, dataPointIndex, w }) {
            // when tooltip changed
            store.commit("SET_TOOLTIP_INDEX", dataPointIndex);
            const seriesXvalue = w.globals.seriesXvalues[0];
            getChartOptions.setTooltipPosition(seriesXvalue, dataPointIndex);
            getChartOptions.getChartPointColor(dataPointIndex);

            const level = Number(chartData[dataPointIndex]);

            const tooltipTitle = getTooltipContents.tooltipTitle(level);
            const tooltipDate = getTooltipContents.tooltipDate(dataPointIndex);
            const tooltipTitleColor = getTooltipContents.tooltipTitleColor(level);

            getTooltipContents.setTooltipTitle(tooltipTitle);
            getTooltipContents.setTooltipDate(tooltipDate);
            getTooltipContents.setTooltipTitleColor(tooltipTitleColor);
            getTooltipContents.getScore(level);

            return "";
          },
        },
      };
    },
    changeCount(count) {
      // console.log(count);
      this.loaded = false;
      this.count = count;
      this.$emit("changeCount", count);
      // console.log(this.historyData);
      // this.getUrineTestItemsHistory();
    },
    updateSeriesLine(chartData) {
      this.$refs.historyChart.updateSeries(
        [
          {
            name: "History",
            data: chartData,
          },
        ],
        false,
        true
      );
    },

    getUrineTestItemsHistory(data) {
      try {
        // console.log(this.historyData, data);
        let response = data;

        if (response.length === 0) {
          this.chartData = [];
          this.chartLabels = [];
          this.historyItemDates = [];
          // console.log("length 0");
        } else {
          response = data;
          // console.log("this is response", response);
          const urineTestItemHistory = response;
          let chartData = response.map((i) => i.level);

          const chartLabels = response.map((item) => {
            // console.log(item);
            if (item.createdAt.length === 5) {
              const [Y, M] = item.createdAt.split(".");
              // console.log(M);
              return `${M}`;
            } else {
              const date = item.createdAt.split("T")[0];
              const [Year, Month, Date] = date.split("-");
              // console.log(date);
              return `${Month}.${Date}`;
            }
          });
          const historyItemDates = response.map((item) => {
            if (item.createdAt.length === 5) {
              const [year, month] = item.createdAt.split(".");
              const yearUnit = this.$i18n.t("avg_year");
              const monthUnit = this.$i18n.t("avg_month");
              const average = this.$i18n.t("average");
              const date = this.isKo
                ? `20${year}${yearUnit}${month}${monthUnit} ${average}`
                : `${average} 20${year}${yearUnit}${month}${monthUnit}`;
              return item.createdAt.length === 8 || item.createdAt.length === 5 ? date : item.createdAt.slice(2);
            } else {
              const [date, time] = item.createdAt.split("T");
              const formattedDate = date.split("-").join(".").slice(2);
              const formattedTime = time.slice(0, 5);
              let [hour, min] = formattedTime.split(":");

              let isAm = "AM";
              if (Number(hour) > 12) {
                isAm = "PM";
                hour = String(Number(hour) - 12);
              }

              hour = hour.padStart(2, "0");

              return `${formattedDate} ${hour}:${min} ${isAm}`;
            }
            // if (item.createdAt.length === 10) {
            //   const [Y, M, D] = item.createdAt.split(".");
            //   return `${M}.${D}`;
            // } else if (item.createdAt.length === 7) {
            //   const [Y, M] = item.createdAt.split(".");
            //   return `${M}`;
            // } else if (item.createdAt.length === 5) {
            //   const [Y, M] = item.createdAt.split(".");
            //   return `${M}`;
            //   // return item.createdAt;
            // }
            // else {
            //   const splitStr = item.createdAt.split("T");
            //   const [Y, M, D] = splitStr[0].split("-");
            //   return `${M}.${D}`;
            // }
          });

          const lastIndexChartData = Number(urineTestItemHistory[urineTestItemHistory.length - 1].level);
          const getTooltipContents = this.getTooltipContents(this.type);

          this.chartData = chartData;
          this.chartLabels = chartLabels;
          // console.log(this.chartData);
          this.historyItemDates = historyItemDates;
          // this.chartOptions = this.setChartOptions(this.type, chartData);

          this.tooltipTitle = getTooltipContents.tooltipTitle(lastIndexChartData);
          this.tooltipDate = getTooltipContents.tooltipDate(chartData.length - 1);
          this.tooltipTitleColor = getTooltipContents.tooltipTitleColor(lastIndexChartData);
          // this.$store.commit("GET_CYM_SCORE", getTooltipContents.tooltipTitleColor(lastIndexChartData));
        }

        this.chartOptions = this.setChartOptions(this.type, this.chartData);
        this.updateSeriesLine(this.chartData);
      } catch (error) {
        new Error(error.message);
      }
    },
  },

  mounted() {
    this.getUrineTestItemsHistory(this.historyData);
    // console.log(this.historyData);
    this.isKo = this.$i18n.locale === "ko";
  },
};
</script>

<style scoped>
.chart__wrapper {
  padding-left: 1px;
  position: relative;
  height: 255px;
}
::v-deep .chart__tooltip {
  background-color: #ededed !important;
  text-align: center;
  font-size: 1.25rem;
  font-weight: bold;
  border-radius: 0px !important;
}

::v-deep .chart_tooltip_date {
  font-size: 10px;
  font-weight: 500;
  color: #646464;
}

::v-deep .negative-txt {
  color: #00bb00;
}

::v-deep .exertion-txt {
  color: #646464;
}
::v-deep .enter-txt {
  color: #fac9bf;
}

::v-deep .ketone_normal-txt {
  color: #f3b6b6;
}
::v-deep .ketone_good-txt {
  color: #b83a71;
}
::v-deep .ketone_warning-txt {
  color: #8f3f6e;
}

::v-deep .positive-txt {
  color: #ffcc00 !important;
}

::v-deep .positive-plus-minus-txt {
  color: #ffcc00 !important;
}

::v-deep .positive-plus-txt {
  color: #ffcc00 !important;
}

::v-deep .positive-double-txt {
  color: #ff6600;
}

::v-deep .positive-triple-txt {
  color: #646464;
}

::v-deep .positive-quadruple-txt {
  color: #646464;
}

::v-deep .apexcharts-tooltip {
  top: -25px !important;
  box-shadow: none !important;
  border: none !important;
}

::v-deep text {
  /* font-size: 14px; */
  font-family: GilroyMedium !important;
  font-weight: unset !important;
  color: #a7a7a7;
}

::v-deep .apexcharts-yaxis-label {
  letter-spacing: -0.08em;
  font-size: 14px;
  font-family: GilroyMedium !important;
}

::v-deep .apexcharts-xcrosshairs {
  stroke-dasharray: 0 !important;
  opacity: 1 !important;
  stroke: #ededed !important;
}

::v-deep .apexcharts-canvas {
  position: relative !important;
  left: -3px;
}

.point {
  width: 12px;
  height: 12px;
  border: 2px solid black;
  border-radius: 50%;
  position: absolute;
}

.tooltip {
  position: absolute;
  top: -30px;
  background: #ededed;
  border-radius: 5px;
  padding: 5px 5px 0 5px;
}

.tooltip-title {
  font-size: 22px;
  font-weight: bold;
  letter-spacing: -0.03em;
  line-height: 22px;
}

.tooltip-time {
  font-family: GilroyMedium;
  font-size: 13px;
  color: #646464;
}

.ph-sub-title {
  font-family: Noto Sans KR;
  font-size: 14px;
  font-weight: 800;
  line-height: 17px;
  letter-spacing: -0.03em;
  text-align: center;
}

::v-deep path {
  filter: none !important;
}

.chart-btn__wrapper {
  width: 100%;
  padding: 0 30px;
}
</style>
