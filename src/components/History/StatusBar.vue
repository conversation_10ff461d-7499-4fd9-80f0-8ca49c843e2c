<template>
  <div>
    <div v-if="type === 'ketone'" class="mystate-indicator-title">
      <div v-if="!ketoneMode">
        <div>
          {{ username.length > 8 ? username.slice(0, 8) + "..." : username }} {{ $t("users") }} {{ computedKorNam }}
          {{ $t("status") }}
        </div>
        <div>({{ $t("abb_diabetes") }})</div>
      </div>
      <div v-else>
        <div>
          {{ username.length > 8 ? username.slice(0, 8) + "..." : username }} {{ $t("users") }} {{ computedKorNam }}
          {{ $t("status") }}
        </div>
        <div>({{ $t("is_ketosis") }})</div>
      </div>
      <div class="setting-icon" v-if="type === 'ketone'" @click="openEditModalWindow">
        <v-icon>$setting_btn </v-icon>
      </div>
    </div>
    <div v-else class="mystate-indicator-title">
      <div>
        {{ username.length > 8 ? username.slice(0, 8) + "..." : username }}{{ $t("users") }} {{ computedKorNam }}
        {{ $t("status") }}
      </div>
    </div>
    <div class="p-30">
      <div class="mystste-indicator-wrapper">
        <div class="mystate-indicator">
          <div
            class="indicator__item"
            v-for="(item, index) in items"
            :key="index"
            :class="index === curInfo ? itemInfo : ''"
          >
            {{ item }}
            <span class="ph-subtitle" v-if="type === 'ph' && index === 0">&nbsp;(pH5~pH8)</span>
            <span class="ph-subtitle" v-if="type === 'ph' && index === 1">&nbsp;(pH9)</span>
          </div>
        </div>
      </div>
    </div>
    <div class="no-result" v-if="status">{{ $t("cym_no_data") }}</div>
    <div class="mystatusTxt-explain__wrapper" v-else>
      <p class="mystatusTxt-explain" v-html="statusTxt"></p>
    </div>
  </div>
</template>
<script>
import { historyTypes } from "@/assets/data/data.js";
import { mapGetters } from "vuex";

export default {
  props: {
    // username: String,
    // historyData: Array,
    type: String,
    status: Boolean,
  },

  // TODO: 항목별 상태 설명과 상태알림기능
  data() {
    return {
      currentPosition: 0,
      items: [],
      statusTxt: "",
      username: "",
      ketoneMode: JSON.parse(localStorage.getItem("ketoneMode")),
    };
  },
  methods: {
    openEditModalWindow() {
      this.$store.commit("openKetoneEditModal", true);
    },
    statusbarColorHandler() {
      // console.log("cymScore:", this.cymScore, this.type, this.status);
      const ketoneMode = JSON.parse(localStorage.getItem("ketoneMode"));
      // console.log("ketoneMode:", ketoneMode);
      if (
        this.type === "blood" ||
        this.type === "glucose" ||
        this.type === "protein" ||
        (this.type === "ketone" && !ketoneMode)
      ) {
        this.items = [
          this.$i18n.t("negative"),
          this.$i18n.t("positive"),
          this.$i18n.t("positive"),
          this.$i18n.t("positive"),
        ];
      } else if (this.type === "ph") {
        this.items = [this.$i18n.t("negative"), this.$i18n.t("positive")];
      } else if (this.type === "ketone" && ketoneMode) {
        this.items = [
          this.$i18n.t("exertion"),
          this.$i18n.t("enter"),
          this.$i18n.t("ketone_normal"),
          this.$i18n.t("ketone_great_level"),
          this.$i18n.t("warning"),
        ];
      } else {
        this.items = [this.$i18n.t("good"), this.$i18n.t("normal"), this.$i18n.t("warning"), this.$i18n.t("caution")];
      }
      if (this.type === "ketone") this.statusTxt = historyTypes(this.type, this.cymScore);

      this.username = localStorage.getItem("username");
      // console.log(this.username);
      // this.$store.state.username;
    },
  },
  mounted() {
    // console.log(this.type);
    this.statusbarColorHandler();
  },
  updated() {
    // this.ketoneMode ? this.statusbarColorHandler() : null;
    // console.log("updated");
    this.statusTxt = historyTypes(this.type, this.cymScore);
  },
  computed: {
    ...mapGetters(["cymScore"]),
    computedKorNam() {
      switch (this.type) {
        case "blood":
          return this.$i18n.t("hematuria");
        case "glucose":
          return this.$i18n.t("glycosuria");
        case "protein":
          return this.$i18n.t("proteinuria");
        case "ph":
          return this.$i18n.t("tab_ph");
        case "ketone":
          return this.$i18n.t("ketone_status");
        default:
          return "";
      }
    },

    itemInfo() {
      if (this.type === "ketone" && this.ketoneMode) {
        return "ketone-" + this.cymScore;
      } else if (this.type === "ph") return "cym-" + this.cymScore + "-ph";
      return "cym-" + this.cymScore;
    },

    curInfo() {
      // console.log(this.cymScore);
      if (this.type === "ketone" && this.ketoneMode) {
        if (this.cymScore.length) {
          switch (this.cymScore) {
            case "exertion":
              return 0;
            case "enter":
              return 1;
            case "ketone_normal":
              return 2;
            case "ketone_good":
              return 3;
            case "ketone_warning":
              return 4;
            default:
              return;
          }
        } else {
          return 4;
        }
      } else {
        // console.log("ketone", this.cymScore);
        if (this.cymScore === "negative") {
          return 0;
        } else if (
          this.cymScore === "positive" ||
          this.cymScore === "positive-plus-minus" ||
          this.cymScore === "positive-plus"
        ) {
          return 1;
        } else if (this.cymScore === "positive-double") {
          return 2;
        } else {
          return 3;
        }
      }
    },
  },
  watch: {
    curInfo(newVal) {
      // console.log(newVal);
    },
    type(newVal) {
      // console.log("type", newVal);
    },
    cymScore(newVal) {
      // console.log("cymScore", newVal);
    },
    // historyData(newVal) {
    //   // console.log("history data:", newVal);
    //   this.statusbarColorHandler()
    // },
  },
};
</script>

<style lang="scss" scoped>
@mixin cymStateBar($bg-color) {
  background-color: $bg-color;
  color: #fff;
  font-weight: bold;
}

@mixin ketoneStateBar($bg-color, $color: white) {
  background-color: $bg-color;
  color: $color;
  font-weight: bold;
}

$black: #000000;
$danger: #646464;
$green: #00bb00;
$yellow: #ffcc00;
$red: #ff6600;

$ketone-0: #00bb00;
$ketone-1: #ffcc00;
$ketone-2: #ffcc00;
$ketone-3: #ff6600;
$ketone-4: #646464;

// ketoneMode
$ketone-exertion: #fff8f4;
$ketone-enter: #fac9bf;
$ketone-ketone_normal: #f3b6b6;
$ketone-ketone_good: #b83a71;
$ketone-ketone_warning: #8f3f6e;

$primary: #41d8e6;
$lightblue: #c9f4f8;

.mystate-indicator-title {
  display: flex;
  justify-content: center;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 26px;
  text-align: center;
  letter-spacing: -0.03em;
}

.setting-icon {
  position: absolute;
  right: 30px;
}

.mystate-indicator {
  display: flex;
  border-radius: 5px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ededed;
  margin: 15px 0px;
}

.indicator__item {
  flex: 1;
  text-align: center;
  line-height: 32px;
  font-size: 16px;
  color: #a7a7a7;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ph-subtitle {
  font-size: 12px;
  line-height: 32px;
  font-family: Noto Sans KR;
}

.mystatusTxt-explain {
  text-align: left;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 23px;
  letter-spacing: -0.02em;
}

/* cym 702 state bar */
.cym702-good {
  @include cymStateBar($primary);
  border-radius: 5px 0px 0px 5px;
}

.cym702-normal {
  @include cymStateBar($green);
  border-radius: 0px;
}

.cym702-caution {
  @include cymStateBar($yellow);
}

.cym702-warning {
  @include cymStateBar($red);
  border-radius: 0px 5px 5px 0px;
}

/* blood, protein, glucose state bar */
.cym-negative {
  @include cymStateBar($green);
  border-radius: 5px 0px 0px 5px;
}
.cym-negative-ph {
  @include cymStateBar($green);
  border-radius: 5px 0px 0px 5px;
}

.cym-positive-double {
  @include cymStateBar($red);
}

.cym-positive {
  @include cymStateBar($yellow);
}
.cym-positive-plus {
  @include cymStateBar($yellow);
}
.cym-positive-plus-minus {
  @include cymStateBar($yellow);
}
.cym-positive-ph {
  @include cymStateBar($yellow);
  border-radius: 0px 5px 5px 0px;
}

.cym-positive-triple {
  @include cymStateBar($danger);
  border-radius: 0px 5px 5px 0px;
}
.cym-positive-quadruple {
  @include cymStateBar($danger);
  border-radius: 0px 5px 5px 0px;
}

/* ph state bar */
.ph-warning {
  @include cymStateBar($yellow);
  border-radius: 0px 5px 5px 0px;
}

.ph-good {
  @include cymStateBar($green);
  border-radius: 0px 5px 5px 0px;
}

/* ketone state bar */
.ketone-normal {
  @include ketoneStateBar($ketone-0);
  border-radius: 5px 0px 0px 5px;
}
.ketone-exertion {
  @include ketoneStateBar($ketone-exertion);
  color: #646464;
  border-radius: 5px 0px 0px 5px;
}

.ketone-caution_plus_minus {
  @include ketoneStateBar($ketone-1);
}
.ketone-enter {
  @include ketoneStateBar($ketone-enter);
}

.ketone-caution_plus {
  @include ketoneStateBar($ketone-2);
}
.ketone-ketone_normal {
  @include ketoneStateBar($ketone-ketone_normal);
}

.ketone-caution {
  @include ketoneStateBar($ketone-3);
}
.ketone-ketone_good {
  @include ketoneStateBar($ketone-ketone_good);
}
.ketone-ketone_warning {
  border-radius: 0px 5px 5px 0px;
  @include ketoneStateBar($ketone-ketone_warning);
}

.ketone-danger {
  border-radius: 0px 5px 5px 0px;
  @include ketoneStateBar($ketone-4);
}
.no-result {
  font-size: 14px;
  padding: 5px 0px;
  color: #646464;
  font-weight: 500;
  text-indent: 5px;
  text-align: left;
}

p {
  margin: 0;
}
</style>
