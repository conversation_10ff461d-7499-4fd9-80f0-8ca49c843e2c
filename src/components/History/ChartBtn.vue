<template>
  <div class="history-chartBtn__wrapper">
    <div class="skip-btn__wrapper">
      <!-- <v-icon :size="25" @click="prevHandler">$prev_icon</v-icon>
      <v-icon :size="25" @click="nextHandler">$next_icon</v-icon> -->
    </div>
    <div class="history-chart-btn">
      <div class="l--flex-center">
        <div
          :class="{
            'chart-btn__el': count !== 'recent',
            'chart-btn__el chart-btn__el--isActive': count === 'recent',
          }"
          @click="changeCount('recent')"
        >
          {{ $t("recent_login") }}
        </div>
        <!-- <div
          :class="{
            'chart-btn__el': count !== 'month',
            'chart-btn__el chart-btn__el--isActive': count === 'month',
          }"
          @click="changeCount('month')"
        >
          1{{ $t("chart_btn_m") }}
        </div> -->
        <div
          :class="{
            'chart-btn__el': count !== 'year',
            'chart-btn__el chart-btn__el--isActive': count === 'year',
          }"
          @click="changeCount('year')"
        >
          {{ $t("chart_btn_y") }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    count: String,
  },
  methods: {
    changeCount(count) {
      // console.log(count);
      this.$emit("changeCount", count);
    },
    prevHandler() {
      // console.log("prev");
    },
    nextHandler() {
      // console.log("next");
    },
  },
  mounted() {
    // console.log(this.count);
  },
};
</script>

<style lang="scss" scoped>
.history-chartBtn__wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.skip-btn__wrapper {
  display: flex;
  margin-left: 20px;
  height: 35px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.history-chart-btn {
  // width: 190px;
  padding: 4px;
  background-color: #ededed;
  border-radius: 5px;
  color: #a7a7a7;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.l--flex-center {
  width: 100%;
  padding: 0 3px;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
}

.chart-btn__el {
  border-radius: 4px;
  width: 55px;
  font-size: 14px;
  letter-spacing: -0.03em;
  line-height: 20px;
}

.chart-btn__el--isActive {
  border-radius: 5px;
  width: 55px;
  padding: 3px;
  background-color: #fff;
  color: #000;
  line-height: 20px;
}
</style>
