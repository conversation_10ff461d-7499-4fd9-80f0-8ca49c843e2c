<template>
  <div>
    <div class="stage-guide__bg-modal">
      <div class="modal-window">
        <div class="guide-content__container">
          <div class="content-title__wrapper">
            <div class="content-header__closeBtn">
              <div @click="closeGuideModal">
                <v-icon>$x_circle_solid</v-icon>
              </div>
            </div>

            <div class="content-header__title">
              <div class="content-title">
                {{ title }}
                <span class="ph-level" v-if="type === 'ph' && title === this.$i18n.t('negative')"
                  >&nbsp;(pH5~8)</span
                >
                <span class="ph-level" v-if="type === 'ph' && title === this.$i18n.t('positive')"
                  >&nbsp;(pH9)</span
                >
                <span
                  class="ph-level"
                  v-if="
                    (type === 'glucose' || type === 'protein') && title === this.$i18n.t('negative')
                  "
                  >&nbsp;(-),(+/-)</span
                >
                <span
                  class="ph-level"
                  v-if="
                    (type === 'glucose' || type === 'protein') && title === this.$i18n.t('positive')
                  "
                  >&nbsp;(-),(+/-)</span
                >
              </div>
              <div
                class="content-subimg-items"
                v-if="
                  this.title.length < 9 &&
                  this.title !== this.$i18n.t('ketone_precautions') &&
                  this.title !== this.$i18n.t('protein_precautions')
                "
              >
                <div class="subimg-item">
                  <img :src="setImage.color1" alt="" />
                </div>
                <div class="subimg-item">
                  <img :src="setImage.color2" alt="" />
                </div>
                <div class="subimg-item" v-if="type === 'ph' && title === this.$i18n.t('negative')">
                  <img :src="setImage.color3" alt="" />
                </div>
                <div class="subimg-item" v-if="type === 'ph' && title === this.$i18n.t('negative')">
                  <img :src="setImage.color4" alt="" />
                </div>
              </div>
            </div>
            <div class="zoom-btn" @click="textZomeControlView">
              <span class="small">{{ $t("font_size") }}</span>
              <span class="large">{{ $t("font_size") }}</span>
            </div>
          </div>
          <template v-if="showZoomControlView">
            <div class="slider__wrapper">
              <div class="smallText">{{ $t("font_size") }}</div>
              <v-slider
                color="#C9F4F8"
                track-color="#fff"
                track-fill-color="#fff"
                v-model="slider"
                step="1"
                :max="4"
                ticks="always"
              ></v-slider>
              <div class="largeText">{{ $t("font_size") }}</div>
            </div>
          </template>
          <div class="guide-txt__wrapper">
            <div class="guide-txt" v-html="convertInfoTxt" ref="guideTxt"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  normalStripImg,
  warningStripImg,
  cautionStripImg,
  glucoseTriplePositiveImg,
  dangerStripImg,
  enterStripImg,
  exertionStripImg,
} from "./utils/computeChart";
export default {
  props: {
    type: String,
    stage: String,
  },
  data() {
    return {
      setImage: {},
      title: "",
      levelInfoTxt: "",
      slider: 0,
      showZoomControlView: false,
    };
  },
  methods: {
    textZomeControlView() {
      if (this.showZoomControlView) {
        this.showZoomControlView = false;
      } else {
        this.showZoomControlView = true;
      }
    },
    closeGuideModal() {
      document.body.classList.remove("no-scroll");
      this.$store.commit("closeGuideModal");
    },
    getLevelValue(type, level) {
      // console.log(type, level);
      const ketoneMode = JSON.parse(localStorage.getItem("ketoneMode"));

      switch (level) {
        case this.$i18n.t("exertion"):
          return this.$i18n.t(`${type}_exertion_level_modal`);
        case this.$i18n.t("enter"):
          return this.$i18n.t(`${type}_enter_level_modal`);
        case this.$i18n.t("normal"):
          return this.$i18n.t(`${type}Mode_normal_level_modal`);
        case this.$i18n.t("good"):
          return this.$i18n.t(`${type}_good_level_modal`);
        case this.$i18n.t("warning"):
          return this.$i18n.t(`${type}_warning_level_modal`);
        case this.$i18n.t("negative"):
          return this.$i18n.t(`${type}_negative_level_modal`);
        case this.$i18n.t("positive"):
          return this.$i18n.t(`${type}_positive_level_modal`);
        case this.$i18n.t("positive_plus"):
          return this.$i18n.t(`${type}_positive_plus_level_modal`);
        case this.$i18n.t("positive_double_plus"):
          return this.$i18n.t(`${type}_positive_double_level_modal`);
        case this.$i18n.t("positive_triple_plus"):
          return this.$i18n.t(`${type}_positive_triple_level_modal`);
        case this.$i18n.t("positive_quadruple_plus"):
          return this.$i18n.t(`${type}_positive_quadruple_level_modal`);
        case this.$i18n.t("negative_minus"):
          return this.$i18n.t(`${type}_negative_level_modal`);
        case this.$i18n.t("positive_plus_minus"):
          return this.$i18n.t(`${type}_positive_plus_minus_level_modal`);
        case this.$i18n.t("protein_precautions"):
          return this.$i18n.t("protein_precautions_description");
        case this.$i18n.t("ketone_precautions"):
          return this.$i18n.t("ketone_precautions_description");
        case "healthInfo":
          return type === "ketone" && ketoneMode
            ? this.$i18n.t(`${type}_info_ketosis`)
            : this.$i18n.t(`${type}_info`);
      }
    },
    setTitleImg(type) {
      this.title = this.$store.state.cardTitle;
      if (this.type === "ketone") {
        if (
          // 양성(+), 적절
          this.$store.state.cardTitle === this.$i18n.t("positive_plus") ||
          this.$store.state.cardTitle === this.$i18n.t("ketone_good_level_minus")
        ) {
          this.setImage = normalStripImg(this.type);
        }
        if (
          // 음성(-), 분발
          this.$store.state.cardTitle === this.$i18n.t("negative_minus") ||
          this.$store.state.cardTitle === this.$i18n.t("exertion")
        ) {
          this.setImage = exertionStripImg(this.type);
        }
        if (
          // 양성(+/-), 진입
          this.$store.state.cardTitle === this.$i18n.t("positive_plus_minus") ||
          this.$store.state.cardTitle === this.$i18n.t("enter")
        ) {
          this.setImage = enterStripImg(this.type);
        }
        if (
          this.$store.state.cardTitle === this.$i18n.t("positive_double_plus") ||
          this.$store.state.cardTitle === this.$i18n.t("good")
        ) {
          this.setImage = cautionStripImg(this.type);
        }
        if (
          this.$store.state.cardTitle === this.$i18n.t("positive_triple_plus") ||
          this.$store.state.cardTitle === this.$i18n.t("warning")
        )
          this.setImage = dangerStripImg(this.type);
      } else {
        if (this.$store.state.cardTitle === this.$i18n.t("negative")) {
          this.setImage = normalStripImg(this.type);
        } else if (
          this.$store.state.cardTitle === this.$i18n.t("positive_plus") ||
          this.$store.state.cardTitle === this.$i18n.t("positive")
        ) {
          this.setImage = warningStripImg(this.type);
        } else if (this.$store.state.cardTitle === this.$i18n.t("positive_double_plus")) {
          this.setImage = cautionStripImg(this.type);
        } else if (
          this.$store.state.cardTitle === this.$i18n.t("positive_triple_plus") &&
          this.type === "glucose"
        ) {
          this.setImage = glucoseTriplePositiveImg(this.type);
        } else if (this.$store.state.cardTitle === this.$i18n.t("positive_triple_plus")) {
          this.setImage = dangerStripImg(this.type);
        } else if (this.$store.state.cardTitle === this.$i18n.t("positive_quadruple_plus")) {
          this.setImage = dangerStripImg(this.type);
        }
      }
    },
  },
  computed: {
    convertInfoTxt() {
      return this.levelInfoTxt;
    },

    returnSliderValue() {
      return this.slider;
    },
  },
  watch: {
    slider: function (newVal, oldVal) {
      const fontSize = 18 + newVal * 2;
      this.$refs.guideTxt.style.fontSize = `${fontSize}px`;
    },
  },
  mounted() {
    this.setTitleImg(this.type);
    // console.log(this.title);
    if (this.title.includes("?") || this.title.includes("？")) {
      this.levelInfoTxt = this.getLevelValue(this.type, "healthInfo");
    } else {
      this.levelInfoTxt = this.getLevelValue(this.type, this.$store.state.cardTitle);
    }
  },
};
</script>

<style lang="scss" scoped>
.stage-guide__bg-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 450px;
  height: 100%;
  z-index: 999999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 25px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-window {
  background-color: #fff;
  width: 100%;
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  padding: 15px 20px;
  min-height: 540px;
}
.guide-content__container {
  width: 100%;
}
.content-title__wrapper {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ededed;
  padding: 10px 0;
}
.content-title {
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 18px;
  /* identical to box height */
  display: flex;
  align-items: center;
  text-align: center;
  letter-spacing: -0.03em;
}

.ph-level {
  font-size: 16px;
  line-height: 18px;
}

.content-header__title {
  display: flex;
  padding-left: 10px;
}
.content-subimg-items {
  display: flex;
}
.subimg-item {
  padding: 0px 3px;
}
.subimg-item > img {
  width: 20px;
  margin-top: 7px;
}

.zoom-btn {
  letter-spacing: -0.15em;
  color: #41d8e6;
  font-weight: 900;
}

.small {
  font-size: 14px;
}
.large {
  font-size: 20px;
}
.guide-txt__wrapper {
  width: 100%;
  padding: 10px 0;
}
.guide-txt {
  width: 100%;
  height: 460px;
  text-align: left;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 25px;
  letter-spacing: -0.02em;
  overflow: scroll;
}
.slider__wrapper {
  z-index: 999999999;
  position: absolute;
  margin-top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  padding: 10px 15px 5px 15px;
  background-color: #41d8e6;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.slider__wrapper:after {
  border-top: 0px solid transparent;
  border-left: 8px solid transparent;
  border-right: 2px solid transparent;
  border-bottom: 8px solid #41d8e6;
  content: "";
  position: absolute;
  top: -8px;
  left: 89%;
}
.smallText {
  font-size: 16px;
  font-weight: 700;
  color: #fff;
  padding-bottom: 4px;
}
.largeText {
  font-size: 30px;
  font-weight: 700;
  color: #fff;
  padding-left: 7px;
  padding-bottom: 4px;
}
::v-deep .v-messages {
  display: none !important;
}
::v-deep .v-slider__tick {
  border-left: 0.5px solid #fff !important;
  height: 10px !important;
  position: absolute;
  left: 50%;
  margin-left: 0px;
  top: -4px !important;
  background-color: #fff !important;
}
::v-deep .v-slider__thumb {
  width: 25px !important;
  height: 25px !important;
  box-shadow: 2px 2px 5px rgb(0 0 0 / 20%) !important;
}
</style>
