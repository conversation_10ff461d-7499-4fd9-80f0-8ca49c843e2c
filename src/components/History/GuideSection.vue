<template>
  <div>
    <div class="guide-section__container">
      <!-- <div class="subtitle" v-if="!ketoneMode">{{ card2Title }}</div> -->
      <div class="container-items__wrapper">
        <div class="gid-container-items">
          <div class="gid-card__item">
            <GuideCard :type="type" :stripImage="stripNormal" :cardTitle="normal" cardBoxHeight="min-height-140" />
          </div>
          <div class="gid-card__item" v-if="type !== 'ph'">
            <GuideCard :type="type" :stripImage="stripWarning" :cardTitle="warning" cardBoxHeight="min-height-140" />
          </div>
        </div>
        <div class="gid-container-items">
          <div class="gid-card__item">
            <GuideCard :type="type" :stripImage="stripCaution" :cardTitle="caution" cardBoxHeight="min-height-140" />
          </div>
          <div class="gid-card__item" v-if="type !== 'ph'">
            <GuideCard :type="type" :stripImage="stripDanger" :cardTitle="danger" cardBoxHeight="min-height-140" />
          </div>
        </div>
        <div class="gid-container-items">
          <div class="gid-card__ketone" v-if="type === 'protein'">
            <GuideCard
              :type="type"
              :stripImage="proteinDangerImg"
              :cardTitle="proteinDanger"
              cardBoxHeight="min-height-140"
            />
          </div>
        </div>
        <div class="gid-container-items">
          <div class="gid-card__ketone" v-if="type === 'glucose'">
            <GuideCard
              :type="type"
              :stripImage="glucoseDangerImg"
              :cardTitle="glucoseDanger"
              cardBoxHeight="min-height-140"
            />
          </div>
        </div>
        <div class="gid-container-items">
          <div class="gid-card__ketone" v-if="type === 'ketone'">
            <GuideCard
              :type="type"
              :stripImage="ketoneDangerImg"
              :cardTitle="ketoneDanger"
              cardBoxHeight="min-height-140"
            />
          </div>
        </div>
      </div>
      <div class="urineTestItemsInfo-cards__wrapper">
        <div class="gid-card__item">
          <GuideCard :isFullSize="true" :type="type" :cardTitle="cardTitle" cardBoxHeight="min-height-80" />
        </div>
        <div v-if="type === 'protein' || type === 'ketone'" class="gid-card__item">
          <GuideCard :isFullSize="true" :cardTitle="card2Title" cardBoxHeight="min-height-80" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import GuideCard from "./GuideCard.vue";

export default {
  components: {
    GuideCard,
  },
  props: {
    type: String,
  },
  data() {
    return {
      normal: "",
      warning: "",
      caution: "",
      danger: "",
      stripNormal: "",
      stripWarning: "",
      stripCaution: "",
      stripDanger: "",
      proteinDangerImg: "",
      proteinDanger: "",
      glucoseDangerImg: "",
      glucoseDanger: "",
      ketoneDangerImg: "",
      ketoneDanger: "",
      cardTitle: "",
      card2Title: "",
      ketoneMode: JSON.parse(localStorage.getItem("ketoneMode")),
    };
  },
  mounted() {
    if (this.type === "blood") {
      this.normal = this.$i18n.t("negative");
      this.warning = this.$i18n.t("positive_plus");
      this.caution = this.$i18n.t("positive_double_plus");
      this.danger = this.$i18n.t("positive_triple_plus");
      this.cardTitle = this.$i18n.t("what_is_hematuria");
      this.card2Title = "잠혈 건강정보";

      this.stripNormal = require("@/assets/images/ui_strip_images/blood/normal.png");
      this.stripWarning = require("@/assets/images/ui_strip_images/blood/warning.png");
      this.stripCaution = require("@/assets/images/ui_strip_images/blood/caution.png");
      this.stripDanger = require("@/assets/images/ui_strip_images/blood/danger.png");
    } else if (this.type === "glucose") {
      this.normal = this.$i18n.t("negative");
      this.warning = this.$i18n.t("positive_plus");
      this.caution = this.$i18n.t("positive_double_plus");
      this.danger = this.$i18n.t("positive_triple_plus");
      this.glucoseDanger = this.$i18n.t("positive_quadruple_plus");
      this.cardTitle = this.$i18n.t("what_is_glycosuria");
      this.card2Title = "포도당 건강정보";
      this.stripNormal = require("@/assets/images/ui_strip_images/glucose/normal.png");
      this.stripWarning = require("@/assets/images/ui_strip_images/glucose/warning.png");
      this.stripCaution = require("@/assets/images/ui_strip_images/glucose/caution.png");
      this.stripDanger = require("@/assets/images/ui_strip_images/glucose/positive_triple.png");
      this.glucoseDangerImg = require("@/assets/images/ui_strip_images/glucose/positive_quadruple.png");
    } else if (this.type === "protein") {
      this.normal = this.$i18n.t("negative");
      this.warning = this.$i18n.t("positive_plus");
      this.caution = this.$i18n.t("positive_double_plus");
      this.danger = this.$i18n.t("positive_triple_plus");
      this.proteinDanger = this.$i18n.t("positive_quadruple_plus");
      this.cardTitle = this.$i18n.t("what_is_proteinuria");
      this.card2Title = this.$i18n.t("protein_precautions");
      this.stripNormal = require("@/assets/images/ui_strip_images/protein/normal.png");
      this.stripWarning = require("@/assets/images/ui_strip_images/protein/warning.png");
      this.stripCaution = require("@/assets/images/ui_strip_images/protein/caution.png");
      this.stripDanger = require("@/assets/images/ui_strip_images/protein/positive_triple.png");
      this.proteinDangerImg = require("@/assets/images/ui_strip_images/protein/positive_quadruple.png");
    } else if (this.type === "ph") {
      this.normal = `${this.$i18n.t("negative")}`;
      this.warning = "";
      this.caution = this.$i18n.t("positive");
      this.danger = "";
      this.cardTitle = this.$i18n.t("what_is_ph");
      this.card2Title = "pH 건강정보";
      this.stripNormal = require("@/assets/images/ui_strip_images/ph/normal.png");
      this.stripWarning = require("@/assets/images/ui_strip_images/ph/normal.png");
      this.stripCaution = require("@/assets/images/ui_strip_images/ph/warning.png");
      this.stripDanger = require("@/assets/images/ui_strip_images/ph/warning.png");
    } else if (this.type === "ketone" && !this.ketoneMode) {
      // 당뇨모드
      this.normal = this.$i18n.t("negative_minus");
      this.warning = this.$i18n.t("positive_plus_minus");
      this.caution = this.$i18n.t("positive_plus");
      this.danger = this.$i18n.t("positive_double_plus");
      this.ketoneDanger = this.$i18n.t("positive_triple_plus");
      this.cardTitle = this.$i18n.t("what_is_ketone");
      this.card2Title = this.$i18n.t("ketone_precautions");
      this.stripNormal = require("@/assets/images/ui_strip_images/ketone/normal_neg.png");
      this.stripWarning = require("@/assets/images/ui_strip_images/ketone/ketone_plus_minus.png");
      this.stripCaution = require("@/assets/images/ui_strip_images/ketone/ketone_plus.png");
      this.stripDanger = require("@/assets/images/ui_strip_images/ketone/great.png");
      this.ketoneDangerImg = require("@/assets/images/ui_strip_images/ketone/warning.png");
    } else if (this.type === "ketone" && this.ketoneMode) {
      // 케토시스 모드(분발, 진입, 적절, 좋음, 주의)
      this.normal = this.$i18n.t("exertion");
      this.warning = this.$i18n.t("enter");
      this.caution = this.$i18n.t("ketone_normal");
      this.danger = this.$i18n.t("ketone_great_level");
      this.ketoneDanger = this.$i18n.t("warning");
      this.cardTitle = this.$i18n.t("what_is_ketone");
      this.card2Title = this.$i18n.t("ketone_precautions");
      this.stripNormal = require("@/assets/images/ui_strip_images/ketone/normal_neg.png");
      this.stripWarning = require("@/assets/images/ui_strip_images/ketone/ketone_plus_minus.png");
      this.stripCaution = require("@/assets/images/ui_strip_images/ketone/ketone_plus.png");
      this.stripDanger = require("@/assets/images/ui_strip_images/ketone/great.png");
      this.ketoneDangerImg = require("@/assets/images/ui_strip_images/ketone/warning.png");
    }
  },
};
</script>

<style scoped>
.subtitle {
  padding-bottom: 20px;
  font-size: 18px;
  font-weight: 700;
}

.container-title__wrapper {
  width: 100%;
  align-items: center;
  padding: 10px 0px;
}

.guide-section-title {
  width: 100%;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 26px;
  text-align: center;
  letter-spacing: -0.03em;
}

.container-items__wrapper {
  width: 100%;
  padding: 0px 20px;
  margin: auto;
}

.urineTestItemsInfo-cards__wrapper {
  padding-left: 20px;
  padding-right: 20px;
  padding-bottom: 120px;
}

.gid-container-items {
  display: flex;
  width: 100%;
}

.gid-card__item {
  width: 100%;
  padding: 10px;
}

.gid-card__ketone {
  width: 50%;
  padding: 10px;
}
</style>
