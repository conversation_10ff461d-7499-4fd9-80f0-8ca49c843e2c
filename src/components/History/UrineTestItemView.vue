<template>
  <div class="history-tab-item__container">
    <div class="history-header__wrapper">
      <LineChart :historyData="historyData" :clicked="count" :type="type" @changeCount="changeCount" />
      <PageHandle />
    </div>
    <div class="myStatus-section__wrapper">
      <StatusBar :cymScore="cymScore" :type="type" :status="status" :historyData="historyData" />
      <div class="en-description"><br />{{ $t("status_description") }}</div>
    </div>
    <GuideComponent :type="type" />
    <GuideModal v-if="showGuide" :type="type" />
  </div>
</template>

<script>
import LineChart from "./Apexcharts.vue";
import PageHandle from "./PageHandle.vue";
import StatusBar from "./StatusBar.vue";
import GuideComponent from "./GuideSection.vue";
import GuideModal from "./GuideModal.vue";

export default {
  props: {
    type: String,
    historyData: Array,
  },
  components: {
    Line<PERSON>hart,
    PageHandle,
    StatusBar,
    GuideComponent,
    GuideModal,
  },
  data() {
    return {
      count: "recent",
      loading: false,
      loaded: false,
      scrollY: 0,
      cymScore: "",
    };
  },

  computed: {
    computedKorNam() {
      switch (this.type) {
        case "blood":
          return this.$i18n.t("tab_blood");
        case "glucose":
          return this.$i18n.t("tab_glucose");
        case "protein":
          return this.$i18n.t("tab_protein");
        case "ph":
          return this.$i18n.t("tab_ph");
        case "ketone":
          return this.$i18n.t("tab_ketone");
        default:
          return "";
      }
    },
    showGuide() {
      return this.$store.state.openGuide;
    },
    getHistoryData() {
      return this.historyData;
    },
    status() {
      if (this.historyData.length > 0) {
        return false;
      } else {
        return true;
      }
    },
  },

  watch: {
    showGuide(newVal) {
      // console.log(newVal, this.scrollY);
      if (newVal) {
        this.scrollY = window.scrollY;
        // document.body.classList.add("no-scroll");
        document.body.style.position = "fixed";
        document.body.style.width = "100%";
        document.body.style.top = `-${this.scrollY}px`;
        document.body.style.overflowY = "scroll";
      } else {
        // document.body.classList.remove("no-scroll");
        // window.scrollTo(0, this.scrollY);
        document.body.style.position = "";
        document.body.style.width = "";
        document.body.style.top = "";
        document.body.style.overflowY = "";
        window.scrollTo(0, this.scrollY);
      }
    },
    scrollY(newVal) {},
  },
  methods: {
    changeCount(count) {
      // console.log(count);
      this.$emit("changeCount", count);
      // this.getHistoryData();
    },
  },
  mounted() {
    window.scrollTo(0, 0);
    this.$emit("changeCount", this.count);
  },
};
</script>

<style lang="scss" scoped>
.history-header__wrapper {
  padding: 50px 0 15px;
  margin: auto;
  background-color: #fff;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
}

.myStatus-section__wrapper {
  padding: 25px 30px 5px;
}

.en-description {
  font-size: 16px;
  line-height: 23px;
  letter-spacing: -0.02em;
  text-align: left;
}

.mystate-indicator-title {
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 26px;
  text-align: center;
  letter-spacing: -0.03em;
}
</style>
