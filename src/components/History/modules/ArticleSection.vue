<template>
  <div>
    <div class="article-section__wrapper">
      <div class="card-title">
        <div class="title-kor">
          {{ computedTypes.ko }}<span class="title-eng p-b-4">({{ computedTypes.en }})</span>이란?
        </div>
      </div>
      <div class="explain-txt">
        {{ article_one }}
        <br /><br />
        {{ article_two }}
        <br /><br />
        {{ article_three }}
        <br /><br />
        {{ article_four }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    type: String,
  },
  data() {
    return {
      article_one: "",
      article_two: "",
      article_three: "",
      article_four: "",
    };
  },
  computed: {
    computedTypes() {
      if (this.type === "blood")
        return {
          en: "Blood",
          ko: "잠혈",
        };
      else if (this.type === "glucose")
        return {
          en: "Glucose",
          ko: "포도당",
        };
      else if (this.type === "protein")
        return {
          en: "Protein",
          ko: "단백질",
        };
      else if (this.type === "ph")
        return {
          en: "pH",
          ko: "산도",
        };
      else
        return {
          en: "Ketone",
          ko: "케톤",
        };
    },
  },
  mounted() {
    if (this.type === "blood") {
      this.article_one = this.$t("blood_info_one");
      this.article_two = this.$t("blood_info_two");
      this.article_three = this.$t("blood_info_three");
      this.article_four = "";
    } else if (this.type === "glucose") {
      this.article_one = this.$t("glucose_info_one");
      this.article_two = this.$t("glucose_info_two");
      this.article_three = this.$t("glucose_info_three");
      this.article_four = this.$t("glucose_info_four");
    } else if (this.type === "protein") {
      this.article_one = this.$t("protein_info_one");
      this.article_two = this.$t("protein_info_two");
      this.article_three = this.$t("protein_info_three");
      this.article_four = this.$t("protein_info_four");
    } else if (this.type === "ph") {
      this.article_one = this.$t("ph_info_one");
      this.article_two = this.$t("ph_info_two");
      this.article_three = this.$t("ph_info_three");
      this.article_four = "";
    } else if (this.type === "ketone") {
      this.article_one = this.$t("ketone_info_one");
      this.article_two = this.$t("ketone_info_two");
      this.article_three = this.$t("ketone_info_three");
      this.article_four = "";
    }
  },
};
</script>

<style scoped>
.article-section__wrapper {
  margin-top: 40px;
  background-color: #fff;
  border-radius: 20px 20px 0px 0px;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
}
.card-title {
  display: flex;
  justify-content: center;
  padding: 30px 0px;
}

.title-eng {
  font-family: GilroyBold;
  font-style: normal;
  font-size: 20px;
  line-height: 20px;
}

.title-kor {
  font-family: Noto Sans KR;
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 0px;
  letter-spacing: -0.03em;
}

.explain-txt {
  font-size: 0.87rem;
  padding: 0px 30px 100px 30px;
  text-align: justify;
  letter-spacing: -0.01em;
}
</style>
