<template>
  <div class="float-box">
    <div class="float-title">{{ floatTitle }}</div>
    <div class="float-date">{{ floatDate }}</div>
  </div>
</template>

<script>
export default {
  props: {
    floatTitle: String,
    floatDate: String,
  },
};
</script>

<style scoped>
.float-box {
  position: absolute;
  top: 10px;
  background-color: #ededed;
  width: fit-content;
  padding: 3px 4px;
  border-radius: 5px;
  margin-left: 40px;
  letter-spacing: -0.03em !important;
}

.float-title {
  font-weight: 700;
  font-size: 20px;
}

.float-date {
  font-weight: 400;
  font-size: 12px;
  color: #646464;
}
</style>
