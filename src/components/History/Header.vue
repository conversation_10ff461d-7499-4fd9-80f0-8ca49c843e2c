<template>
  <div>
    <fixed-header :threshold="50">
      <div class="nav-bar">
        <div class="fixed-header" :class="isIos ? 'nav-space' : 'nav-space-android'">
          <div>
            <div class="history-header_nav">
              <router-link to="/home"><v-icon>$back_btn_bold</v-icon></router-link>
            </div>
          </div>
        </div>
      </div>
    </fixed-header>

    <!-- <div class="pt-72 dp-30 pb-49 text-left mx-auto"> -->
    <!-- <CymLogo class="mylogo" />
      <div class="float-right">
        <div>
          <img
            @click="clickBluetooth"
            width="16.55"
            src="@/assets/images/toilet_icon.png"
          />
        </div> -->
    <!-- <AlertSign :position="position" v-if="showCompleteAlert" /> -->
    <!-- </div> -->
    <!-- </div> -->
  </div>
</template>

<script>
import FixedHeader from "vue-fixed-header";

export default {
  components: {
    FixedHeader,
  },

  data() {
    return {
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },

  methods: {
    clickBluetooth() {
      // console.log("clicked");
    },
  },
  mounted() {
    // TODO: header 통일
    // console.log("history page fixed header");
  },
};
</script>

<style lang="scss" scoped>
.nav-bar.vue-fixed-header--isFixed .fixed-header {
  background-color: #c9f4f8;
  display: block !important;
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 450px;
  z-index: 999;
}

.nav-bar.vue-fixed-header .fixed-header {
  display: none;
}

.nav-space {
  padding-top: 50px;
  padding-bottom: 15px;
}
.nav-space-android {
  padding-top: 20px;
  padding-bottom: 15px;
}

.history-header_nav {
  display: flex;
  justify-content: flex-start;
  padding-left: 30px;
}
</style>
