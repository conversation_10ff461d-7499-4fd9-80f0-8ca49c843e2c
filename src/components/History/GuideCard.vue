<template>
  <div>
    <div class="guide-card__wrapper" :class="cardBoxHeight">
      <div :class="isFullSize ? 'full-card__wrapper' : 'card-title__wrapper'" @click="clickHandler">
        <div class="card-title">
          {{ cardTitle }}
          <span v-if="type === 'ph' && cardTitle === this.$i18n.t('negative')">(pH5~8)</span>
          <span v-else-if="type === 'ph' && cardTitle === this.$i18n.t('positive')">(pH9)</span>
          <span v-else-if="type === 'protein' && cardTitle === this.$i18n.t('negative')">(-),(+/-)</span>
        </div>
        <div class="card-title__ic">
          <img src="@/assets/images/guide-card-arrow.png" alt="" />
        </div>
      </div>
      <div class="card-img__wrapper">
        <img :src="stripImage" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    stripImage: String,
    cardTitle: String,
    cardBoxHeight: String,
    type: String,
    isFullSize: Boolean,
  },
  data() {
    return {
      ketoneSmallTxt: "(+)",
    };
  },
  methods: {
    clickHandler() {
      // const bodyTag = document.getElementsByTagName("body");
      // bodyTag[0].classList.add("noscroll");
      // document.body.classList.remove("no-scroll");
      this.$store.commit("openGuideModal");
      // console.log(this.cardTitle);
      this.$store.commit("getCardTitle", this.cardTitle);
    },
  },
  mounted() {
    // console.log(this.isFullSize);
  },
};
</script>

<style scoped>
.guide-card__wrapper {
  width: 100%;
  height: 100%;
  background: #ffffff;
  border-radius: 20px;
  position: relative;
  border: 1px solid #ededed;
}

.min-height-140 {
  min-height: 140px;
}

.min-height-80 {
  min-height: 86px;
}

.min-height-140 > .card-title__wrapper {
  display: flex;
  justify-content: center;
}

.min-height-80 > .card-title__wrapper {
  display: flex;
  justify-content: space-between;
}

.card-title__wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  padding: 30px 0;
}

.full-card__wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: space-between;
  padding: 30px;
}

.card-title {
  font-size: 18px;
  text-align: center;
  font-style: normal;
  font-weight: 500;
  line-height: 26px;
}

.card-title__ic {
  padding-left: 5px;
}
.card-title__ic > img {
  width: 7px;
}

.card-img__wrapper {
  position: absolute;
  top: 90px;
  right: 0px;
}

.card-img__wrapper > img {
  width: 70%;
  float: right;
}

.card-title__txt--small {
  font-size: 12px;
  font-weight: 600;
}</style
>ƒ
