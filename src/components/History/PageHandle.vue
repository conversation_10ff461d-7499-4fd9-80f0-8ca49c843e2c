<template>
  <div class="chart-btn__wrapper">
    <div>
      <div class="select-box" @click="isOpenList">
        {{ limit }}{{ $t("times") }}
        <img src="@/assets/images/lightgrey_arrow.png" alt="arrow" />
      </div>
      <!-- </select> -->
      <div class="limit-list" v-if="showList">
        <div
          class="list"
          :class="{ selected: limit === option }"
          v-for="(option, idx) in limitArr"
          :key="idx"
          value="option"
          @click="limitHandler(idx)"
        >
          {{ option }}{{ $t("times") }}
        </div>
      </div>
    </div>
    <div class="btn__wrapper">
      <img :src="prevIcon" alt="link" @click="prevHandler" />
      <img :src="nextIcon" alt="link" @click="nextHandler" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    // totalCount: Number,
    // totalPage: Number,
  },
  data() {
    return {
      limitArr: [5, 10, 15, 20, 25, 30],
      isPrevData: false,
      isNextData: false,
      showList: false,
    };
  },
  computed: {
    prevIcon() {
      return this.isPrevData
        ? require("@/assets/images_assets/icons/prev_chevron.png")
        : require("@/assets/images_assets/icons/prev_btn.png");
    },
    nextIcon() {
      return this.isNextData
        ? require("@/assets/images_assets/icons/next_chevron.png")
        : require("@/assets/images_assets/icons/next_btn.png");
    },
    page: {
      get() {
        return this.$store.state.historyPage;
      },
      set(value) {
        this.$store.commit("SET_HISTORY_PAGE", value);
      },
    },
    limit() {
      return this.$store.state.historyLimit;
    },
    totalPage() {
      return this.$store.state.historyTotalPage;
    },
  },
  watch: {
    page(newVal) {
      // console.log("cur page", newVal);
      this.isNextData = newVal <= 1 ? false : true;
      this.isPrevData = this.totalPage > newVal ? true : false;
    },
    limit(newVal) {
      // console.log("limit", newVal);
      // console.log("total page props", this.totalPage);
    },
    totalPage(newVal) {
      this.isNextData = this.page <= 1 ? false : true;
      this.isPrevData = newVal > this.page ? true : false;
    },
  },
  methods: {
    prevHandler() {
      if (this.page > 0 && this.totalPage > this.page) {
        const page = (this.page += 1);
        this.$store.commit("SET_HISTORY_PAGE", page);
        this.$store.commit("SET_TOOLTIP_INDEX", null);
        // this.$emit("pageHandler", this.page);
      }
    },
    nextHandler() {
      if (this.page > 1) {
        const page = (this.page -= 1);
        this.$store.commit("SET_HISTORY_PAGE", page);
        this.$store.commit("SET_TOOLTIP_INDEX", null);
        // this.$emit("pageHandler", this.page);
      }
    },

    limitHandler(idx) {
      // console.log(idx);
      // this.limit = limit;
      this.$store.commit("SET_HISTORY_LIMIT", this.limitArr[idx]);
      this.$store.commit("SET_HISTORY_PAGE", 1);
      this.$store.commit("SET_TOOLTIP_INDEX", null);
      this.showList = false;

      // this.$emit('limitHandler', this.limit)
    },

    isOpenList() {
      // console.log("clicked");
      this.showList = !this.showList;
    },
  },
  mounted() {
    this.isNextData = this.$store.state.historyPage <= 1 ? false : true;
    this.isPrevData = this.totalPage > this.$store.state.historyPage ? true : false;
  },
};
</script>

<style lang="scss" scoped>
.chart-btn__wrapper {
  padding: 0px 30px 0 53px;
  display: flex;
  justify-content: space-between;
  height: 30px;
}

.select-box {
  border: 1px solid #c8c8c8;
  border-radius: 5px;
  padding: 5px 8px 5px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  width: 70px;
  height: 30px;
  position: absolute;
  z-index: 99;
  img {
    width: 13px;
  }
}

.selected {
  background-color: #ededed;
}

.btn__wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 50px;
  img {
    width: 12px;
    height: 19.2px;
  }
}

.limit-list {
  position: relative;
  display: flex;
  flex-direction: column;
  z-index: 99;
  justify-content: space-between;
  border: 1px solid #c8c8c8;
  border-radius: 5px;
  top: 30px;
  text-align: left;
  background-color: #fff;
  width: 70px;
  height: 210px;
  font-size: 14px;
}

.list {
  padding: 5px 8px 5px 12px;
  height: 35px;
  display: flex;
  align-items: center;
  &:active {
    background-color: #ededed;
  }
}
</style>
