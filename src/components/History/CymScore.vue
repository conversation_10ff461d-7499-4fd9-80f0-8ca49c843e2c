<template>
  <div>
    <div class="bg-modal">
      <div class="alert-window">
        <div class="alert-container">
          <div class="close-btn">
            <div class="close-icon" @click="closeModal">
              <img src="@/assets/images_assets/icons/close-btn-solid-ic.png" />
            </div>
          </div>
          <div class="alert-title">
            <span v-html="$t('cym702_score_info_title')"></span>
          </div>
          <div class="alert-detail">
            <p v-html="this.$i18n.t('first_tutorial_content')"></p>
          </div>
          <div class="alert-table">
            <div class="cym-table-row header">
              <div class="cym-table-col good">{{ $t("good") }}</div>
              <div class="cym-table-col normal">{{ $t("normal") }}</div>
              <div class="cym-table-col warn">{{ $t("warning") }}</div>
              <div class="cym-table-col caution">{{ $t("caution") }}</div>
            </div>
            <div class="cym-table-row">
              <div class="cym-table-col score">100~76</div>
              <div class="cym-table-col score">75~51</div>
              <div class="cym-table-col score">50~26</div>
              <div class="cym-table-col score">25~0</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    showOverlay: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    closeModal() {
      this.$store.commit("CLOSEGUIDE");
    },
  },
};
</script>
<style lang="scss" scoped>
.bg-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 450px;
  height: 100%;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.alert-window {
  background-color: #fff;
  width: 100%;
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 25px 0 50px 0;
}

.alert-container {
  display: flex;
  width: 100%;
  padding: 0 20px;
  flex-direction: column;
  align-items: center;
}

.alert-title {
  display: flex;
  justify-content: center;
  font-size: 22px;
  line-height: 22px;
  color: #41d8e6;
  font-weight: 700;
  margin-bottom: 20px;
}

.alert-detail {
  width: 100%;
  display: flex;
  justify-content: center;
  text-align: justify;
  font-size: 18px;
  color: #323232;
}

.score {
  font-size: 18px;
  color: black;
  font-family: GilroyMedium;
}

.good {
  color: #41d8e6;
  font-weight: 500;
  font-size: 14px;
}

.normal {
  color: #00bb00;
  font-weight: 500;
  font-size: 14px;
}

.warn {
  color: #ffcc00;
  font-weight: 500;
  font-size: 14px;
}

.caution {
  color: #ee0000;
  font-weight: 500;
  font-size: 14px;
}

.cym-table-row {
  display: flex;
}

.cym-table-col {
  font-size: 18px;
  flex: 1;
  text-align: center;
  padding: 5px 0px;
}

.alert-table {
  width: 100%;
  background-color: #f8f8f8;
  border-radius: 5px;
  padding: 6px;
}

.header {
  border-bottom: 1px solid #ededed;
}

.close-btn {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  padding-left: 5px;
}

.close-icon {
  width: 25px;
  height: 25px;
  img {
    width: 100%;
  }
}
</style>
