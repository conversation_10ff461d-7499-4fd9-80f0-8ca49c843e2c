<template>
  <div>
    <div class="cymchart__wrapper">
      <div class="float-box" v-if="showFloatBox || tabChanged">
        <div v-if="isKo" class="float-box__title">
          {{ totalCount }}{{ $t("test_count") }} {{ avgScore }}{{ $t("score") }}
        </div>
        <div v-else class="float-box__title">
          {{ $t("test_count") }} {{ totalCount }} {{ $t("score") }}
          {{ avgScore }}
        </div>
        <div v-if="startDate !== ''" class="float-box__date">{{ startDate }} ~ {{ endDate }}</div>
      </div>
      <div class="history-header__wrapper">
        <line-chart
          v-if="loaded"
          :historyData="historyData"
          :page="type"
          :count="count"
          @clickEventHandler="clickEventHandler"
        ></line-chart>
        <PageHandleVue />
      </div>
    </div>
    <div class="my-status-section__wrapper">
      <StatusBar
        :initialState="initialState"
        :resultExposeTest="floatTitle"
        :cymtestDate="floatDate"
        :noData="status"
        :totalScore="avgScore"
        :totalCount="totalCount"
      />
    </div>
  </div>
</template>

<script>
import LineChart from "@/components/Chart/LineChart.vue";
import StatusBar from "@/components/History/CymStatusBar.vue";
import PageHandleVue from "./PageHandle.vue";
// import ChartBtn from "@/components/History/ChartBtn.vue";
import { mapGetters } from "vuex";

export default {
  name: "Cym702",
  props: {
    historyData: Array,
    totalCount: Number,
    totalPage: Number,
    avgScore: Number,
    startDate: String,
    endDate: String,
  },
  components: {
    LineChart,
    StatusBar,
    PageHandleVue,
    // ChartBtn,
  },
  data() {
    return {
      type: "cym",
      count: "recent",
      loading: false,
      loaded: false,
      chartData: [],
      chartLabels: [],
      fullTimeDate: [],
      cymScore: "",
      floatTitle: "",
      floatDate: "",
      resultExposeTest: "",
      showFloatBox: true,
      isKo: true,
      initialState: true,
      limitArr: [5, 10, 15, 20, 25, 30],
      // page: 1,
      // limit: 5,
      // totalPage: 1,
      isPrevData: false,
      isNextData: false,
    };
  },
  computed: {
    ...mapGetters(["cymAvg"]),
    status() {
      if (this.historyData.length > 0) {
        return false;
      } else {
        return true;
      }
    },
    setHistoryData() {
      return this.historyData;
    },
    tabChanged() {
      return this.$store.state.tabChange;
    },
  },
  watch: {
    historyData(newVal) {
      // console.log(newVal);
      this.chartData = newVal;
      this.loaded = true;
    },
    cymAvg(newVal) {
      // console.log(newVal);
      this.cymScore = newVal;
    },
    avgScore() {
      this.$store.commit("GET_CYM_AVG", this.avgScore);
    },
    tabChanged() {
      this.$store.commit("GET_CYM_AVG", this.avgScore);
    },
  },
  methods: {
    clickEventHandler() {
      this.showFloatBox = false;
      this.initialState = false;
      this.$store.commit("SET_TAB_STATE", false);
    },
    changeCount(count) {
      // console.log("count clicked");
      this.loaded = false;
      this.count = count;
      this.showFloatBox = true;
      this.$store.commit("GET_CYM_AVG", this.avgScore);
      this.$emit("changeCount", count);
      this.getHistoryData();
      this.initialState = true;
    },
    getHistoryData() {
      this.loaded = true;
      this.chartData = this.historyData;
      this.$store.commit("GET_CYM_AVG", this.avgScore);
    },
  },
  mounted() {
    // console.log("cym score data", this.historyData);
    // console.log(this.startDate);
    // console.log(this.totalPage);
    this.getHistoryData();
    this.isKo = this.$i18n.locale === "ko";
    // this.isNextData = this.$store.state.historyPage <= 1 ? false : true;
    // this.isPrevData = this.totalPage > this.$store.state.historyPage ? true : false;
  },
};
</script>

<style lang="scss" scoped>
.cymchart__wrapper {
  padding-top: 50px;
  padding-bottom: 15px;
  margin: auto;
  background-color: #fff;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
}

.my-status-section__wrapper {
  min-height: 30vh;
  padding: 10px 30px 20vh;
}

.float-box {
  position: absolute;
  top: 15px;
  left: 60px;
  background-color: #ededed;
  padding: 0px 5px;
  border-radius: 5px;
  letter-spacing: -0.03em !important;

  .float-box__title {
    font-weight: 700;
    font-size: 22px;
    line-height: 29px;
    color: #000000;
  }

  .float-box__date {
    font-family: GilroyMedium;
    color: #646464;
    font-size: 14px;
    font-weight: 400;
    line-height: 17px;
    letter-spacing: 0em;
    text-align: center;
  }
}
.chart-btn__wrapper {
  padding: 0px 30px 0 53px;
  display: flex;
  justify-content: space-between;
  // height: 40px;
}

.select-box {
  border: 1px solid #ededed;
  border-radius: 5px;
  height: 100%;
  padding: 5px 15px;
  display: flex;
  align-items: center;
  font-size: 14px;
  position: absolute;
  top: 0;
  img {
    width: 13px;
  }
}

.btn__wrapper {
  // width: 100%;
  display: flex;
  align-items: center;
  gap: 25px;
  padding: 0 0.2rem 0 0 !important;
  img {
    width: 12px;
    height: 19.2px;
  }
}

.limit-list {
  position: relative;
  display: flex;
  flex-direction: column;
  top: 0;
  height: 100%;
}
</style>
