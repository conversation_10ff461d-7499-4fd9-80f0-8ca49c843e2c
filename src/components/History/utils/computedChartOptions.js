import i18n from "../../../i18n.js";
const ketoneMode = JSON.parse(localStorage.getItem("ketoneMode"));

export const computeYaxis = (type, value) => {
  switch (type) {
    case "blood":
      if (value === 1) return "0";
      if (value === 2) return "10";
      if (value === 3) return "50";
      if (value === 4) return "250";
      else return "";
    case "protein":
      if (value === 2) return "10";
      else if (value === 3) return "30";
      else if (value === 4) return "100";
      else if (value === 5) return "300";
      else return "";
    case "glucose":
      if (value === 2) return "100";
      else if (value === 3) return "250";
      else if (value === 4) return "500";
      else if (value === 5) return "1000";
      else return "";
    case "ph":
      if (value === 1) return "pH 5";
      else if (value === 2) return "pH 6";
      else if (value === 3) return "pH 7";
      else if (value === 4) return "pH 8";
      else if (value === 5) return "pH 9";
      else return "";
    case "ketone":
      if (!ketoneMode) {
        if (value === 1) return "100";
        else if (value === 2) return "75";
        else if (value === 3) return "50";
        else if (value === 4) return "25";
        else if (value === 5) return "0";
        else return "";
      } else {
        if (value === 1) return "0";
        else if (value === 2) return "50";
        else if (value === 3) return "75";
        else if (value === 4) return "100";
        else if (value === 5) return "50";
        else return "";
      }
  }
};

// 레벨별 상태 텍스트 반환하는 함수
export const computeKorScore = (type, value) => {
  switch (type) {
    case "blood":
      if (value === 1) return i18n.t("negative");
      else if (value === 2) return i18n.t("positive_plus");
      else if (value === 3) return i18n.t("positive_double_plus");
      else if (value === 4) return i18n.t("positive_triple_plus");
      else return "";
    case "protein":
      if (value === 1) return i18n.t("negative_minus");
      else if (value === 2) return i18n.t("negative_plus_minus");
      else if (value === 3) return i18n.t("positive_plus");
      else if (value === 4) return i18n.t("positive_double_plus");
      else if (value === 5) return i18n.t("positive_triple_plus");
      else if (value === 6) return i18n.t("positive_quadruple_plus");
      else return "";
    case "glucose":
      if (value === 1) return i18n.t("negative_minus");
      else if (value === 2) return i18n.t("negative_plus_minus");
      else if (value === 3) return i18n.t("positive_plus");
      else if (value === 4) return i18n.t("positive_double_plus");
      else if (value === 5) return i18n.t("positive_triple_plus");
      else if (value === 6) return i18n.t("positive_quadruple_plus");
      else return "";
    case "ph":
      if (value === 1 || value === 2 || value === 3 || value === 4) return i18n.t("negative");
      // else if (value === 2) return i18n.t("negative_six");
      // else if (value === 3) return i18n.t("negative_seven");
      // else if (value === 4) return i18n.t("negative_eight");
      else if (value === 5) return i18n.t("positive");
      else return "";
    case "ketone":
      // 당뇨모드
      if (!ketoneMode) {
        if (value === 1) return i18n.t("negative");
        else if (value === 2) return i18n.t("positive_plus_minus");
        else if (value === 3) return i18n.t("positive_plus");
        else if (value === 4) return i18n.t("positive_double_plus");
        else if (value === 5) return i18n.t("positive_triple_plus");
        else return "";
      }
      // 케토시스 모드
      else {
        if (value === 1) return i18n.t("exertion");
        else if (value === 2) return i18n.t("enter");
        else if (value === 3) return i18n.t("ketone_normal");
        else if (value === 4) return i18n.t("ketone_great_level");
        else if (value === 5) return i18n.t("warning");
        else return "";
      }
  }
};

// 상태 텍스트를 원하는 텍스트로 변환해 반환하는 함수(class명 사용을 위해 사용중)
export const computeEnScore = (score, type) => {
  // console.log(score);
  switch (score) {
    case i18n.t("negative"):
      return "negative";
    // case i18n.t("negative_six"):
    //   return "negative-six";
    // case i18n.t("negative_seven"):
    //   return "negative-seven";
    // case i18n.t("negative_eight"):
    //   return "negative-eight";
    case i18n.t("negative_minus"):
      return "negative";
    case i18n.t("negative_plus_minus"):
      return "negative";
    case i18n.t("positive"):
      return "positive";
    case i18n.t("positive_plus_minus"):
      return "positive-plus-minus";
    case i18n.t("positive_plus"):
      return "positive-plus";
    case i18n.t("positive_double_plus"):
      return "positive-double";
    case i18n.t("positive_triple_plus"):
      return "positive-triple";
    case i18n.t("positive_quadruple_plus"):
      return "positive-quadruple";
    case i18n.t("normal"):
      return "ketone_normal";
    case i18n.t("ketone_normal"):
      return "ketone_normal";
    case i18n.t("ketone_warning_level_plus_minus"):
      return "caution_plus_minus";
    case i18n.t("ketone_warning_level_plus"):
      return "caution_plus";
    case i18n.t("warning"):
      return "ketone_warning";
    case i18n.t("caution"):
      return "caution";
    case i18n.t("danger"):
      return "danger";
    case i18n.t("good"):
      return "ketone_good";
    case i18n.t("exertion"):
      return "exertion";
    case i18n.t("enter"):
      return "enter";
    case i18n.t("ketone_great_level"):
      return "ketone_great_level";
    default:
      return "";
  }
};

export const computeColor = (score, type) => {
  switch (score) {
    case i18n.t("negative"):
      return "#00bb00";
    case i18n.t("negative_minus"):
      return "#00bb00";
    case i18n.t("negative_plus_minus"):
      return "#00bb00";
    case i18n.t("positive"):
      return "#ffcc00";
    case i18n.t("positive_plus_minus"):
      return "#ffcc00";
    case i18n.t("positive_plus"):
      return "#ffcc00";
    case i18n.t("positive_double_plus"):
      return "#ff6600";
    case i18n.t("normal"):
      return "#F3B6B6";
    case i18n.t("exertion"):
      return "#FFF8F4";
    case i18n.t("enter"):
      return "#FAC9BF";
    case i18n.t("warning"):
      return "#8F3F6E";
    case i18n.t("ketone_warning_level_plus_minus"):
      return "#ffcc00";
    case i18n.t("ketone_warning_level_plus"):
      return "#ffcc00";
    case i18n.t("caution"):
      return "#ff6600";
    case i18n.t("danger"):
      return "#646464";
    case i18n.t("good"):
      return "#B83A71";
    default:
      return "#646464";
  }
};
