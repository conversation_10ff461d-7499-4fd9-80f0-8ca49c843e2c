<template>
  <div class="carousel">
    <div class="last-btn__wrapper">
      <div class="last-btn" :style="{ backgroundColor: color, color: textColor }" @click="btn<PERSON>and<PERSON>">
        {{ title }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    color: String,
    title: String,
    path: String,
    textColor: String,
  },
  data() {
    return {};
  },
  methods: {
    btnHandler() {
      this.$router.push({ path: this.path });
      this.$emit("changeCurrentState");
      this.$emit("setLocalStorage");
    },
  },
};
</script>

<style lang="scss" scoped>
.last-btn__wrapper {
  width: 100%;
  position: fixed;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 20.83%, #ffffff 100%);
  bottom: 0;
  height: calc(5vh + 90px);
  left: 0;
  padding: 30px 30px 0px 30px;
}

.last-btn {
  height: 50px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  letter-spacing: -0.03em;
  z-index: 999;
  max-width: 390px !important;
  width: 100%;
  border-radius: 10px;
  line-height: 22px;
  font-size: 22px;
  font-weight: bold;
  color: #ffffff;
  margin: 0 auto;
}
</style>
