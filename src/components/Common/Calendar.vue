<template>
  <div class="calendar">
    <vc-date-picker
      :masks="{ title: 'YYYY.MM' }"
      :attributes="calendarAttributes"
      title-position="left"
      is-expanded
      trim-weeks
      mode="date"
      :key="componentKey"
      v-model="selectedDate"
      :locale="isKo ? 'ko' : 'en'"
      ref="myButton"
    />
    <button :class="isIos ? 'move-btn' : 'move-btn-android'" @click="goToToday">
      {{ $t("today") }}
    </button>
  </div>
</template>

<script>
export default {
  props: { calendarAttributes: Array },

  data() {
    return {
      componentKey: 0,
      selectedDate: null,
      isKo: true,
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },

  watch: {
    selectedDate(newDate) {
      this.$emit("activeDate", newDate);
    },
    componentKey(newVal) {
      // console.log(newVal);
    },
  },

  methods: {
    goToToday() {
      this.selectedDate = new Date(); // 현재 날짜로 설정
      this.componentKey++;
    },
    handleButtonClick(event) {
      // 버튼 클릭 이벤트 처리
      // console.log("my-button 클릭됨!");
    },
  },

  mounted() {
    const leftButton = document.querySelectorAll(".vc-arrow is-left");
    const rightButton = document.querySelectorAll(".vc-arrow is-right");

    // 각 요소에 클릭 이벤트 리스너를 등록합니다.
    leftButton.forEach((button) => {
      button.addEventListener("click", this.handleButtonClick);
    });
    rightButton.forEach((button) => {
      button.addEventListener("click", this.handleButtonClick);
    });
    this.isKo = this.$i18n.locale === "ko";
  },
};
</script>

<style lang="scss" scoped>
.calendar {
  background: #fff;
  box-shadow: 0px 2px 5px 0px #0000000d;
  padding: 25px 10px 20px;
  transition: all 0.2s;
  text-align: center;
}

.move-btn {
  z-index: 99;
  position: absolute;
  top: 230px;
  left: 115px;
  border: 1px solid #ededed;
  color: #858585;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  // font-family: Noto Sans KR;
  line-height: 16px;
  padding: 0 9px;
  height: 28px;
  &:active {
    background-color: #ededed;
  }
}
.move-btn-android {
  z-index: 99;
  position: absolute;
  top: 197px;
  left: 115px;
  border: 1px solid #ededed;
  color: #858585;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  // font-family: Noto Sans KR;
  line-height: 16px;
  padding: 0 9px;
  height: 28px;
  &:active {
    background-color: #ededed;
  }
}

.vc-day {
  min-height: 50px !important;
}

.vc-day-content {
  &:hover {
    background-color: transparent !important;
  }
}

.vc-header {
  padding: 0 !important;
  padding-left: 20px !important;
  height: 55px;
}

.vc-svg-icon {
  color: #000000;
  width: 30px;
  height: 35px;
}

.vc-title {
  font-family: GilroyMedium;
  font-weight: unset !important;
  font-size: 22px !important;
  color: #000000 !important;
}

.vc-weekday {
  font-family: Noto Sans KR;
  font-size: 16px !important;
  font-weight: unset !important;
  color: #646464 !important;
  margin-bottom: 7px;
}

.vc-day-content {
  font-family: GilroyMedium;
  font-size: 22px !important;
  font-weight: unset !important;
  margin: 10px !important;
}

.vc-arrows-container {
  top: -2px;
}

.vc-dot {
  width: 8px !important;
  height: 8px !important;
}

.vc-day-box-center-bottom {
  padding-bottom: 2px;
}

.vc-nav-header {
  .vc-svg-icon {
    color: #ffffff !important;
  }
}

.vc-nav-arrow {
  &:hover {
    background-color: transparent !important;
  }
  &:focus {
    border: none !important;
  }
}

.vc-nav-title {
  font-family: GilroyBold !important;
  font-size: 16px !important;
  padding-top: 7px;
  color: #fff !important;
  &:hover {
    background-color: #41d8e6 !important;
    border: none !important;
  }
}

.vc-nav-popover-container {
  background-color: #41d8e6 !important;
  border: none !important;
  border-radius: 18px !important;
  padding: 4px 8px 8px !important;
  box-shadow: none !important;
}

.vc-popover-content-wrapper {
  left: -20px !important;
}

.vc-nav-item {
  // font-family: Noto Sans KR;
  font-weight: 700;
  font-size: 16px !important;
  &:hover {
    color: #000000 !important ;
    background-color: #fff !important;
    border-radius: 5px !important;
  }
  &:focus {
    border: none !important;
  }
}
.vc-nav-item.is-active {
  color: #000000 !important ;
  background-color: #fff !important;
  box-shadow: none !important;
}
.vc-nav-items {
  grid-row-gap: 5px;
  grid-column-gap: 7px;
}

.vc-pane-container,
.vc-pane-layout {
  transition: none !important;
  animation: none !important;
}
</style>
