<template>
  <div>
    <CheckModal
      v-if="analysisWarningModalOpen"
      @cancelHandler="cancelHandler"
      @confirmHandler="confirmHandler"
      :isWarning="true"
      :content="this.$i18n.t('do_not_reuse_boat')"
    />
    <div class="mobile">
      <div>
        <div class="bottom-nav">
          <div class="bottom-nav__btn" @click="goToHome">
            <div class="btn__icon">
              <v-icon :size="30" id="home-icon" v-if="getPath === '/home'">$home_on</v-icon>
              <v-icon :size="30" id="home-icon" v-else>$home_off</v-icon>
              <div class="nav_text">{{ $t("nav_home") }}</div>
            </div>
          </div>

          <div class="bottom-nav__btn" v-if="!isSub" @click="moveSolution">
            <div class="btn__icon">
              <v-icon :size="30" id="solution-icon" v-if="getPath === '/solution'">$solution_on</v-icon>
              <v-icon :size="30" id="solution-icon" v-else>$solution_off</v-icon>
              <div class="nav_text">{{ $t("nav_solution") }}</div>
            </div>
            <AlertSign :position="position" v-if="isSolution" />
          </div>

          <div class="bottom-nav__btn" @click="goToExam">
            <div class="btn__icon">
              <v-icon :size="30">$exam_off</v-icon>
              <div class="nav_text">{{ $t("nav_test") }}</div>
            </div>
          </div>

          <div class="bottom-nav__btn" @click="moveShop">
            <div class="btn__icon">
              <v-icon :size="30" id="shop-icon">$shop_off</v-icon>
              <div class="nav_text">{{ $t("nav_shop") }}</div>
            </div>
            <!-- <AlertSign :position="position" v-if="isShop" /> -->
          </div>

          <div class="bottom-nav__btn" @click="goToMypage">
            <div class="btn__icon">
              <v-icon :size="30" id="my-icon" v-if="getPath === '/mypage'">$my_on</v-icon>
              <v-icon :size="30" id="my-icon" v-else>$my_off</v-icon>
              <div class="nav_text">{{ $t("nav_my") }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AlertSign from "@/components/Common/AlertSign.vue";
import CheckModal from "@/components/Common/CheckModal.vue";

export default {
  name: "Navigation",
  props: {
    path: String,
  },
  components: {
    AlertSign,
    CheckModal,
  },
  data: () => ({
    isIos:
      navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
      navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    type: "",
    position: "bottom",
    isActive: false,
    showCompleteAlert: false,
    isHome: false,
    isSolution: false,
    isExam: false,
    isShop: false,
    isMypage: false,
    survey: "",
    isSub: false,
  }),

  computed: {
    getPath() {
      return this.path;
    },
    surveyStatus() {
      return this.$store.state.surveyStatus;
    },
    lastAnalysisTime() {
      return this.$store.state.lastAnalysisTime;
    },
    analysisWarningModalOpen() {
      return this.$store.state.analysisWarningModalOpen;
    },
  },

  watch: {
    surveyStatus(newVal) {
      this.survey = newVal === "" ? localStorage.getItem("surveyStatus") : newVal;
    },
  },
  mounted() {
    this.type = localStorage.getItem("type") ?? "human";
    const subjects = JSON.parse(sessionStorage.getItem("subjects"));
    const selectUser = Number(sessionStorage.getItem("selectUser"));
    this.isSub = subjects !== null && selectUser >= 1 ? true : false;
  },
  updated() {
    setTimeout(() => {
      this.isSolution = false;
      this.isShop = false;
    }, 2000);
  },
  methods: {
    isLastAnalysisWithinOneHour() {
      const now = new Date();
      const createdAtDate = new Date(this.lastAnalysisTime);
      const timeDiff = now - createdAtDate;
      const minutesPassed = timeDiff / 60000;
      return minutesPassed < 60;
      // return true;
    },

    cancelHandler() {
      this.$store.commit("setAnalysisWarningModal", false);
    },

    confirmHandler() {
      this.$router.push({ name: "ExamIntro" });
    },

    moveSolution() {
      sessionStorage.removeItem("savedBookmarkList");
      sessionStorage.removeItem("savedFoodList");
      sessionStorage.removeItem("savedBookmarkDataPage");
      sessionStorage.removeItem("scrollPosition");
      sessionStorage.removeItem("solutionCurTab");
      sessionStorage.removeItem("scrollbookmarkPosition");
      sessionStorage.removeItem("savedDataPage");
      document.body.classList.remove("no-scroll");

      const mainUserId = Number(localStorage.getItem("subjectId"));
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser"));
      const status = this.survey === "" ? localStorage.getItem("surveyStatus") : this.survey;
      if (status === "survey_ongoing" || status === "survey_ready") {
        this.$store.commit("setSurveyModal", open);
      } else {
        if (subjects !== null) {
          if (this.type !== "kardio" && mainUserId === subjects[selectedId].id)
            this.$router.push({ path: "/solution" });
          else if (this.type === "kardio") this.isSolution = true;
          else this.isSolution = true;
        } else {
          this.type === "kardio" ? (this.isSolution = true) : this.$router.push({ path: "/solution" });
        }
      }
    },
    moveShop() {
      sessionStorage.removeItem("savedBookmarkList");
      sessionStorage.removeItem("savedFoodList");
      sessionStorage.removeItem("savedBookmarkDataPage");
      sessionStorage.removeItem("scrollPosition");
      sessionStorage.removeItem("solutionCurTab");
      sessionStorage.removeItem("scrollbookmarkPosition");
      sessionStorage.removeItem("savedDataPage");
      document.body.classList.remove("no-scroll");
      /*global Webview*/
      /*eslint no-undef: "error"*/
      this.isShop = true;
      const bridge_msg = {
        action: "goCym702",
        url: "https://smartstore.naver.com/cym702",
      };
      Webview.goCym702(bridge_msg);
      const message = "https://smartstore.naver.com/cym702";
      Webview.openUrl(message);
    },
    showAlert() {
      this.showCompleteAlert = true;
    },
    closeAlert(fromChild) {
      this.showCompleteAlert = fromChild;
    },
    goToHome() {
      sessionStorage.removeItem("savedBookmarkList");
      sessionStorage.removeItem("savedFoodList");
      sessionStorage.removeItem("savedBookmarkDataPage");
      sessionStorage.removeItem("scrollPosition");
      sessionStorage.removeItem("solutionCurTab");
      sessionStorage.removeItem("scrollbookmarkPosition");
      sessionStorage.removeItem("savedDataPage");
      document.body.classList.remove("no-scroll");
      this.$router.push({ name: "Home" });
    },
    goToExam() {
      sessionStorage.removeItem("savedBookmarkList");
      sessionStorage.removeItem("savedFoodList");
      sessionStorage.removeItem("savedBookmarkDataPage");
      sessionStorage.removeItem("scrollPosition");
      sessionStorage.removeItem("solutionCurTab");
      sessionStorage.removeItem("scrollbookmarkPosition");
      sessionStorage.removeItem("savedDataPage");
      document.body.classList.remove("no-scroll");
      const status = this.survey === "" ? localStorage.getItem("surveyStatus") : this.survey;
      if (status === "survey_ongoing" || status === "survey_ready") {
        this.$store.commit("setSurveyModal", true);
      } else if (this.isLastAnalysisWithinOneHour()) {
        this.$store.commit("setAnalysisWarningModal", true);
      } else {
        this.$router.push({ name: "ExamIntro" });
      }
    },
    goToMypage() {
      sessionStorage.removeItem("savedBookmarkList");
      sessionStorage.removeItem("savedFoodList");
      sessionStorage.removeItem("savedBookmarkDataPage");
      sessionStorage.removeItem("scrollPosition");
      sessionStorage.removeItem("solutionCurTab");
      sessionStorage.removeItem("scrollbookmarkPosition");
      sessionStorage.removeItem("savedDataPage");
      document.body.classList.remove("no-scroll");
      this.$router.push({ name: "Mypage" });
    },
  },
  beforeDestroy() {
    this.$store.commit("setAnalysisWarningModal", false);
  },
};
</script>

<style lang="scss" scoped>
.bottom-nav {
  max-width: 450px;
  width: 100%;
  position: fixed;
  bottom: -0.5px;
  display: flex;
  padding: 5px 10px 25px 10px;
  // background-color: #c9f4f8;
  background-color: #fff;
  border-top: 0.5px solid #ededed;
  z-index: 9999;
}

::v-deep .v-icon {
  padding-left: 3px !important;
  padding-top: 15px !important;
}

.bottom-nav__btn {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  margin: 0 auto;
}

.btn__icon {
  transition: 0.3s;
  width: 100%;
  // padding-left: 20px;
}

.nav_text {
  font-size: 12px;
  color: #646464;
  font-weight: 500;
  letter-spacing: -0.03em;
  width: 100%;
  display: flex;
  justify-content: center;
}

#home-icon {
  padding-left: 5px !important;
}

#solution-icon {
  padding-left: 7px !important;
}

#shop-icon {
  padding-left: 5px !important;
}

#my-icon {
  padding-left: 6px !important;
}
</style>
