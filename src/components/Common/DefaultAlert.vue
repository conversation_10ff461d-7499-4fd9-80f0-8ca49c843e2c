<template>
  <div>
    <div class="bg-modal">
      <div class="alert-window">
        <!-- <div class="alert-window__title">🎉 가입을 축하합니다!</div> -->
        <div class="alert-window__content">
          {{ $t("join_modal_message") }}
        </div>
        <div class="alert-window__btn" @click="closeAlertHandler">{{ $t("confirm_btn") }}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    showOverlay: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    closeAlertHandler() {
      this.$emit("closeAlertHandler");
    },
  },
};
</script>
<style scoped>
.bg-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 450px;
  height: 100%;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-window {
  background-color: #fff;
  width: 300px;
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 25px 15px;
}

.alert-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.alert-window__title {
  font-size: 18px;
  font-weight: 700;
}

.alert-window__content {
  font-size: 16px;
  padding-top: 15px;
}

.alert-window__btn {
  width: 100%;
  padding: 0px 20px;
  padding-top: 10px;
  text-align: right;
  font-weight: 700;
  color: #41d8e6;
}
</style>
