<template>
  <div>
    <div class="alert-tooltip-top" :class="isIos ? 'top' : 'top-android'" v-if="position === 'top'">
      {{ $t("current_no_service") }}
    </div>
    <div class="alert-tooltip-bottom" v-if="position === 'bottom' && appType === 'kardio'">
      {{ $t("current_no_service") }}
    </div>
    <div class="alert-tooltip-bottom" v-else-if="position === 'bottom'">
      {{ $t("only_main") }}
    </div>
    <div class="alert-tooltip-right-bottom" v-else>잘못된 입력입니다.</div>
    <div
      class="alert-result"
      :class="
        lang === 'ko' ? (isIos ? 'result' : 'result-android') : isIos ? 'result en-text' : 'result-android en-text'
      "
      v-if="position === 'result'"
    >
      {{ $t("go_result_popup") }}
    </div>
  </div>
</template>

<script>
export default {
  props: {
    position: {
      type: String,
    },
  },

  data() {
    return {
      lang: "",
      appType: "",
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
  mounted() {
    this.appType = localStorage.getItem("type") ?? "human";
    this.lang = this.$i18n.locale === "ko" ? "ko" : "en";
  },
};
</script>

<style lang="scss" scoped>
.alert-tooltip-bottom {
  width: 90px;
  text-align: center;
  color: #fff;
  background-color: #41d8e6;
  border-radius: 5px;
  // box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  font-size: 14px;
  height: 34px;
  line-height: 34px;
  font-weight: 500;
  letter-spacing: -0.1em;
  position: absolute;
  top: -45px;
  left: -20px;
  transition: ease-in 0.4s;
}

.alert-tooltip-bottom:after {
  border-top: 8px solid #41d8e6;
  border-left: 2px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 0px solid transparent;
  content: "";
  position: absolute;
  top: 34px;
  left: 60px;
}

.alert-tooltip-top {
  // left: -70px;
  right: 30px;
  width: 90px;
  text-align: center;
  color: #fff;
  background-color: #41d8e6;
  border-radius: 5px;
  font-size: 14px;
  height: 34px;
  line-height: 34px;
  font-weight: 500;
  letter-spacing: -0.1em;
  position: absolute;

  transition: ease-in 0.4s;
}

.top {
  top: 135px;
}
.top-android {
  top: 105px;
}

.alert-tooltip-top:after {
  border-top: 0px solid transparent;
  border-left: 10px solid transparent;
  border-right: 0px solid transparent;
  border-bottom: 10px solid #41d8e6;
  content: "";
  position: absolute;
  top: -6px;
  left: 65px;
}

.alert-tooltip-right-bottom {
  width: 120px;
  text-align: center;
  color: #fff;
  background-color: #ff6600;
  border-radius: 5px;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  font-size: 14px;
  height: 34px;
  line-height: 34px;
  font-weight: 500;
  letter-spacing: -0.1em;
  position: absolute;
  top: -45px;
  left: 40px;
  transition: ease-in 0.4s;
}

.alert-tooltip-right-bottom:after {
  border-top: 8px solid #ff6600;
  border-left: 2px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 0px solid transparent;
  content: "";
  position: absolute;
  top: 34px;
  left: 60px;
}

.result {
  top: 405px;
}

.result-android {
  top: 375px;
}

.alert-result {
  right: 20px;
  z-index: 9999;
  padding: 0 10px;
  text-align: center;
  color: #fff;
  background-color: #41d8e6;
  border-radius: 5px;
  transition: ease-in 0.8s;
  font-size: 16px;
  height: 35px;
  line-height: 34px;
  font-weight: 500;
  letter-spacing: -0.1em;
  position: absolute;
}
.alert-result:after {
  border-top: 10px solid #41d8e6;
  border-left: 8px solid transparent;
  border-right: 2px solid transparent;
  border-bottom: 0px solid transparent;
  content: "";
  position: absolute;
  top: 33px;
  right: 28px;
}

.en-text {
  font-family: GilroyBold !important;
  letter-spacing: 0;
  font-weight: 400;
}
</style>
