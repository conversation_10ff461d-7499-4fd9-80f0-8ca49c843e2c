<template>
  <div>
    <fixed-header :threshold="50">
      <div class="nav-bar">
        <div class="fixed-header">
          <slot></slot>
        </div>
      </div>
    </fixed-header>
  </div>
</template>

<script>
import FixedHeader from "vue-fixed-header";

export default {
  components: {
    FixedHeader,
  },
};
</script>

<style lang="scss" scoped>
::v-deep .nav-bar.vue-fixed-header--isFixed .fixed-header {
  background-color: #c9f4f8;
  display: block !important;
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 450px;
  z-index: 999;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
}

::v-deep .nav-bar.vue-fixed-header .fixed-header {
  display: none;
}
</style>
