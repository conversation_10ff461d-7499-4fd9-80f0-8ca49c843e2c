<template>
  <div>
    <div class="bg-modal">
      <div class="alert-window">
        <!-- count idx -->
        <div class="counter-idx">
          {{ popupCurrentIndex + 1 }} / {{ popupList.length }}
        </div>
        <!-- img -->
        <div class="slider-container">
          <transition name="slide" mode="out-in">
            <img
              :key="popupCurrentIndex"
              class="img-custom"
              :src="getImageUrl(popupList[popupCurrentIndex].imgName)"
              alt="popup-image"
              loading="lazy"
              @click="handleImgClick"
            />
          </transition>
        </div>

        <div class="btn__wrapper">
          <span
            class="close-text"
            :class="isKo ? 'close-text__isKo' : 'close-text__isEn'"
            @click="handleHideTodayClick"
            >{{ $i18n.t("hide_today") }}</span
          >
          <span class="close-text" @click="handleCloseClick">{{
            $i18n.t("hide_btn")
          }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      POPUP_ROTATE_TIME: 4300, // constants
      isKo: this.$i18n.locale.includes("ko"), // boolean
      popupCurrentIndex: 0, // number
      popupList: [
        {
          imgName: "gentok",
          route_ko: "https://smartstore.naver.com/cym702/products/11367511660",
          route_en: "https://smartstore.naver.com/cym702/products/11367511660",
        },
        {
          imgName: "gs25",
          route_ko: "/mypage/notice",
          route_en: "/mypage/notice",
        },
        {
          imgName: "new-year",
          route_ko: "https://smartstore.naver.com/cym702/products/11153485917",
          route_en: "https://smartstore.naver.com/cym702/products/11153485917",
        },
      ], // Array<{ imgName: string, route_ko: string, route_en: string }>
      intervalId: null, // number | null
    };
  },
  computed: {
    currentImage() {
      const { imgName } = this.popupList[this.popupCurrentIndex];
      const lang = this.isKo ? "ko" : "en";
      const imgUrl = require(`@/assets/images/modals/${imgName}_${lang}.png`);

      return imgUrl;
    },
  },
  mounted() {
    this.startPopupListRotate();
  },
  beforeDestroy() {
    clearInterval(this.intervalId);
  },
  methods: {
    closeHandler() {
      this.$emit("closeHandler", true);
    },
    confirmHandler() {
      this.$emit("confirmHandler", true);
    },

    handleHideTodayClick() {
      const today = new Date();
      const [currentDate] = today.toISOString().split("T");

      localStorage.setItem("lastClosedDate", currentDate);

      this.$emit("closeHandler", true);
    },

    handleCloseClick() {
      this.closeHandler();
    },

    handleImgClick() {
      const message = {
        action: "goCym702",
        url: this.popupList[this.popupCurrentIndex][
          `route_${this.isKo ? "ko" : "en"}`
        ],
      };

      // 만약 외부 링크로 이동하는 경우가 아닐 때 처리하는 분기처리
      if (!message.url.includes("https://")) {
        this.$emit("closeHandler", true);
        this.$router.push(message.url);
        return;
      }

      /*global Webview*/
      /*eslint no-undef: "error"*/
      Webview.goOutLick(message);
    },

    getImageUrl(imgName) {
      const lang = this.isKo ? "ko" : "en";
      return require(`@/assets/images/modals/${imgName}_${lang}.png`);
    },

    startPopupListRotate() {
      const { POPUP_ROTATE_TIME } = this;
      const LENGTH = this.popupList.length;

      this.intervalId = setInterval(() => {
        this.popupCurrentIndex = (this.popupCurrentIndex + 1) % LENGTH;
      }, POPUP_ROTATE_TIME);
    },
  },
};
</script>

<style lang="scss" scoped>
.bg-modal {
  position: fixed;
  width: 100%;
  max-width: 450px;
  height: 100%;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: flex-end;
  padding: 0;
}

.alert-window {
  background-color: #fff;
  width: 100%;
  height: 345px;
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border-radius: 30px 30px 0 0;
}

.alert-window__content {
  font-size: 20px;
  color: #323232;
}

.btn__wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 12px 30px 0 25px;
}

.img-custom {
  max-width: 360px;
  max-height: 265px;
  /* padding: 20px 10px 0; */
  border-radius: 30px 30px 0 0;
  object-fit: cover;
}

.counter-idx {
  width: 40px;
  height: 20px;
  position: absolute;
  float: right;
  align-self: flex-end;
  margin-right: 30px;
  margin-top: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  background: #646464a5;
  color: #ffffff;
  font-family: Noto Sans KR !important;
  font-size: 12px;
  font-weight: 500;
  line-height: 17.38px;
  letter-spacing: -0.02em;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.close-text {
  font-size: 16px;
  font-weight: 400;
  line-height: 23.17px;
  letter-spacing: -0.05em;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #646464;
}

.close-text__isKo {
  font-family: Noto Sans KR !important;
}

.close-text__isEn {
  font-family: GilroyMedium !important;
}

.slider-container {
  width: 100%;
  height: 265px;
  background: #c9f4f8;
  border-radius: 30px 30px 0 0;
}
</style>
