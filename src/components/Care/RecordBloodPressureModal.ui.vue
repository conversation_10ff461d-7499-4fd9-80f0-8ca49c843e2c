<template>
  <div class="modal-background">
    <header class="modal-header">
      <div class="cancel-btn" @click="handleCloseModal">
        <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
      </div>
      <div class="modal-title">{{ $t("writing") }}</div>
      <button class="save-button" @click="handleSaveButtonClick">
        {{ $t("save") }}
      </button>
    </header>
    <section>
      <!-- 수축기 -->
      <div class="input-items">
        <span class="input-title">{{ $t("systolic") }}</span>
        <div class="input-container">
          <input
            class="input input-number"
            type="number"
            inputmode="numeric"
            placeholder="0"
            v-model="systolic"
          />
          <span class="unit-text">mmHg</span>
        </div>
      </div>
      <!-- 이완기 -->
      <div class="input-items">
        <span class="input-title">{{ $t("diastolic") }}</span>
        <div class="input-container">
          <input
            class="input input-number"
            type="number"
            inputmode="numeric"
            placeholder="0"
            v-model="diastolic"
          />
          <span class="unit-text">mmHg</span>
        </div>
      </div>
      <!-- 맥박 -->
      <div class="input-items">
        <span class="input-title">{{ $t("pulse_text") }}</span>
        <div class="input-container">
          <input
            class="input-number input"
            type="number"
            inputmode="numeric"
            placeholder="0"
            v-model="pulse"
          />
          <span class="unit-text">bpm</span>
        </div>
      </div>
      <!-- 수축기 -->
      <div class="input-items">
        <span class="input-title">{{
          $t("blood_pressure_note_text_title")
        }}</span>
        <div class="input-container container-memo">
          <textarea
            class="input input-memo"
            v-model="memo"
            @input="memoValidate"
            :placeholder="$t('blood_pressure_note_text_title')"
          />
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { bloodPressure } from "@/api/care/blood-pressure.js";
import { debounce } from "lodash";

export default {
  data() {
    return {
      systolic: null,
      diastolic: null,
      pulse: null,
      memo: null,
      isMemoValid: true,
      RESOURCE_TYPE: "manual",
    };
  },

  created() {
    this.memoValidate = debounce(this.memoValidate, 200);
  },

  methods: {
    formatDate() {
      const date = new Date();

      return date.toISOString();
    },

    getSubjectId() {
      const subjectId = localStorage.getItem("subjectId");

      return subjectId ?? "";
    },

    handleCloseModal() {
      this.$store.commit("setRecordBloodPressureData", false);
    },

    async handleSaveButtonClick() {
      const { addBloodPressureRecord } = bloodPressure;

      try {
        const payload = {
          date: this.formatDate(),
          diastolic: this.diastolic,
          systolic: this.systolic,
          pulse: this.pulse,
          mean: Math.round((this.systolic + 2 * this.diastolic) / 3),
          comment: this.memo,
          resourceType: this.RESOURCE_TYPE,
        };
        const subjectId = this.getSubjectId();

        if (!this.isValidPayload()) {
          throw Error("Systolic, diastolic, and pulse are required inputs.");
        }

        if (!this.isMemoValid) {
          throw Error("Memo must be no more than 250 characters long.");
        }

        if (!subjectId) {
          throw Error("Subject ID is required.");
        }

        await addBloodPressureRecord(subjectId, payload)
          .then((_res) => {
            console.log("success to add blood pressure record ");

            if (_res && _res.status === 201) {
              localStorage.setItem("systolic", String(this.systolic));
              localStorage.setItem("diastolic", String(this.diastolic));

              this.$emit("save-success");
            }

            this.$store.commit("setRecordBloodPressureData", false);
          })
          .catch((_err) => {
            console.error(_err);
            throw _err;
          });
      } catch (_error) {
        const status = _error?.response?.status;

        console.error(_error);

        if (status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    memoValidate() {
      const memo = this.memo;
      const length = memo?.length || 0;
      const MAX_LEN = 250;

      if (memo && length > MAX_LEN) {
        console.warn("Memo must be no more than 250 characters long.");

        this.isMemoValid = false;

        return;
      }

      this.isMemoValid = true;
    },

    isValidPayload() {
      if (
        this.systolic === null ||
        isNaN(this.systolic) ||
        this.diastolic === null ||
        isNaN(this.diastolic) ||
        this.pulse === null ||
        isNaN(this.pulse)
      ) {
        return false;
      }

      return true;
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-background {
  width: 100%;
  border-radius: 30px 30px 0px 0px;
  position: fixed;
  max-width: 450px;
  padding: 30px;
  /* right: 0; */
  bottom: 0;
  left: 50%;
  transform: translateX(round(-50%));
  z-index: 999999;
  height: 93.5%;
  background: rgba(0, 0, 0, 0.81);
  opacity: 1;
  transition: opacity 0.15s linear;
  overscroll-behavior: contain;
  animation: showEditModal 0.7s forwards;
}

@keyframes showEditModal {
  0% {
    opacity: 0;
    transform: translate(-50%, 100%);
  }
  to {
    opacity: 1;
    transform: translateZ(-50%, 0);
  }
}

.cancel-btn {
  display: flex;
  width: 20px;
  height: 20px;
  align-items: center;
  img {
    width: 100%;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 55px;
}

.modal-title {
  color: #ffffff;
  font-weight: 500;
  font-size: 20px;
  line-height: 24px;
  letter-spacing: -3%;
  text-align: center;
}

.save-button {
  width: 45px;
  height: 23px;
  font-weight: 700;
  font-size: 16px;
  line-height: 19.2px;
  letter-spacing: -3%;
  text-align: center;
  color: #ffffff;
  display: grid;
  place-content: center;
  background: #41d8e6;
  border-radius: 5px;
}

.input-container {
  width: 100%;
  height: 50px;
  border-radius: 5px;
  background-color: #ededed;
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
  padding: 0 20px;
}

.input {
  width: 100%;
  caret-color: #41d8e6;
  outline: none;
}

.input-number {
  font-family: GilroyMedium !important;
  font-weight: 500;
  font-size: 36px;
  line-height: 42.37px;
  letter-spacing: 0%;
}

.input-memo {
  font-weight: 500;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: -3%;
  vertical-align: middle;
}

.input-wrapper {
  display: flex;
  justify-content: space-between;
}

.input-title {
  font-weight: 500;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: -3%;
  text-align: center;
  vertical-align: middle;
  color: #ffffff;
}

.input-items {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 30px;
}

.unit-text {
  font-weight: 400;
  font-size: 30px;
  line-height: 36px;
  letter-spacing: -3%;
  text-align: right;
  color: #646464;
  display: flex;
  align-items: center;
}

.container-memo {
  min-height: 100px;
  padding: 14px 15px;

  & > textarea {
    resize: none;
  }
}
</style>
