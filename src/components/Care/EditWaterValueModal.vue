<template>
  <div>
    <div class="modal-background xs-mobile">
      <div class="date-picker-container">
        <div class="weight-edit-header-flex-box">
          <div class="canclebtn" @click="closeEditModalWindow">
            <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
          </div>
          <div class="modal-title">{{ title }}</div>
          <v-btn color="#41d8e6" :disabled="!isValid" class="savebtn" @click="saveBtnHandler">{{
            $t("save")
          }}</v-btn>
        </div>
        <div v-if="page !== 'ketone'" class="setting-items">
          <div class="setting-item">
            <div class="setting-item__title">
              {{ settingTitle }} {{ $t("setting_header_title") }}
            </div>
            <div class="setting-item__input">
              <div class="input">
                <input
                  type="number"
                  inputmode="numeric"
                  pattern="[0-9]*"
                  v-model="inputValue"
                  placeholder="0"
                />
              </div>
              <div class="value-txt">{{ unit }}</div>
            </div>
          </div>

          <div class="setting-water" v-if="page === 'water' && isMainUser">
            <div class="setting-item__title">{{ $t("recommended_water") }}</div>
            <div class="setting-item__btn" @click="goToHealthInfo">
              {{ $t("health_info_settings") }}
            </div>
          </div>
        </div>
        <div v-else class="setting-items">
          <div class="setting-item">
            <div class="checkbox__items">
              <div class="checkbox__item">
                <v-checkbox
                  color="#41d8e6"
                  off-icon="$check_box"
                  on-icon="$check_box_inactive"
                  v-model="notKetoneMode"
                >
                  <!-- @change="ketoneModeHandler" -->
                  <template v-slot:label>
                    <div class="text">{{ $t("no_ketosis") }}</div>
                  </template>
                </v-checkbox>
                <div class="description">{{ $t("is_diabetes") }}</div>
              </div>
              <div class="checkbox__item">
                <v-checkbox
                  color="#41d8e6"
                  off-icon="$check_box"
                  on-icon="$check_box_inactive"
                  v-model="isKetoneMode"
                >
                  <!-- @change="notKetoneModeHandler" -->
                  <template v-slot:label>
                    <div class="text">{{ $t("is_ketosis") }}</div>
                  </template>
                </v-checkbox>
                <div class="description">{{ $t("no_diabetes") }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    page: String,
    targetValue: Number,
  },
  data() {
    return {
      inputValue: 0,
      initialKetoneMode: false,
      notKetoneMode: false,
      isKetoneMode: false,
      isMainUser: true,
      // isValid: false,
    };
  },

  watch: {
    isKetoneMode(newVal) {
      // console.log(newVal);
      newVal ? (this.notKetoneMode = false) : null;
      this.$emit("ketoneModeHandler", this.isKetoneMode);
    },
    notKetoneMode(newVal) {
      // console.log(newVal);
      newVal ? (this.isKetoneMode = false) : null;
      this.$emit("ketoneModeHandler", this.isKetoneMode);
    },
    targetValue(newVal) {
      this.inputValue = newVal;
    },
  },

  computed: {
    unit() {
      if (this.page === "solution") return "kcal";
      else if (this.page === "weight") return "Kg";
      else return "ml";
    },
    title() {
      if (this.page === "solution") return this.$i18n.t("set_target_calorie");
      else if (this.page === "weight") return this.$i18n.t("set_target_weight");
      else if (this.page === "ketone") return this.$i18n.t("ketone_mode_setting");
      else return this.$i18n.t("set_target_water");
    },
    settingTitle() {
      return this.page === "weight" ? this.$i18n.t("goal") : this.$i18n.t("directly");
    },
    isValid() {
      return this.initialKetoneMode !== this.isKetoneMode || this.inputValue !== 0;
    },
  },
  methods: {
    closeEditModalWindow() {
      this.$emit("closeEditModalWindow", false);
    },
    saveBtnHandler() {
      this.page === "ketone"
        ? this.$emit("saveBtnHandler", this.isKetoneMode)
        : this.$emit("saveBtnHandler", this.inputValue);
      this.$emit("closeEditModalWindow", false);
    },
    deleteWeightData(idx) {
      const deletedData = this.weightData.filter((data, index) => {
        if (idx !== index) {
          return data;
        }
      });
      this.weightData = deletedData;
    },
    goToHealthInfo() {
      this.$router.push("/profile/survey");
    },
  },
  created() {
    const ketoneMode = JSON.parse(localStorage.getItem("ketoneMode"));
    ketoneMode
      ? ((this.isKetoneMode = true), (this.notKetoneMode = false))
      : ((this.isKetoneMode = false), (this.notKetoneMode = true));
    // console.log(this.isKetoneMode, this.notKetoneMode);
    this.initialKetoneMode = ketoneMode;
  },

  mounted() {
    // console.log(this.targetValue)
    this.inputValue = this.targetValue;
    // console.log(localStorage.getItem("ketoneMode"));
    // this.isKetoneMode = ketoneMode;
    const mainUserId = Number(localStorage.getItem("subjectId"));
    const subjects = JSON.parse(sessionStorage.getItem("subjects"));
    const selectedId = Number(sessionStorage.getItem("selectUser"));
    this.isMainUser = mainUserId === subjects[selectedId].id;
  },
};
</script>

<style lang="scss" scoped>
.modal-background {
  width: 100%;
  border-radius: 30px 30px 0px 0px;
  position: fixed;
  max-width: 450px;
  padding: 30px;
  /* right: 0; */
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999999;
  height: 93.5%;
  background: rgba(0, 0, 0, 0.81);
  opacity: 1;
  transition: opacity 0.15s linear;
  overscroll-behavior: contain;
  animation: showEditModal 0.7s forwards;
}

@keyframes showEditModal {
  0% {
    opacity: 0;
    transform: translate(-50%, 100%);
  }
  to {
    opacity: 1;
    transform: translateZ(-50%, 0);
  }
}

.weight-edit-header-flex-box {
  border-radius: 20px 20px 0px 0px;
  width: 100%;
  /* background-color: black; */
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  // padding: 30px 30px 9px 30px;
  z-index: 99;
  font-size: 20px;
}

.canclebtn {
  display: flex;
  width: 26px;
  height: 33px;
  align-items: center;
  img {
    width: 100%;
  }
}

.modal-title {
  font-size: 22px;
  font-weight: 500;
}

.savebtn {
  background-color: #41d8e6;
  color: #fff !important;
  border-radius: 5px;
  font-weight: 700;
  padding: 0 !important;
  font-size: 18px !important;
  // width: 50px;
  height: 30px !important;
  line-height: 27px;
  letter-spacing: -0.03em;
}
.setting-water {
  padding-top: 50px;
}

.setting-item {
  padding-top: 80px;
}

.setting-item__title {
  color: #fff;
  text-align: left;
  font-weight: 500;
  font-size: 20px;
}

.setting-item__input {
  width: 100%;
  height: 55px;
  background-color: #ededed;
  border-radius: 5px;
  margin-top: 15px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 0 20px 5px 20px;
}

.input {
  height: 100%;
  input {
    width: 100%;
    height: 100%;
    font-size: 38px;
    font-family: GilroyMedium;
    color: #000;
    outline: none;
    caret-color: #41d8e6;
  }
}

.value-txt {
  font-weight: 500;
  font-size: 32px;
  font-family: GilroyMedium;
  line-height: 38px;
  color: #646464;
}

.setting-item__btn {
  margin-top: 15px;
  width: 100%;
  height: 55px;
  background-color: #41d8e6;
  border-radius: 10px;
  color: #fff;
  line-height: 55px;
  font-weight: 700;
  font-size: 22px;
  letter-spacing: -0.03em;
}

.checkbox__items {
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.text {
  color: #fff;
  font-size: 20px;
  font-weight: 500;
  padding-left: 10px;
}

.checkbox__item {
  color: #fff;
}

.description {
  color: #fff;
  font-size: 18px;
  text-align: left;
  // padding-left: 33px;
}

::v-deep .v-input--selection-controls {
  margin-top: 10px;
}

::v-deep .v-input--selection-controls__input {
  padding-top: 5px;
}

::v-deep .v-input--selection-controls__input {
  margin: 0 !important;
}

::v-deep .theme--light.v-messages {
  display: none;
}

::v-deep .v-text-field .v-label {
  top: -30px !important;
  font-weight: 500;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 12px !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}

.theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}

::v-deep .v-btn.v-size--default {
  min-width: 55px !important;
}
</style>
