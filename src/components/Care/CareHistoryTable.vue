<template>
  <!--=============== weight, water_history_page history table 컴포넌트 =======================-->
  <div class="history-table__wrapper">
    <!--============== 🚨 체중, 수분 history정보 화면에 구현 =======================-->
    <div class="history-table">
      <div class="history-table-header">
        <div class="history-table-title">{{ title }} {{ $t("record") }}</div>
        <div class="button-container">
          <button
            :class="['select-btn', 'mr-3']"
            v-if="type === 'bloodPressure' && count === 'd'"
            @click="handleRecordClick"
          >
            {{ $t("writing") }}
          </button>
          <button
            v-if="type !== 'step' && count === 'd' && history.length !== 0"
            @click="selectBtnHandler"
            :class="[active, 'select-btn']"
          >
            {{ $t("btn_select") }}
          </button>
          <span v-else-if="type === 'step' && count !== 'd'">{{
            totalText
          }}</span>
        </div>
      </div>
      <!--=============== [♦︎] 수분 history정보 화면에 구현 =======================-->

      <div
        v-if="!noData && type !== 'bloodPressure'"
        class="history-table-body"
      >
        <div
          class="history-table-row"
          v-for="(item, idx) in history"
          :key="idx"
        >
          <div class="body-left">{{ item.createdAt }}</div>
          <div class="table-right">
            <div
              class="body-right"
              :class="type === 'step' && isKo && 'step-ko'"
            >
              {{ item.value.toLocaleString("ko-KR") }} {{ unit }}
            </div>
            <button
              @click="selectHandler(item.id)"
              v-if="activeDelBtn"
              class="del-btn"
            >
              {{ $t("delete_btn") }}
            </button>
          </div>
        </div>
      </div>
      <!--=============== [♦︎] 혈압 history정보 화면에 구현 =======================-->
      <div v-else-if="!noData && type === 'bloodPressure'">
        <div
          class="history-table-row blood-pressure-wrapper"
          v-for="(item, idx) in history"
          :key="idx"
        >
          <div class="body-left blood-pressure-left">
            <span>{{ item.createdAt }}</span>
            <div
              v-show="count !== 'y'"
              class="body-right"
              :class="type === 'step' && isKo && 'step-ko'"
            >
              {{ item.value }} {{ unit }}
            </div>
          </div>
          <div class="table-right blood-pressure-right">
            <button
              @click="selectHandler(item.id)"
              v-if="activeDelBtn"
              class="del-btn"
            >
              {{ $t("delete_btn") }}
            </button>
            <span v-if="count !== 'y'">{{
              bloodPressureResourceType(item.createdType)
            }}</span>
            <span v-else>{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="snackbar">
      <v-snackbar v-model="delSuccess" timeout="2000">{{
        successContent
      }}</v-snackbar>
      <v-snackbar v-model="delFail" timeout="2000" color="#EE0000">{{
        failContent
      }}</v-snackbar>
    </div>
  </div>
</template>

<script>
import API from "@/api/care/index.js";
import { getSubjectId } from "../Common/getSubjectId.js";

export default {
  props: {
    history: Array,
    type: String,
    noData: Boolean,
    count: String,
    total: Number || String,
  },
  data() {
    return {
      careType: "",
      activeDelBtn: false,
      delSuccess: false,
      successContent: this.$i18n.t("delete_success"),
      delFail: false,
      failContent: this.$i18n.t("save_fail"),
      isKo: false,
    };
  },
  computed: {
    unit() {
      const units = {
        weight: "kg",
        water: "ml",
        pee: this.$i18n.t("times"),
        step: this.$i18n.t("step"),
      };

      return units[this.type] ?? "";
    },

    title() {
      const titles = {
        weight: this.$i18n.t("weight_title"),
        water: this.$i18n.t("water_title"),
        pee: this.$i18n.t("pee_title"),
        step: this.$i18n.t("step_title"),
        bloodPressure: this.$i18n.t("bloodPressure_table_title"),
      };

      return titles[this.type];
    },
    active() {
      return this.activeDelBtn ? "active-btn " : false;
    },

    totalText() {
      const texts = {
        step: this.$i18n.t("total_step_week"),
      };
      return `${texts[this.type]}${this.type !== "w" ? this.total : ""}`;
    },
  },
  watch: {
    history(newVal) {
      // console.log(newVal);
      this.activeDelBtn = false;
    },
  },
  methods: {
    selectBtnHandler() {
      this.activeDelBtn = this.activeDelBtn ? false : true;
    },
    selectHandler(id) {
      // console.log(id);
      if (this.activeDelBtn) {
        this.deleteCareData(id);
      }
    },

    handleRecordClick() {
      this.$store.commit("setRecordBloodPressureData", true);
    },

    async deleteCareData(id) {
      const subjectId = getSubjectId();
      try {
        const body = { careId: id };
        const { status } = await API.DeleteCareData(subjectId, this.type, body);
        if (status === 204) {
          this.delSuccess = true;
          this.$emit("reloadData");
        }
      } catch (error) {
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    bloodPressureResourceType(resourceType) {
      switch (resourceType) {
        case "manual":
          return "수동 입력";
        case "samsung_health":
          return "Samsung Health";
        default:
          console.error("error: resourceType is not defined");
          return "etc.";
      }
    },
  },

  mounted() {
    this.isKo = this.$i18n.locale.includes("ko");
  },
};
</script>
<style lang="scss" scoped>
.weight-info {
  padding: 25px 0px;
  text-align: center;
  font-size: 14px;
  color: #646464;
  font-weight: 500;
}

p {
  margin: 0;
}

.history-table {
  margin-top: 10px;
  padding: 25px 30px 90px 30px;
  border-radius: 20px 20px 0px 0px;
  background-color: #fff;
}

.history-table-header {
  display: flex;
  justify-content: space-between;
  border-bottom: 0.5px solid #a7a7a7;
  padding-bottom: 10px;
}

.history-table-row {
  display: flex;
  justify-content: space-between;
  align-content: center;
  padding: 10px 0px;
}

.history-table-title {
  font-weight: 700;
  font-size: 16px;
}

.select-btn {
  border-radius: 5px;
  border: 1px solid #ededed;
  padding: 0 10px;
  white-space: nowrap;
  color: #858585;
  min-width: 60px;
  width: auto;
  letter-spacing: -0.03em;
  display: flex;
  justify-content: center;
  font-weight: 500;
}

.active-btn {
  background-color: #41d8e6;
  border: 1px solid #41d8e6;
  color: #fff;
}
.del-btn {
  width: fit-content;
  border-radius: 5px;
  border: 1px solid #ededed;
  padding: 0 10px;
  white-space: nowrap;
  color: #858585;
  &:active {
    background-color: #a7a7a7;
    border: 1px solid #a7a7a7;
    color: #fff;
  }
}

.body-left {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #a7a7a7;
  font-weight: normal;
  font-family: GilroyMedium !important;
}

.table-right {
  display: flex;
  gap: 10px;
}

.body-right {
  display: flex;
  align-items: center;
  font-size: 16px;
  line-height: 20px;
  font-weight: 400;
  font-family: GilroyMedium !important;
  color: #000;
}

.strong {
  color: #41d8e6;
}

.noData {
  text-align: left;
  padding: 0 30px;
}

.healthkit-btn__wrapper {
  text-align: right;
  letter-spacing: -0.05em;
  color: #a7a7a7;
  font-weight: 400;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.switch-label {
  padding-bottom: 10px;
  padding-right: 10px;
}

::v-deep .v-input--selection-controls__input {
  margin: 0px !important;
}

::v-deep .v-input--selection-controls {
  margin: 0px !important;
  padding: 0px !important;
}

::v-deep .v-messages {
  display: none;
}

.step-ko {
  font-family: Noto Sans KR !important;
}

.button-container {
  display: flex;
}

.blood-pressure-wrapper {
  align-items: flex-end;
}

.blood-pressure-left {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
}

.blood-pressure-right {
  height: fit-content;
  display: flex;
  align-items: flex-end;
  flex-direction: column;
}
</style>
