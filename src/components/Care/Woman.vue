<template>
  <div>
    <AlertModal
      v-if="showAlertModal"
      @nextHandler="surveyHandler"
      :btnText="goToSurvey"
      :error="error"
      @closeBtnHandler="closeBtnHandler"
    />
    <Header :type="type" />
    <div class="white-background">
      <!-- @dateStatus="dateStatus" -->
      <!-- :onPeriod="onPeriod"
      :offPeriod="offPeriod" -->
      <Calendar @activeDate="activeDate" :calendarAttributes="calendarAttributes" />
    </div>
    <WomanCareTable
      :selectedDate="selectedDate"
      :selectedDateStatus="selectedDateStatus"
      @periodOnHandler="periodOnHandler"
      @periodOffHandler="periodOffHandler"
    />
    <div class="snackbar">
      <v-snackbar v-model="isSuccess" timeout="2000">{{ successContent }}</v-snackbar>
      <v-snackbar v-model="isFailed" timeout="2000" color="#EE0000">{{ failContent }}</v-snackbar>
    </div>
    <navigation :path="path"></navigation>
  </div>
</template>

<script>
import Calendar from "@/components/Common/Calendar.vue";
import WomanCareTable from "@/components/Care/WomanCareTable.vue";
import Header from "@/components/Care/Header.vue";
import AlertModal from "@/components/Common/AlertModal.vue";

import { formatDate } from "@/components/Common/dateFunctions.js";
import { getSubjectId } from "@/components/Common/getSubjectId.js";
import API from "@/api/care/index.js";

export default {
  components: {
    Header,
    Calendar,
    WomanCareTable,
    AlertModal,
  },
  data() {
    return {
      path: "/home",
      type: "woman",
      page: "woman",
      selectedDate: null,
      selectedDateStatus: {},
      onPeriod: false,
      offPeriod: false,
      periodColor: "#FFB5B5",
      cymColor: "#41D8E6",
      fertileWindowColor: "#C9F4F8",
      todayColor: "#D9D9D9",
      periodArr: [],
      fertileWindow: { key: "fertile_window", startDate: null, endDate: null },
      selectedDates: {},
      ovulationDay: null,
      nextMenstruation: { key: "period", startDate: null, endDate: null },
      isPeriod: false,
      isSuccess: false,
      isFailed: false,
      successContent: this.$i18n.t("save_success"),
      failContent: this.$i18n.t("save_fail"),
      error: this.$i18n.t("need_survey_message"),
      goToSurvey: this.$i18n.t("go_to_survey"),
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },

  computed: {
    calendarAttributes() {
      const todayHighlight = {
        key: "today",
        highlight: {
          style: { background: this.todayColor, zIndex: 9 },
        },
        dates: new Date(),
      };

      const fertileWindowHighlight = this.getFertileWindowDots();

      const nextMenstruationDots = {
        key: "nextMenstruation",
        dot: {
          style: {
            background: this.periodColor,
          },
        },
        dates: { start: this.nextMenstruation.startDate, end: this.nextMenstruation.endDate },
      };

      if (!this.periodArr || this.periodArr.length === 0) {
        return [todayHighlight];
      } else {
        return [
          todayHighlight,
          ...fertileWindowHighlight,
          nextMenstruationDots,
          ...this.periodArr.map((range) => {
            const startDate = new Date(range.startDate);
            const endDate = new Date(range.endDate);

            return {
              key: "highlighted-" + range.id,
              highlight: {
                style: {
                  background: this.periodColor,
                },
              },
              dates:
                startDate.getTime() === endDate.getTime()
                  ? range.startDate
                  : { start: range.startDate, end: range.endDate },
              customData: { id: range.id },
            };
          }),
        ];
      }
    },
    showAlertModal() {
      return this.$store.state.surveyModalOpen;
    },
  },

  methods: {
    activeDate(fromChild) {
      // console.log("??", fromChild);
      this.selectedDate = fromChild;
      this.setDateStatus();
    },
    setDateStatus() {
      const isPeriod = this.checkDateInHighlightedRanges(this.selectedDate, this.periodArr);
      const isEnded = this.isSelectedDateEqualToEndDate(this.selectedDate, this.periodArr);
      this.isPeriod = isPeriod !== null;
      const [nextStatus, remain] = this.findNearestFutureDate(
        this.selectedDate,
        this.fertileWindow,
        this.nextMenstruation
      );
      // console.log("here", nextStatus, remain);
      const dateStatus = {
        isPeriod: isPeriod,
        isFertileWindow: this.checkDateInHighlightedRanges(this.selectedDate, [this.fertileWindow]),
        isEnded: isEnded,
        fertileWindowDate: this.fertileWindow.startDate,
        nextStatus,
        remain,
      };
      this.selectedDateStatus = dateStatus;
    },

    periodOnHandler(fromChild) {
      const endDate = new Date(this.selectedDate);
      endDate.setDate(endDate.getDate() + 4);
      this.selectedDates.startDate = formatDate(this.selectedDate);
      this.selectedDates.endDate = formatDate(endDate);
      fromChild ? this.requestSaveDate() : this.onDateSelected();
      // if (fromChild && !this.isPeriod) this.requestSaveDate();
      // else if (!fromChild && this.isPeriod) this.onDateSelected();
    },
    periodOffHandler(fromChild) {
      fromChild ? this.onEndButtonClick() : null;
    },

    onDateSelected() {
      this.selectedRangeId = null;
      const selectedRange = this.periodArr.find((range) => {
        const startDate = new Date(range.startDate);
        const endDate = new Date(range.endDate);

        // 년-월-일까지만 비교하기 위해 시간 정보를 제거
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999); // 범위의 끝까지 포함

        const selected = new Date(this.selectedDate);
        selected.setHours(0, 0, 0, 0);

        return selected >= startDate && selected <= endDate;
      });

      if (selectedRange) {
        this.selectedRangeId = selectedRange.id;
        this.requestDeleteDate();
      }
    },

    checkDateInHighlightedRanges(selectedDate, highlightedRanges) {
      // 선택된 날짜를 Date 객체로 변환
      const selectedDateObj = new Date(selectedDate);
      selectedDateObj.setHours(0, 0, 0, 0); // 시간을 0으로 설정하여 날짜만 비교

      // highlightedRanges를 순회하며 선택된 날짜가 포함되는지 확인
      for (const range of Object.values(highlightedRanges)) {
        const startDate = new Date(range.startDate);
        const endDate = new Date(range.endDate);
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(0, 0, 0, 0);

        // 선택된 날짜가 하이라이트된 기간 내에 있는지 확인
        if (selectedDateObj >= startDate && selectedDateObj <= endDate) {
          // 시작일로부터 선택된 날짜까지 몇 일이 지났는지 계산
          const daysPassed = (selectedDateObj - startDate) / (1000 * 60 * 60 * 24);
          return daysPassed; // 해당 날짜가 기간 내에 있다면 지난 일수 반환
        }
      }

      // 선택된 날짜가 어떤 하이라이트된 기간에도 속하지 않는 경우
      return null;
    },

    isSelectedDateEqualToEndDate(selectedDate, highlightedRanges) {
      const selected = new Date(selectedDate);
      // 선택된 날짜의 시간을 제거하여, 년-월-일만 비교하도록 설정
      selected.setHours(0, 0, 0, 0);

      // highlightedRanges 배열을 순회하며 endDate와 선택된 날짜를 비교
      const isEndDate = highlightedRanges.some((range) => {
        const endDate = new Date(range.endDate);
        // endDate의 시간을 제거하여, 년-월-일만 비교하도록 설정
        endDate.setHours(0, 0, 0, 0);

        return selected.getTime() === endDate.getTime();
      });

      return isEndDate;
    },

    onEndButtonClick() {
      // console.log("button clicked");
      if (!this.selectedDate) {
        this.failContent = this.$i18n.t("no_date_selected");
        this.isFailed = true;
        return;
      }

      const closestPastHighlight = this.findClosestPastHighlight(this.selectedDate);
      // console.log(closestPastHighlight);
      if (closestPastHighlight) {
        this.requestUpdateDate(closestPastHighlight.id, this.selectedDate);
      }
    },

    findClosestPastHighlight(selectedDate) {
      const selectedDateObj = new Date(selectedDate);
      selectedDateObj.setHours(0, 0, 0, 0);

      const pastHighlights = this.periodArr.filter((range) => {
        const startDateObj = new Date(range.startDate);
        startDateObj.setHours(0, 0, 0, 0);
        return startDateObj <= selectedDateObj;
      });

      if (pastHighlights.length === 0) {
        return null;
      }

      // 가장 가까운 과거의 하이라이트를 찾기 위해 정렬
      pastHighlights.sort((a, b) => {
        const endDateA = new Date(a.endDate);
        endDateA.setHours(0, 0, 0, 0);
        const endDateB = new Date(b.endDate);
        endDateB.setHours(0, 0, 0, 0);
        return endDateB - endDateA;
      });

      // console.log("sortedArr", pastHighlights[0]);
      return pastHighlights[0];
    },

    getFertileWindowDots() {
      // 가임기 + 배란일 색 다르게 dot object 추가
      // this.highlightedRanges.forEach((range) => {
      const startDate = new Date(this.fertileWindow.startDate);
      const endDate = new Date(this.fertileWindow.endDate);

      let currentDate = new Date(startDate);

      let dayCounter = 0;
      const dotsArr = [];

      while (currentDate <= endDate) {
        let color = this.fertileWindowColor;

        if (dayCounter === 4) {
          // 0번째 인덱스부터 시작
          color = this.cymColor;
        }

        dotsArr.push({
          dates: new Date(currentDate),
          dot: {
            style: { background: color },
          },
        });

        currentDate.setDate(currentDate.getDate() + 1);
        dayCounter += 1;
      }
      return dotsArr;
    },

    findNearestFutureDate(selectedDate, obj1, obj2) {
      // console.log(selectedDate);
      if (this.selectedDate !== null) {
        const selected = new Date(selectedDate);
        const todayFormatted = new Date(
          selected.getFullYear(),
          selected.getMonth(),
          selected.getDate()
        );

        let nearestDateInfo = { key: "", daysRemaining: Infinity };

        [obj1, obj2].forEach((obj) => {
          const startDate = new Date(obj.startDate);
          const startDateFormatted = new Date(
            startDate.getFullYear(),
            startDate.getMonth(),
            startDate.getDate()
          );

          const timeDiff = startDateFormatted - todayFormatted;
          const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

          if (daysDiff > 0 && daysDiff < nearestDateInfo.daysRemaining) {
            nearestDateInfo.daysRemaining = daysDiff;
            nearestDateInfo.key = obj.key;
          }
        });

        // 남은 일수를 정수로 반올림
        nearestDateInfo.daysRemaining = Math.ceil(nearestDateInfo.daysRemaining);

        // nearestDateInfo에 저장된 key와 daysRemaining을 배열로 반환
        // 단, daysRemaining가 Infinity인 경우, 즉 미래의 날짜가 없는 경우 빈 배열 반환
        return nearestDateInfo.daysRemaining !== Infinity
          ? [nearestDateInfo.key, nearestDateInfo.daysRemaining]
          : [];
      } else return [];
    },

    async fetchSavedDate() {
      const subjectId = getSubjectId();
      this.periodArr = [];
      try {
        const { data, status } = await API.GetPeriodData(subjectId);
        // console.log("period data:", data);
        if (status === 200) {
          if (data.result.length === 0) {
            // console.log("data empty");
          } else {
            data.result.map((range) => {
              const startDate = new Date(range.startDate);
              const endDate = new Date(range.endDate);
              this.periodArr.push({ startDate, endDate, id: range.id, key: "period" });
            });
            this.ovulationDay = new Date(data.ovulationDay);
            this.fertileWindow.startDate = data.fertileWindow.startDate;
            this.fertileWindow.endDate = data.fertileWindow.endDate;
            this.nextMenstruation.startDate = data.nextMenstruation.startDate;
            this.nextMenstruation.endDate = data.nextMenstruation.endDate;
            this.setDateStatus();
            // console.log("=============", this.fertileWindow, this.nextMenstruation);}
          }
        }
      } catch (err) {
        console.log(err);
      }
    },

    async requestSaveDate() {
      const subjectId = getSubjectId();
      // const selectedDates = this.selectedDates;
      try {
        const { status } = await API.PostPeriodData(subjectId, this.selectedDates);
        if (status === 201) {
          this.successContent = this.$i18n.t("save_success");
          this.isSuccess = true;
          this.fetchSavedDate();
          // this.$emit("reloadData");
        }
      } catch (err) {
        this.failContent = this.$i18n.t("save_fail");
        this.isFailed = true;
        console.log(err);
      }
    },

    async requestUpdateDate(id, newEndDate) {
      // console.log(id, newEndDate);
      const subjectId = getSubjectId();
      const body = { endDate: formatDate(newEndDate) };
      // console.log(body);
      try {
        const { status } = await API.PatchPeriodData(subjectId, id, body);
        if (status === 200) {
          this.successContent = this.$i18n.t("save_success");
          this.isSuccess = true;
          this.fetchSavedDate();
          // this.$emit("reloadData");
        }
      } catch (error) {
        this.failContent = this.$i18n.t("save_fail");
        this.isFailed = true;
      }
    },

    async requestDeleteDate() {
      const subjectId = getSubjectId();
      try {
        const { status } = await API.DeletePeriodData(subjectId, this.selectedRangeId);
        // console.log(status);
        if (status === 204) {
          this.successContent = this.$i18n.t("delete_success");
          this.isSuccess = true;
          this.fetchSavedDate();
        }
      } catch (err) {
        console.log(err);
        this.failContent = this.$i18n.t("delete_fail");
        this.isFailed = true;
      }
    },

    closeBtnHandler(fromChild) {
      // console.log(fromChild);
      this.$store.commit("setSurveyModal", false);
    },
    surveyHandler(fromChild) {
      // console.log(fromChild);
      this.$router.push("/survey");
    },
  },

  mounted() {
    this.calendarAttributes.push(this.today);
    this.selectedDate = new Date();
    this.fetchSavedDate();
  },
};
</script>

<style lang="scss" scoped></style>
