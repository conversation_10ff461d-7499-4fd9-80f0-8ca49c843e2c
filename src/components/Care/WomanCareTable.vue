<template>
  <div :class="['record__wrapper', { opacity: isFutureDate }, { en: !isKo }]">
    <div class="record-title__wrapper">
      <div class="record-title">{{ formattedDate }}</div>
      <div class="status-text">{{ displayMessage }}</div>
    </div>
    <div class="record-table">
      <div v-if="days === 0 || days === null" class="period-record-card">
        <div class="left-section">
          <img src="@/assets/images/care/period_on.png" />{{
            $t("period_start")
          }}
        </div>
        <div class="right-section">
          <div v-if="isFutureDate" class="disabled-text">
            {{ $t("not_possible_enter") }}
          </div>
          <div class="switch__wrapper">
            <v-switch
              color="#41d8e6"
              height="20px"
              inset
              v-model="isStarted"
              @change="periodOnHandler"
              :disabled="periodStarted || isFutureDate"
            ></v-switch>
          </div>
        </div>
      </div>

      <div v-else class="period-record-card">
        <div class="left-section">
          <img src="@/assets/images/care/period_ing.png" />{{ $t("in_period") }}
        </div>
        <div class="right-section">
          <div class="switch__wrapper">
            <v-switch
              color="#41d8e6"
              height="20px"
              inset
              v-model="isPeriod"
              disabled
            >
            </v-switch>
          </div>
        </div>
      </div>

      <div class="period-record-card">
        <div class="left-section">
          <img src="@/assets/images/care/period_off.png" />{{
            $t("period_end")
          }}
        </div>
        <div class="right-section">
          <div v-if="isFutureDate" class="disabled-text">
            {{ $t("not_possible_enter") }}
          </div>
          <div class="switch__wrapper">
            <v-switch
              color="#41d8e6"
              height="20px"
              inset
              v-model="isEnded"
              @change="periodOffHandler"
              :disabled="isFutureDate"
            >
            </v-switch>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { isFutureDate } from "../Common/dateFunctions.js";

export default {
  props: {
    selectedDate: Date,
    selectedDateStatus: Object,
  },

  data() {
    return {
      isStarted: false,
      periodStarted: false,
      isFutureDate: false,
      isEnded: false,
      isPeriod: false,
      isFertileWindow: false,
      formattedDate: null,
      days: null,
      nextStatus: "",
      remain: null,
      fertileWindow: null,
      isKo: true,
    };
  },

  computed: {
    displayMessage() {
      // During period: "생리 3일째" or "Period day 3"
      if (this.isPeriod) {
        return this.isKo
          ? `${this.$i18n.t("period")} ${this.days + 1}${this.$i18n.t(
              "in_day"
            )}`
          : `${this.$i18n.t("period")} ${this.$i18n.t("in_day")} ${
              this.days + 1
            }`;
      }

      // Before next status: "배란일 5일전" or "Ovulation day 5 days ago"
      else if (
        !this.isPeriod &&
        !this.isFertileWindow &&
        this.remain !== null &&
        this.remain < 13
      ) {
        return this.isKo
          ? `${this.$i18n.t(this.nextStatus)} ${this.remain}${this.$i18n.t(
              "days_ago"
            )}`
          : `${this.$i18n.t(this.nextStatus)} ${this.remain} ${this.$i18n.t(
              "days_ago"
            )}`;
      }

      // Fertile window: "가임기 2일째" or "Fertile window day 2"
      else if (this.isFertileWindow && this.fertileWindow !== 4) {
        return this.isKo
          ? `${this.$i18n.t("fertile_window")} ${
              this.fertileWindow + 1
            }${this.$i18n.t("in_day")}`
          : `${this.$i18n.t("fertile_window")} ${this.$i18n.t("in_day")} ${
              this.fertileWindow + 1
            }`;
      }

      // Ovulation day: "배란일"
      else if (this.isFertileWindow && this.fertileWindow === 4) {
        return `${this.$i18n.t("ovulation_day")}`;
      }

      return null;
    },
  },

  watch: {
    selectedDate(newVal) {
      if (newVal !== null) {
        this.isFutureDate = isFutureDate(newVal);
        this.formattedDate = this.formatDate(newVal);
      } else {
        this.isFutureDate = false;
        this.formattedDate = null;
      }
    },
    selectedDateStatus(newVal) {
      this.nextStatus = newVal.nextStatus;
      this.remain = newVal.remain;
      this.isEnded = newVal.isEnded;
      if (newVal.isPeriod === null) {
        this.isPeriod = false;
        this.isEnded = false;
        this.days = null;
        this.periodStarted = false;
        this.isStarted = false;
      } else {
        this.isPeriod = true;
        this.days = newVal.isPeriod;
        this.periodStarted = newVal.isPeriod > 0 ? true : false;
        this.isStarted = true;
      }
      this.isFertileWindow = newVal.isFertileWindow !== null;
      this.fertileWindow = newVal.isFertileWindow;
    },
  },

  methods: {
    periodOnHandler() {
      this.$emit("periodOnHandler", this.isStarted);
    },
    periodOffHandler() {
      this.$emit("periodOffHandler", this.isEnded);
    },
    formatDate(date) {
      if (!date) return "";

      const locale = this.isKo ? "ko-KR" : "en-US"; // this.isKo 값에 따라 로케일 결정
      const options = { weekday: "short" }; // 요일을 짧은 형식으로 가져오기
      const dayOfWeek = new Intl.DateTimeFormat(locale, options).format(
        new Date(date)
      );

      const month = String(new Date(date).getMonth() + 1);
      const day = String(new Date(date).getDate());

      return `${month}${this.$i18n.t("month")}${day}${this.$i18n.t(
        "day"
      )}(${dayOfWeek})`;
    },
  },

  mounted() {
    this.formattedDate = this.formatDate(new Date());
    this.isKo = this.$i18n.locale === "ko";
  },
};
</script>

<style lang="scss" scoped>
.record__wrapper {
  margin-top: 20px;
  padding: 25px 30px 90px 30px;
  border-radius: 20px 20px 0px 0px;
  background-color: #fff;
}

.opacity {
  color: #dadada;
  opacity: 0.95;
}

.record-title__wrapper {
  // font-family: Noto Sans KR;
  display: flex;
  justify-content: space-between;
  border-bottom: 0.5px solid #a7a7a7;
  padding-bottom: 10px;
}

.record-title {
  font-weight: 700;
  font-size: 16px;
  display: flex;
  color: #000000;
}

.status-text {
  color: #646464;
  font-size: 16px;
  font-weight: 700;
}

.record-table {
  display: flex;
  flex-direction: column;
  padding-top: 3px;
}

.period-record-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 45px;
}

.left-section {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  gap: 10px;
  img {
    width: 20px;
    object-fit: contain;
  }
}

.right-section {
  display: flex;
  gap: 15px;
  justify-content: center;
  align-items: center;
}

.disabled-text {
  font-size: 16px;
}

.switch__wrapper {
  display: flex;
  width: 40px;
  align-items: center;
  justify-content: flex-end;
  padding-bottom: 5px;
}

::v-deep .v-input--selection-controls__input {
  width: 36px !important;
}

::v-deep .theme--light.v-input--switch .v-input--switch__track {
  color: #dadada;
}

::v-deep .v-input--switch__track {
  opacity: 1 !important;
  width: 45px !important;
  height: 25px !important;
}

::v-deep .v-input--switch__thumb {
  color: #ffffff !important;
  width: 17px !important;
  height: 17px !important;
}

::v-deep .v-messages {
  min-height: 5px;
}

::v-deep .theme--light.v-input--is-disabled {
  opacity: 0.4;
}
</style>
