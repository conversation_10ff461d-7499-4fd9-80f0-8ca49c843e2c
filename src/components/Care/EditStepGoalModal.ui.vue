<template>
  <div class="modal-background">
    <header class="modal-header">
      <div class="cancel-btn" @click="handleCloseEditModalWindow">
        <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
      </div>
      <div class="modal-title">
        {{ title }}
      </div>
      <button class="save-button" @click="handleTargetStepChangeClick">
        {{ $t("save") }}
      </button>
    </header>
    <section>
      <div class="input-container">
        <label class="input-label">{{ $t("setting_step_goal_label") }}</label>
        <div class="input-wrapper">
          <input
            class="input"
            type="number"
            inputmode="numeric"
            pattern="[0-9]*"
            v-model.number="stepGoalState"
            placeholder="0"
            :max="MAX_STEP_GOAL"
          />
          <span class="unit">걸음</span>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { updateSubjectInfo } from "@/api/user";

export default {
  props: {
    title: String,
    currentTargetSteps: Number,
    stepGoal: Number,
  },

  data: () => ({
    stepGoalState: 0,
    MAX_STEP_GOAL: 100000,
  }),

  methods: {
    getSubjectId() {
      const subjectId = localStorage.getItem("subjectId");

      return subjectId;
    },
    limitStepGoal() {
      if (!this.stepGoalState || this.stepGoalState < 0) {
        this.stepGoalState = 0;
      }

      console.log(this.stepGoalState, this.stepGoal);
      if (this.stepGoalState > this.MAX_STEP_GOAL) {
        this.stepGoalState = this.MAX_STEP_GOAL;
      }
    },

    async handleTargetStepChangeClick() {
      const subjectId = this.getSubjectId();
      const reqBody = {
        targetStep: this.stepGoalState,
      };

      await updateSubjectInfo(subjectId, reqBody).then((_res) => {
        this.handleCloseEditModalWindow();
      });
    },

    handleCloseEditModalWindow() {
      this.$emit("handleCloseEditModalWindow", false);
    },
  },

  mounted() {
    this.stepGoalState = this.stepGoal;
  },
};
</script>

<style lang="scss" scoped>
.modal-background {
  width: 100%;
  border-radius: 30px 30px 0px 0px;
  position: fixed;
  max-width: 450px;
  padding: 30px;
  /* right: 0; */
  bottom: 0;
  left: 50%;
  transform: translateX(round(-50%));
  z-index: 999999;
  height: 93.5%;
  background: rgba(0, 0, 0, 0.81);
  opacity: 1;
  transition: opacity 0.15s linear;
  overscroll-behavior: contain;
  animation: showEditModal 0.7s forwards;
}

@keyframes showEditModal {
  0% {
    opacity: 0;
    transform: translate(-50%, 100%);
  }
  to {
    opacity: 1;
    transform: translateZ(-50%, 0);
  }
}

.cancel-btn {
  display: flex;
  width: 20px;
  height: 20px;
  align-items: center;
  img {
    width: 100%;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 80px;
}

.modal-title {
  color: #ffffff;
  font-weight: 500;
  font-size: 20px;
  line-height: 24px;
  letter-spacing: -3%;
  text-align: center;
}

.save-button {
  width: 45px;
  height: 23px;
  font-weight: 700;
  font-size: 16px;
  line-height: 19.2px;
  letter-spacing: -3%;
  text-align: center;
  color: #ffffff;
  display: grid;
  place-content: center;
  background: #41d8e6;
  border-radius: 5px;
}

.input-label {
  font-weight: 500;
  font-size: 18px;
  line-height: 21.6px;
  letter-spacing: -3%;
  margin-bottom: 15px;
  color: #ffffff;
}

.input-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.input-wrapper {
  width: 100%;
  padding: 8px 20px;
  background: #ededed;
  border-radius: 5px;
  display: flex;
  justify-content: space-between;
}

.input {
  width: calc(100% - 55px);
  caret-color: #41d8e6;
  font-family: GilroyMedium !important;
  font-weight: 500;
  font-size: 36px;
  line-height: 42.37px;
  letter-spacing: 0%;
  outline: none;
}

.unit {
  font-weight: 400;
  font-size: 30px;
  line-height: 36px;
  letter-spacing: -3%;
  text-align: right;
  color: #646464;
}
</style>
