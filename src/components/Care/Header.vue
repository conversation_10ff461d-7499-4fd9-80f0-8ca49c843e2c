<template>
  <div>
    <fixed-header :threshold="50">
      <div class="nav-bar">
        <div
          class="fixed-header"
          :class="isIos ? 'nav-space' : 'nav-space-android'"
        >
          <div>
            <div class="history-header_nav">
              <router-link to="/home"
                ><v-icon>$back_btn_bold</v-icon></router-link
              >
            </div>
          </div>
        </div>
      </div>
    </fixed-header>
    <div class="care-header">
      <div
        :class="isIos ? 'care-header__wrapper' : 'care-header__wrapper-android'"
      >
        <div class="care-header_nav">
          <router-link to="/home"><v-icon>$back_btn_bold</v-icon></router-link>
        </div>
        <div class="care-header_title">
          <span
            class="care-header__title--ko"
            :class="lang === 'ko' ? '' : 'en-title'"
            >{{ setTitle }}</span
          >
          <span class="care-header__title--en"> Care</span>
        </div>
        <div class="care-header_subtitle">
          <div>
            <div v-if="noData">
              <span class="weight-data">{{ $t("nodata") }}</span>
              <span class="care-header_subtitle--date">{{ date }}</span>
            </div>

            <div class="tag-container" v-else-if="type === 'woman'">
              <div class="tag period">{{ $t("period") }}</div>
              <div class="tag fertile-window">{{ $t("fertile_window") }}</div>
              <div class="tag ovulation-day">{{ $t("ovulation_day") }}</div>
            </div>

            <div v-else>
              <span
                v-if="count === 'd' && type !== 'bloodPressure'"
                class="weight-data"
                >{{ setSubComment }} {{ avgScore.toLocaleString("ko-KR") }}
                {{ unit }}</span
              >
              <span
                v-else-if="count !== 'd' && type === 'water'"
                class="weight-data"
              >
                {{ setSubTitle }} {{ avgScore.toLocaleString("ko-KR")
                }}{{ unit }}
              </span>
              <span
                v-else-if="count === 'd' && type === 'bloodPressure'"
                class="weight-data"
                >{{ setSubComment }} {{ setBloodPressureAvg }} 미만</span
              >
              <span
                v-else-if="count !== 'd' && type === 'bloodPressure'"
                class="weight-data"
                >{{ setSubTitle }} {{ setBloodPressureAvg }}
                <span class="blood-unit">{{ unit }}</span></span
              >
              <span v-else class="weight-data"
                >{{ setSubTitle }} {{ avgScore }} {{ unit }}</span
              >
              <span
                v-if="type !== 'step' && type !== 'bloodPressure'"
                class="care-header_subtitle--date"
                >{{ date }}</span
              >
            </div>
          </div>
          <div>
            <span
              v-if="
                (type === 'step' || type === 'bloodPressure') && count === 'd'
              "
              class="mr-2 care-header_subtitle--date"
              >{{ date }}</span
            >
            <v-icon
              @click="openEditModalWindow"
              v-if="
                (type === 'water' ||
                  type === 'weight' ||
                  type === 'step' ||
                  type === 'bloodPressure') &&
                count === 'd'
              "
              >$setting_btn</v-icon
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FixedHeader from "vue-fixed-header";

export default {
  props: {
    type: String,
    count: String,
    value: Number,
    avgScore: Number,
    bloodPressure: {
      type: String,
      default: "",
    },
    date: String,
    noData: Boolean,
  },
  components: { FixedHeader },
  data() {
    return {
      korTitle: "",
      subtitle: "",
      todayDate: "",
      lang: "",
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
  computed: {
    setSubTitle() {
      if (this.count === "w") return this.$i18n.t("avg_week_header");
      if (this.count === "m") return this.$i18n.t("avg_month_header");
      if (this.count === "y") return this.$i18n.t("avg_year_header");
      return "";
    },
    setSubComment() {
      const obj = {
        weight: this.$i18n.t("recent_login"),
        water: this.$i18n.t("target_water_value"),
        pee: this.$i18n.t("today_pee_count"),
        step: this.$i18n.t("today_step_goal"),
        bloodPressure: this.$i18n.t("goal"),
      };
      return obj[this.type] ?? "";
    },
    setTitle() {
      return (
        {
          weight: this.$i18n.t("weight_title"),
          water: this.$i18n.t("water_title"),
          pee: this.$i18n.t("pee_title"),
          woman: this.$i18n.t("woman_title"),
          step: this.$i18n.t("step_title").slice(0, 4),
          bloodPressure: this.$i18n.t("bloodPressure_title"),
        }[this.type] ?? ""
      );
    },
    unit() {
      if (this.type === "weight") return "kg";
      if (this.type === "water") return this.count === "d" ? "L" : "ml";
      if (this.type === "pee") return this.$i18n.t("times");
      if (this.type === "step") {
        return `${this.$i18n.t("step_title")}/일`;
      }
      return "";
    },
    setBloodPressureAvg() {
      if (!this.bloodPressure) return "";

      const [systolic, diastolic] = this.bloodPressure.split("/");

      if (this.count === "d") {
        return `${Number(systolic)}/${Number(diastolic)}`;
      }

      return `${Number(systolic)} / ${Number(diastolic)} mmHg`;
    },
  },
  methods: {
    openEditModalWindow() {
      this.$emit("openEditModalWindow");
    },
  },

  mounted() {
    this.lang = this.$i18n.locale === "ko" ? "ko" : "en";
    console.log(this.avgScore);
    switch (this.type) {
      case "water":
        return (this.korTitle = this.$i18n.t("water_title"));
      case "weight":
        return (this.korTitle = this.$i18n.t("weight_title"));
      case "pee":
        return (this.korTitle = this.$i18n.t("pee_title"));
      default:
        return;
    }
  },
};
</script>

<style lang="scss" scoped>
.nav-bar.vue-fixed-header--isFixed .fixed-header {
  background-color: #c9f4f8;
  display: block !important;
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 450px;
  z-index: 999;
}

.nav-bar.vue-fixed-header .fixed-header {
  display: none;
}

.nav-space {
  padding: 55px 0 15px 10px;
}
.nav-space-android {
  padding: 20px 0 15px 10px;
}

.history-header_nav {
  display: flex;
  justify-content: flex-start;
  padding-left: 20px;
}

.care-header__wrapper {
  padding-top: 65px;
  padding-left: 30px;
}
.care-header__wrapper-android {
  padding-top: 30px;
  padding-left: 30px;
}

.care-header_nav {
  text-align: left;
}

.care-header_title {
  text-align: left;
  font-size: 28px;
  padding-top: 10px;
  line-height: 30px;
}

.care-header_subtitle {
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  padding-top: 22px;
  padding-bottom: 10px;
  letter-spacing: -0.03em;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 25px;
}

.care-header__title--ko {
  line-height: 30px;
  font-weight: 700;
  font-size: 30px;
  margin-bottom: 5px;
}

.care-header__title--en {
  line-height: 30px;
  font-family: GilroyBold;
  font-size: 36px;
}

.weight-data {
  font-size: 22px;
  font-weight: 500;
}

.care-header_subtitle--date {
  font-size: 14px;
  font-family: GilroyMedium;
  font-weight: 500;
  padding-left: 10px;
  color: #646464;
  letter-spacing: 0.01em;
}

.tag-container {
  display: flex;
  gap: 10px;
}

.tag {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  white-space: nowrap;
  min-width: 68px;
  height: 28px;
  background-color: white;
  border-radius: 20px;
  font-size: 16px;
  font-weight: 400 !important;
}

.period {
  background-color: #ffb5b5;
}
.fertile-window {
  background-color: #c9f4f8;
  border: 1px solid #ffffff;
}
.ovulation-day {
  background-color: #41d8e6;
  color: #ffffff;
}

.blood-unit {
  font-size: 14px;
}
</style>
