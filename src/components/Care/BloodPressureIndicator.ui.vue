<template>
  <div class="container">
    <div class="blood_pressure-item">
      <div class="item-title">
        <div class="status-systolic status" />
        <h3>{{ $t("systolic") }}</h3>
      </div>
      <div class="blood_pressure-noun">
        {{ formattedBloodPressure["systolic"] }}
      </div>
      <span class="pressure-text">mmHg</span>
    </div>
    <div class="blood_pressure-item">
      <div class="item-title">
        <div class="status-diastolic status" />
        <h3>{{ $t("diastolic") }}</h3>
      </div>
      <div class="blood_pressure-noun">
        {{ formattedBloodPressure["diastolic"] }}
      </div>
      <span class="pressure-text">mmHg</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    systolic: {
      type: Number,
      default: null,
    }, // 수축기
    diastolic: {
      type: Number,
      default: null,
    }, // 이완기
  },

  computed: {
    formattedBloodPressure() {
      return {
        systolic: this.systolic ?? "--",
        diastolic: this.diastolic ?? "--",
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  max-height: 100%;
  display: flex;
  justify-content: space-evenly;
  padding: 50px 0 40px 0;
}

.blood_pressure-item {
  max-width: 120px;
  width: auto;
  display: flex;
  flex-direction: column;
}

.item-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 10px;
}

.status-systolic {
  background: #ee0000;
  border: 2px solid #ee0000;
}

.status-diastolic {
  background: #ff8000;
  border: 2px solid #ff8000;
}

.blood_pressure-noun {
  width: 112px;
  height: 117px;
  background: #ededed;
  display: grid;
  place-content: center;
  border-radius: 5px;
  font-family: GilroyBold !important;
  font-weight: 800;
  font-size: 45px;
  line-height: 55.13px;
  letter-spacing: 0%;
  text-align: center;
}

.pressure-text {
  font-family: GilroyMedium !important;
  font-weight: 500;
  font-size: 20px;
  line-height: 23.54px;
  letter-spacing: 0%;
  text-align: center;
  margin-top: 15px;
}
</style>
