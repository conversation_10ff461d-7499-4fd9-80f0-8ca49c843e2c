<template>
  <div class="history-chartBtn__wrapper">
    <div class="history-chart-btn">
      <div class="l--flex-center">
        <div
          :class="{
            'chart-btn__el': count !== 'd',
            'chart-btn__el chart-btn__el--isActive': count === 'd',
          }"
          @click="changeCount('d')"
        >
          {{ $t("chart_btn_d") }}
        </div>
        <div
          :class="{
            'chart-btn__el': count !== 'w',
            'chart-btn__el chart-btn__el--isActive': count === 'w',
          }"
          @click="changeCount('w')"
        >
          {{ $t("chart_btn_w") }}
        </div>
        <div
          :class="{
            'chart-btn__el': count !== 'm',
            'chart-btn__el chart-btn__el--isActive': count === 'm',
          }"
          @click="changeCount('m')"
        >
          {{ $t("chart_btn_m") }}
        </div>
        <div
          :class="{
            'chart-btn__el': count !== 'y',
            'chart-btn__el chart-btn__el--isActive': count === 'y',
          }"
          @click="changeCount('y')"
        >
          {{ $t("chart_btn_y") }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    count: String,
  },
  mounted() {
    // console.log(this.count);
  },
  methods: {
    changeCount(count) {
      this.$emit("changeCount", count);
    },
  },
};
</script>

<style lang="scss" scoped>
.history-chartBtn__wrapper {
  padding: 0 30px 25px 30px;
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

.history-chart-btn {
  width: 100%;
  height: 35px;
  padding: 5px 10px;
  background-color: #ededed;
  border-radius: 5px;
  color: #a7a7a7;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.l--flex-center {
  width: 100%;
  padding: 0 3px;
  display: flex;
  flex-wrap: nowrap;
  gap: 0 10px;
  align-items: center;
  justify-content: space-between;
}

.chart-btn__el {
  border-radius: 4px;
  width: 60px;
  height: 26px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  letter-spacing: -0.03em;
  line-height: 20px;
}

.chart-btn__el--isActive {
  border-radius: 7px;
  width: 60px;
  height: 26px;
  display: flex;
  justify-content: center;
  align-items: center;
  // padding: 2px 0px;
  background-color: #fff;
  color: #000;
  font-weight: 500;
  line-height: 20px;
}
</style>
