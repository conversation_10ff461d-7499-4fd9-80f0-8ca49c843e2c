<template>
  <div class="modal-background">
    <header class="modal-header">
      <div class="cancel-btn" @click="handleRecordCloseClick">
        <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
      </div>
      <div class="modal-title">{{ $t("setting_target_blood_pressure") }}</div>
      <button class="save-button" @click="handleSaveClick">
        {{ $t("save") }}
      </button>
    </header>
    <section>
      <div class="input-wrapper">
        <div>
          <span class="input-title">{{ $t("systolic") }}</span>
          <div class="input-container">
            <input
              class="input"
              type="number"
              inputmode="numeric"
              placeholder="0"
              v-model="systolicModel"
            />
          </div>
        </div>
        <div>
          <span class="input-title">{{ $t("diastolic") }}</span>
          <div class="input-container">
            <input
              class="input"
              type="number"
              inputmode="numeric"
              placeholder="0"
              v-model="diastolicModel"
            />
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  props: {
    systolic: {
      type: Number,
      default: null,
    },
    diastolic: {
      type: Number,
      default: null,
    },
  },

  /**
   * @returns {{ systolicState: number|null, diastolicState: number|null }}
   */
  data() {
    return {
      systolicState: this.systolic,
      diastolicState: this.diastolic,
    };
  },

  computed: {
    systolicModel: {
      get() {
        return this.systolicState === 0 || this.systolicState === null
          ? ""
          : this.systolicState;
      },
      set(v) {
        this.systolicState = v === "" ? 0 : Number(v);
      },
    },

    diastolicModel: {
      get() {
        return this.diastolicState === 0 || this.diastolicState === null
          ? ""
          : this.diastolicState;
      },
      set(v) {
        this.diastolicState = v === "" ? 0 : Number(v);
      },
    },
  },

  methods: {
    handleRecordCloseClick() {
      this.$store.commit("setIsShowBloodPressurePopup", false);
    },

    handleSaveClick(e) {
      e.preventDefault();

      try {
        localStorage.setItem(
          "bloodPressureGoal",
          JSON.stringify({
            systolic: this.systolicState,
            diastolic: this.diastolicState,
          })
        );
        this.$emit("save-success");
        this.handleRecordCloseClick();
        this.$emit("close");
      } catch (err) {
        console.error(err);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-background {
  width: 100%;
  border-radius: 30px 30px 0px 0px;
  position: fixed;
  max-width: 450px;
  padding: 30px;
  /* right: 0; */
  bottom: 0;
  left: 50%;
  transform: translateX(round(-50%));
  z-index: 999999;
  height: 93.5%;
  background: rgba(0, 0, 0, 0.81);
  opacity: 1;
  transition: opacity 0.15s linear;
  overscroll-behavior: contain;
  animation: showEditModal 0.7s forwards;
}

@keyframes showEditModal {
  0% {
    opacity: 0;
    transform: translate(-50%, 100%);
  }
  to {
    opacity: 1;
    transform: translateZ(-50%, 0);
  }
}

.cancel-btn {
  display: flex;
  width: 20px;
  height: 20px;
  align-items: center;
  img {
    width: 100%;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 55px;
}

.input-container {
  width: 130px;
  height: 50px;
  border-radius: 5px;
  background-color: #ededed;
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.input {
  width: 100%;
  caret-color: #41d8e6;
  font-family: GilroyMedium !important;
  font-weight: 500;
  font-size: 36px;
  line-height: 42.37px;
  letter-spacing: 0%;
  outline: none;
  text-align: center;
}

.input-wrapper {
  display: flex;
  justify-content: space-between;
}

.input-title {
  font-weight: 500;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: -3%;
  text-align: center;
  vertical-align: middle;
  color: #ffffff;
}

.modal-title {
  color: #ffffff;
  font-weight: 500;
  font-size: 20px;
  line-height: 24px;
  letter-spacing: -3%;
  text-align: center;
}

.save-button {
  width: 45px;
  height: 23px;
  font-weight: 700;
  font-size: 16px;
  line-height: 19.2px;
  letter-spacing: -3%;
  text-align: center;
  color: #ffffff;
  display: grid;
  place-content: center;
  background: #41d8e6;
  border-radius: 5px;
}
</style>
