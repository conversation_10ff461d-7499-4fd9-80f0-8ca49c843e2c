<template>
  <div>
    <div class="bg-modal">
      <div class="week-trend-modal bottomToTop">
        <div class="week-trend-modal__header">
          <div class="header__title" v-if="page === 'pee'">{{ $t("urination_trend") }}</div>
          <div class="header__title" v-else>{{ $t("hydration_trend") }}</div>
          <div class="header__close-btn" @click="closeWeekTrendModal">
            <img src="@/assets/images_assets/icons/close-btn-solid-ic.png" alt="close-btn" />
          </div>
        </div>
        <div>
          <div class="scatter-chart__wrapper">
            <ScatterChart
              :weekDayData="weekDayData"
              :page="page"
              :scatterData="graphData"
              v-if="loaded"
              @getSpecificDate="getSpecificDate"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ScatterChart from "@/components/Chart/ScatterChart.vue";
import API from "@/api/care/index.js";
import dataProcessing from "@/assets/data/manufacturing/care.js";
import { getSubjectId } from "../Common/getSubjectId.js";

export default {
  props: {
    page: String,
    specificDate: String,
  },
  components: {
    ScatterChart,
  },
  data() {
    return {
      graphData: [],
      loaded: false,
      weekDayData: [],
    };
  },
  methods: {
    closeWeekTrendModal: function () {
      this.$emit("closeWeekTrendModal", false);
    },
    getUtcOffset() {
      const now = new Date();
      const utcOffsetMinutes = now.getTimezoneOffset();
      const utcOffsetHours = -utcOffsetMinutes / 60;
      return utcOffsetHours;
    },
    async getSpecificWeekData(specificDate, page) {
      // console.log(specificDate, page);
      this.loaded = false;
      const subjectId = getSubjectId();
      // const [year, month, date] = specificDate.split(".");
      // const plusDate = Number(date) + 1;
      // const modifyedDate = plusDate < 10 ? `0${plusDate}` : plusDate;
      // const start = new Date(new Date(`${year}-${month}-${date}T00:00:00`).setDate(date - 7)).getTime();
      // const utcStart = new Date(start).toISOString();
      // // console.log(year, month, modifyedDate);
      // const end = new Date(`${year}-${month}-${modifyedDate}T00:00:00`).toISOString();
      const convertPage = page === "pee" ? "urine" : page;
      const [year, month, date] = specificDate.split(".");
      const plusDate = Number(date) + 1;
      const modifyedDate = plusDate < 10 ? `0${plusDate}` : `${plusDate}`;
      const start = new Date(`${year}-${month}-${date}T00:00:00`);
      start.setDate(start.getDate() - 7);

      const end = new Date(`${year}-${month}-${modifyedDate}T00:00:00`).toISOString();
      const utcStart = start.toISOString(); // yyyy-mm-ddT00:00:00 형식으로 출력됩니다.
      const utcOffset = this.getUtcOffset();

      try {
        // console.log(start);
        // const isKo = start.includes(".");
        // let [y, m, d] = isKo
        //   ? start.replace(/ /g, "").split(".") // y, m, d
        //   : start.replace(/ /g, "").split("/"); // m, d, y
        // y = y.length === 1 ? `0${y}` : y;
        // m = m.length === 1 ? `0${m}` : m;
        // d = d.length === 1 ? `0${d}` : d;
        // const utcStart = isKo
        //   ? new Date(`${y}-${m}-${d}T00:00:00`).toISOString()
        //   : new Date(`${d}-${y}-${m}T00:00:00`).toISOString();
        const { data } = await API.GetCareData(
          subjectId,
          convertPage,
          utcStart,
          end,
          utcOffset,
          ""
        );
        // console.log(utcStart, end, data);
        if (data) {
          this.loaded = true;
          const ScatterData =
            page === "pee"
              ? dataProcessing.GET_WATER_CARE_DATA(data, "w")
              : dataProcessing.GET_WATER_CARE_DATA(data, "w");
          this.graphData = ScatterData.graphData.reverse();
          this.weekDayData = ScatterData.dayAvgData.reverse();
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    getSpecificDate(specificDate) {
      // console.log(specificDate);
    },
  },
  mounted() {
    this.getSpecificWeekData(this.specificDate, this.page);
  },
};
</script>

<style lang="scss" scoped>
.bg-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 450px;
  height: 100%;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
}

.week-trend-modal {
  position: absolute;
  bottom: 0px;
  width: 100%;
  padding-top: 30px;
  background-color: #fff;
  border-radius: 30px 30px 0px 0px;
}

.week-trend-modal__header {
  display: flex;
  justify-content: space-between;
  padding: 0 30px;
  .header__title {
    font-weight: 700;
    font-size: 16px;
    text-align: left;
  }
  .header__close-btn {
    img {
      width: 20px;
    }
  }
}

.scatter-chart__wrapper {
  padding-right: 20px;
  // padding-bottom: 80px;
}

.chart-btn__wrapper {
  position: absolute;
  z-index: 99999999;
  bottom: 20px;
  width: 100%;
}

.bottomToTop {
  opacity: 0;
  animation: bottomToTop 0.7s forwards;
}

@keyframes bottomToTop {
  0% {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}
</style>
