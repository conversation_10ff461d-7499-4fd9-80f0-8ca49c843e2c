<template>
  <div class="chart-container">
    <apexchart
      type="radialBar"
      :options="updatedOptions"
      :series="[getStepPercent]"
      width="100%"
    />
    <div class="chart-overlay">
      <img
        class="current-step_icon"
        :src="require('@/assets/images/step-care-footpaint.png')"
        alt="foot-paint icon"
        loading="lazy"
        width="30px"
      />
      <strong class="current-step">{{
        currentStep.toLocaleString("ko-KR")
      }}</strong>
      <div class="result-etc">
        <span> {{ meterToKm }} km </span>
        <span class="divide-line">I</span>
        <span>{{ stepCalories }} kcal</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    currentStep: {
      type: Number,
      default: 0,
    },
    stepDistance: {
      type: Number,
      default: 0,
    },
    stepCalories: {
      type: Number,
      default: 0,
    },
    stepGoal: {
      type: Number,
      default: 0,
    },
  },

  computed: {
    meterToKm() {
      const ONE_KILO_METER = 1000;

      if (this.stepDistance <= 0) {
        return 0;
      }

      const km = this.stepDistance / ONE_KILO_METER;

      return Number.isInteger(km) ? km : parseFloat(km.toFixed(2));
    },
    getStepPercent() {
      const { currentStep, stepGoal } = this;
      if (!stepGoal || !currentStep) return 0;
      const percent = (currentStep / stepGoal) * 100;
      return percent > 100 ? 100 : Math.floor(percent);
    },

    updatedOptions() {
      return {
        chart: { type: "radialBar" },
        plotOptions: {
          radialBar: {
            startAngle: -140,
            endAngle: 140,
            track: {
              background: "#EDEDED",
              strokeWidth: "100%",
            },
            hollow: { size: "70%" },
            dataLabels: { show: false },
          },
        },
        fill: { colors: ["#4CC9F0"] },
        stroke: { lineCap: "round" },
      };
    },
  },
  mounted() {
    console.log(this.currentStep);
  },
};
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  max-width: 450px;
  max-height: 256px;
  background: #ffffff;
  position: relative;
  margin-bottom: 30px;
}

.chart-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -35%);
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.current-step {
  font-family: GilroyBold !important;
  font-size: 45px;
  font-weight: 800;
  text-align: center;
}

.current-step_icon {
  width: 30px;
  height: 33px;
}

.result-etc {
  font-family: GilroyMedium !important;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
}

.divide-line {
  font-family: GilroyMedium !important;
  font-size: 18px;
  font-weight: 500;
  color: #41d8e6;
  margin: 0 6px;
}
</style>
