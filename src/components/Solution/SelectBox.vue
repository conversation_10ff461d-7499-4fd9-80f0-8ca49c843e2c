<template>
  <div>
    <div class="modal-window--bg">
      <div class="modal-window__header">
        <div class="cancel-btn" @click="cancleBtnHandler">{{ $t("cancle_btn") }}</div>
        <div class="modal-window__title">{{ selectedItemCount }}{{ $t("foods_count") }}</div>
        <div :class="[active, 'confirm-btn']" @click="confirmBtnHandler">
          <button>{{ $t("confirm_btn") }}</button>
        </div>
      </div>
      <div class="modal-window__selectBtns">
        <div class="select-btn">
          <div class="select-btn__button" @click="addToBookmarkListHandler">
            <img src="@/assets/images_assets/icons/radio-activebtn-ic.png" v-if="addToBookmarkList" />
            <img src="@/assets/images_assets/icons/radio-light-btn-ic.png" v-else />
          </div>
          <div class="select-btn__label" @click="addToBookmarkListHandler">
            {{ labelTitle }}
          </div>
        </div>
        <div class="select-btn">
          <div class="select-btn__button" @click="addToMyMealCheckingHandler">
            <img src="@/assets/images_assets/icons/radio-activebtn-ic.png" v-if="addToMyMealChecking" />
            <img src="@/assets/images_assets/icons/radio-light-btn-ic.png" v-else />
          </div>
          <div class="select-btn__label" @click="addToMyMealCheckingHandler">
            {{ $t("add_meals") }}
          </div>
        </div>
      </div>
      <template v-if="addToMyMealChecking">
        <div class="add-to-mycheckinglist-btns">
          <div class="add-btn" v-for="(item, idx) in mealTitle" :key="idx" @click="addBtnHandler(idx)">
            <div>
              <img src="@/assets/images_assets/icons/wht-plus-ic.png" />
              <div>{{ item }}</div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tabTitle: String,
    selectedItemCount: Number,
  },
  data() {
    return {
      addToBookmarkList: false,
      addToMyMealChecking: false,
      mealTitle: [this.$i18n.t("breakfast"), this.$i18n.t("lunch"), this.$i18n.t("dinner"), this.$i18n.t("snack")],
      mealTypeIdx: 0,
    };
  },
  computed: {
    labelTitle() {
      if (this.tabTitle === "bookmark-meals") return this.$i18n.t("delete_favorites");
      return this.$i18n.t("add_favorites");
    },
    active() {
      return this.addToBookmarkList || this.addToMyMealChecking ? "confirm-btn__active" : "confirm-btn";
    },
  },
  watch: {
    addToBookmarkList(boolean) {},
    addToMyMealChecking(boolean) {
      // console.log(boolean);
    },
  },
  methods: {
    addToBookmarkListHandler() {
      this.addToMyMealChecking = false;
      if (this.addToBookmarkList) {
        this.addToBookmarkList = false;
      } else {
        this.addToBookmarkList = true;
      }
    },

    addToMyMealCheckingHandler() {
      this.addToBookmarkList = false;
      if (this.addToMyMealChecking) {
        this.addToMyMealChecking = false;
      } else {
        this.addToMyMealChecking = true;
      }
    },

    addBtnHandler(targetIdx) {
      const addBtn = document.querySelectorAll(".add-btn");
      this.mealTypeIdx = targetIdx;
      addBtn.forEach((item, idx) => {
        if (idx === targetIdx) {
          item.style.backgroundColor = "#41d8e6";
        } else {
          item.style.backgroundColor = "#dadada";
        }
      });
    },

    // 취소버튼
    cancleBtnHandler() {
      this.$store.commit("selectionBtn", false);
      this.$store.commit("closeSelectModal");
      this.$store.commit("hideSelectBtn");
    },

    // 확인버튼
    confirmBtnHandler() {
      if (this.addToBookmarkList) {
        this.$emit("bookmarkListHandler");
      }
      if (this.addToMyMealChecking) {
        const meals = {
          0: "breakfast",
          1: "lunch",
          2: "dinner",
          3: "snack",
        };

        const mealType = meals[`${this.mealTypeIdx}`];

        this.$emit("addToMyMealChecking", mealType);
      }
      this.$store.commit("selectionBtn", false);
      this.$store.commit("hideSelectBtn");
      this.$store.commit("closeSelectModal");
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-window--bg {
  width: 100%;
  position: absolute;
  bottom: 0px;
  z-index: 99999;
  background: #ededed;
  box-shadow: 0px 0px 10px 4px rgba(0, 0, 0, 0.35);
  border-radius: 30px 30px 0px 0px;
  padding: 30px 30px 40px;
  animation: showEditModal 0.7s forwards;
}

.modal-window__header {
  display: flex;
  justify-content: space-between;
  border-bottom: 0.5px solid #fff;
  padding-bottom: 10px;
  height: 35px;
  align-items: center;
}

.modal-window__title {
  font-weight: 500;
  font-size: 20px;
}
.cancel-btn {
  font-size: 18px;
  font-weight: 500;
  color: #646464;
}

.confirm-btn {
  height: 25px;
  background-color: #c8c8c8;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  color: #fff;
  font-weight: 700;
  width: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 100%;
}
.confirm-btn__active {
  background-color: #41d8e6;
}

.modal-window__selectBtns {
  display: flex;
  justify-content: space-between;
  padding: 15px 0px;
}

.select-btn {
  display: flex;
  align-items: center;
  gap: 5px;
}

.select-btn__button {
  height: 25px;
  img {
    width: 25px;
  }
}
.select-btn__label {
  letter-spacing: -0.05em;
  color: #000000;
  font-weight: 500;
  font-size: 18px;
  line-height: 23px;
}

.add-to-mycheckinglist-btns {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-gap: 10px;
  transition: all 1s;
}

.add-btn {
  background: #dadada;
  border-radius: 5px;
  height: 90px;
  color: #fff;
  letter-spacing: -0.05em;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 20px;
  }
}

@keyframes showEditModal {
  0% {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}
</style>
