<template>
  <div>
    <ErrorModal v-if="showErrorModal" :error="error" @isClicked="isClicked" />
    <div class="meal-categories" v-if="meal.length > 0">
      <div class="meal-categories__title">
        <div class="meal-categories__title--txt">{{ recordTitle }}</div>
        <div>
          <button class="record-handle-btn" @click="goToFoodSearchHandler(meal)">{{ $t("add") }}</button>
          <button :class="[active, 'record-handle-btn']" @click="selectBtntHandler(title)">
            {{ $t("btn_select") }}
          </button>
        </div>
      </div>
      <div class="meal-categories__items">
        <div class="meal-categories__item" v-for="(food, idx) in meal" :key="idx">
          <div class="select-btn">
            <div @click="selectHandler(food.id)" v-if="editBtnClicked" class="select-btn__button">
              <img :src="isSelected(food.id) ? activeIcon : inactiveIcon" />
            </div>
            <div class="meal-category">
              <div class="item__title">{{ isKo ? food.food.korean : food.food.english }}</div>
              <div class="item__createdat">{{ food.createdAt }}</div>
            </div>
          </div>
          <div class="item__amount" @click="goToFoodInfoEditHandler(food.id, idx)">
            <span>{{ Number(food.food.calorie * (food.gram / food.food.amount)).toFixed(1) }} Kcal</span>
            <img src="@/assets/images_assets/icons/arrow-blue-ic.png" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ErrorModal from "@/components/Common/ErrorModal.vue";

export default {
  props: {
    title: String,
    meal: Array,
  },
  components: { ErrorModal },
  data() {
    return {
      // editBtnClicked: false,
      isKo: true,
      isFoodSelected: false,
      showErrorModal: false,
      error: this.$i18n.t("over_ten"),
      selectedFoods: [],
      activeIcon: require("@/assets/images_assets/icons/radio-activebtn-ic.png"),
      inactiveIcon: require("@/assets/images_assets/icons/radio-btn-ic.png"),
    };
  },
  computed: {
    recordTitle() {
      if (this.title === "breakfast") return this.$i18n.t("breakfast");
      if (this.title === "lunch") return this.$i18n.t("lunch");
      if (this.title === "dinner") return this.$i18n.t("dinner");
      return this.$i18n.t("snack");
    },
    active() {
      return this.$store.state.recordSelectType === this.title ? "active-record-btn" : false;
    },
    editBtnClicked() {
      return this.$store.state.recordSelectType === this.title;
    },
    recordSelectType() {
      return this.$store.state.recordSelectType;
    },
    recordSelectBtn() {
      return this.$store.state.recordSelectBtn;
    },
  },
  watch: {
    recordSelectBtn(newVal) {
      newVal ? null : (this.selectedFoods = []);
    },
    selectedFoods(newVal) {
      // console.log(newVal);
      const selectedFoods = { type: this.title, foods: newVal };
      newVal.length !== 0 ? this.$store.commit("showRecordSelectModal", true) : null;
      // : this.$store.commit("showRecordSelectModal", false);
      this.$emit("selectBoxHandler", selectedFoods);
    },
  },
  methods: {
    isClicked() {
      this.showErrorModal = false;
    },
    goToFoodSearchHandler(meal) {
      // console.log(meal[0].type);
      this.$router.push({ path: `/food/search`, query: { type: meal[0].type } });
    },
    goToFoodInfoEditHandler(foodId, idx) {
      // console.log(foodId);
      // console.log(this.meal[idx]);
      this.$store.commit("getFoodCard", this.meal[idx]);
      this.$router.push({ path: `/food/edit/${foodId}?type=${this.title}` });
    },
    selectBtntHandler(type) {
      // console.log(this.$store.state.recordSelectBtn);
      if (this.$store.state.recordSelectBtn) {
        this.$store.commit("recordSelectBtn", false);
        this.$store.commit("showRecordSelectModal", false);
        this.$store.commit("recordSelectType", "");
      } else {
        this.selectedItems = [];
        this.$store.commit("recordSelectBtn", true);
        this.$store.commit("showRecordSelectModal", true);
        this.$store.commit("recordSelectType", type);
      }
    },
    isSelected(idx) {
      // console.log(idx);
      return this.selectedFoods.includes(idx);
    },
    selectHandler(foodId) {
      // if (this.isSelected(foodId)) {
      //   // If already selected, deselect the food
      //   this.selectedFoods = this.selectedFoods.filter((id) => id !== foodId);
      // } else if (this.selectedFoods.length < 10) {
      //   // If not selected and within the limit, select the food
      //   this.selectedFoods.push(foodId);
      // }

      if (this.isSelected(foodId)) {
        // If already selected, deselect the food
        this.selectedFoods = this.selectedFoods.filter((id) => id !== foodId);
      } else if (this.selectedFoods.length < 10) {
        // If not selected and within the limit, select the food
        this.selectedFoods.push(foodId);
      } else {
        // Show error modal when maxChoices is exceeded
        this.showErrorModal = true;
      }

      // if (this.selectedFoods.length < 10) {
      //   if (this.isSelected(foodId)) {
      //     // If already selected, deselect the food
      //     this.selectedFoods = this.selectedFoods.filter((id) => id !== foodId);
      //   } else {
      //     // If not selected, select the food
      //     this.selectedFoods.push(foodId);
      //   }
      // } else {
      //   // Show error modal when maxChoices is exceeded
      //   this.showErrorModal = true;
      // }
    },
  },
  mounted() {
    // console.log(this.meal);
    this.isKo = this.$i18n.locale === "ko";
  },
};
</script>

<style lang="scss">
.meal-categories {
  padding-top: 32px;
}

.meal-categories__title {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #a7a7a7;
  padding-bottom: 10px;
  gap: 5px;
}

.meal-categories__title--txt {
  font-size: 18px;
  font-weight: 500;
}

.record-handle-btn {
  padding: 0 10px;
  border: 1px solid #ededed;
  border-radius: 5px;
  margin-left: 7px;
  font-size: 16px;
  font-weight: 500;
  color: #858585;
}

.active-record-btn {
  color: #ffffff;
  border: 1px solid #41d8e6;
  background-color: #41d8e6;
}
.meal-categories__item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 0px 8px;
  border-bottom: 0.5px solid #a7a7a7;
  width: 100%;
}

.select-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  width: 70%;
}

.select-btn__button {
  height: 25px;
  img {
    width: 25px;
  }
}

.meal-categories__item:last-child {
  border-bottom: none;
}

.meal-category {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}
.item__title {
  color: #646464;
  font-size: 16px;
  text-align: left;
}

.item__createdat {
  font-family: GilroyMedium;
  color: #a7a7a7;
  font-size: 14px;
  // line-height: 14px;
}

.item__amount {
  font-size: 18px;
  color: #646464;
  width: calc(30% + 10px);
  text-align: right;
  span {
    padding-right: 10px;
    font-family: GilroyMedium;
  }
  img {
    width: 10px;
  }
}
</style>
