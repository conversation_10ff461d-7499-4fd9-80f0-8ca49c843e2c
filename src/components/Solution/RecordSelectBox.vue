<template>
  <div>
    <div class="modal-window--bg">
      <div class="modal-window__header">
        <div class="cancel-btn" @click="cancleBtnHandler">{{ $t("cancle_btn") }}</div>
        <div class="modal-window__title">{{ selectedItemCount }}{{ $t("foods_count") }}</div>
        <div :class="[active, 'confirm-btn']" @click="confirmBtnHandler">
          <button>{{ $t("confirm_btn") }}</button>
        </div>
      </div>
      <div class="modal-window__selectBtns">
        <div class="select-btn">
          <div class="select-btn__button" @click="addToBookmarkListHandler">
            <img src="@/assets/images_assets/icons/radio-activebtn-ic.png" v-if="deletedItems" />
            <img src="@/assets/images_assets/icons/radio-light-btn-ic.png" v-else />
          </div>
          <div class="select-btn__label" @click="addToBookmarkListHandler">
            {{ $t("delete_btn") }}
          </div>
        </div>
        <div class="select-btn__edit">
          <div class="select-btn__button" @click="addToMyMealCheckingHandler">
            <img src="@/assets/images_assets/icons/radio-activebtn-ic.png" v-if="timeEdit" />
            <img src="@/assets/images_assets/icons/radio-light-btn-ic.png" v-else />
          </div>
          <div class="select-btn__label" @click="addToMyMealCheckingHandler">
            {{ $t("edit_time") }}
          </div>
        </div>
      </div>

      <div v-if="timeEdit">
        <div class="time-settings__wrapper">
          <div class="time-btn__wrapper">
            <button @click="amBtnHandler" :class="[amActive, 'inactive-btn']">{{ $t("am") }}</button>
            <button @click="pmBtnHandler" :class="[pmActive, 'inactive-btn']">{{ $t("pm") }}</button>
          </div>
          <div class="time-select__wrapper">
            <div :class="[timeSelected, 'select-box']">
              <button @click="hourBtnClicked" class="hour-btn">{{ defaultHour }}</button>
              <div class="colon">:</div>
              <button @click="minuteBtnClicked" class="hour-btn">{{ defaultMinute }}</button>
              <ul v-if="showHourList" class="hour-list">
                <li v-for="hh in hour" v-bind:key="hh" class="hour-item" @click="hourBtnHandler(hh)">
                  {{ hh }}
                </li>
              </ul>
              <ul v-if="showMinuteList" class="minute-list">
                <li v-for="(mm, id) in minute" v-bind:key="id" class="minute-item" @click="minuteHandler(mm)">
                  {{ mm }}
                </li>
              </ul>
              <div class="arrow-icon__wrapper">
                <img src="@/assets/images/mypage-icon/bottom_arrow.png" alt="arrow" />
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="add-to-mycheckinglist-btns">
          <div
            class="add-btn"
            v-for="(item, idx) in mealTitle"
            :key="idx"
            @click="addBtnHandler(idx)"
            :ref="enTitle[idx]"
          >
            <div>
              <img src="@/assets/images_assets/icons/wht-plus-ic.png" />
              <div>{{ item }}</div>
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tabTitle: String,
    selectedItemCount: Number,
  },
  data() {
    return {
      deletedItems: false,
      timeEdit: false,
      defaultHour: 8,
      defaultMinute: "00",
      hour: 12,
      minute: ["00", "10", "20", "30", "40", "50"],
      minuteListClicked: false,
      minuteItemClicked: false,
      onUrineTestAlert: false,
      showHourList: false,
      showMinuteList: false,
      amBtnClicked: true,
      pmBtnClicked: false,
      // mealTitle: ["아침", "점심", "저녁", "간식"],
      // enTitle: ["break", "lunch", "dinner", "snack"],
      // mealTypeIdx: 0,
    };
  },
  computed: {
    active() {
      return (this.deletedItems && this.selectedItemCount !== 0) || (this.timeEdit && this.selectedItemCount !== 0)
        ? "confirm-btn__active"
        : "confirm-btn";
    },
    amActive() {
      return this.amBtnClicked ? "time-btn" : false;
    },
    pmActive() {
      return this.pmBtnClicked ? "time-btn" : false;
    },
    timeSelected() {
      return this.showHourList || this.showMinuteList ? "active-select-box" : false;
    },
  },
  watch: {
    // deletedItems(boolean) {
    //   console.log(boolean);
    // },
    // timeEdit(boolean) {
    //   console.log(boolean);
    // },
  },
  methods: {
    addToBookmarkListHandler() {
      this.timeEdit = false;
      if (this.deletedItems) {
        this.deletedItems = false;
      } else {
        this.deletedItems = true;
      }
    },

    addToMyMealCheckingHandler() {
      this.deletedItems = false;
      if (this.timeEdit) {
        this.timeEdit = false;
      } else {
        this.timeEdit = true;
      }
    },

    addBtnHandler(targetIdx) {
      const addBtn = document.querySelectorAll(".add-btn");
      // this.mealTypeIdx = targetIdx;
      addBtn.forEach((item, idx) => {
        if (idx === targetIdx) {
          item.style.backgroundColor = "#41d8e6";
        } else {
          item.style.backgroundColor = "#ffffff";
        }
      });
    },

    // 취소버튼
    cancleBtnHandler() {
      this.$store.commit("recordSelectBtn", false);
      this.$store.commit("showRecordSelectModal", false);
      this.$store.commit("recordSelectType", "");
    },

    // 확인버튼
    confirmBtnHandler() {
      if (this.deletedItems) {
        this.$emit("deletedItems", true);
      } else {
        const time = [this.amBtnClicked, this.defaultHour, this.defaultMinute];
        this.$emit("timeEdit", time);
      }

      this.$store.commit("recordSelectBtn", false);
      this.$store.commit("showRecordSelectModal", false);
      this.$store.commit("recordSelectType", "");
    },

    hourBtnHandler(id) {
      this.defaultHour = id;
      this.showHourList = false;
    },
    minuteHandler(idx) {
      this.defaultMinute = idx;
      this.showMinuteList = false;
    },
    hourBtnClicked() {
      if (this.showMinuteList === true) {
        this.showHourList = true;
        this.showMinuteList = false;
      } else if (this.showMinuteList === false) {
        this.showHourList = !this.showHourList;
      }
    },
    minuteBtnClicked() {
      if (this.showHourList === true) {
        this.showHourList = false;
        this.showMinuteList = true;
      } else if (this.showHourList === false) {
        this.showMinuteList = !this.showMinuteList;
      }
    },
    amBtnHandler() {
      if (this.pmBtnClicked === true) {
        this.pmBtnClicked = false;
        this.amBtnClicked = true;
      }
      this.amBtnClicked = true;
    },
    pmBtnHandler() {
      if (this.amBtnClicked === true) {
        this.amBtnClicked = false;
        this.pmBtnClicked = true;
      }
      this.pmBtnClicked = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-window--bg {
  width: 100%;
  position: absolute;
  bottom: 0px;
  z-index: 99999;
  background: #ededed;
  box-shadow: 0px 0px 10px 4px rgba(0, 0, 0, 0.35);
  border-radius: 30px 30px 0px 0px;
  padding: 30px 30px 40px;
  animation: showEditModal 0.7s forwards;
}

.modal-window__header {
  display: flex;
  justify-content: space-between;
  border-bottom: 0.5px solid #fff;
  padding-bottom: 10px;
  height: 35px;
  align-items: center;
}

.modal-window__title {
  font-weight: 500;
  font-size: 20px;
}
.cancel-btn {
  font-size: 18px;
  font-weight: 500;
  color: #646464;
}

.confirm-btn {
  height: 25px;
  background-color: #c8c8c8;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  color: #fff;
  font-weight: 700;
  width: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 100%;
}
.confirm-btn__active {
  background-color: #41d8e6;
}

.modal-window__selectBtns {
  display: flex;
  justify-content: space-between;
  padding: 15px 0px;
  width: 100%;
}

.select-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  width: 60%;
}
.select-btn__edit {
  display: flex;
  align-items: center;
  gap: 5px;
  width: 40%;
}

.select-btn__button {
  height: 25px;
  img {
    width: 25px;
  }
}
.select-btn__label {
  letter-spacing: -0.05em;
  color: #000000;
  font-weight: 500;
  font-size: 18px;
  line-height: 23px;
}

.add-to-mycheckinglist-btns {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-gap: 10px;
  transition: all 1s;
}

.add-btn {
  background: #dadada;
  border-radius: 5px;
  height: 90px;
  color: #fff;
  letter-spacing: -0.05em;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 20px;
  }
}

@keyframes showEditModal {
  0% {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}

.time-settings__wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin: 30px 0 10px;
}

.time-btn__wrapper {
  width: 140px;
  height: 55px;
  background-color: #ffffff;
  border: 1px solid #a7a7a7;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.time-btn {
  width: 55px;
  height: 37.5px;
  background-color: #ededed;
  border-radius: 4px;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  margin: 0 5px;
  color: #000000 !important;
  font-size: 20px;
  font-weight: 500;
}

.inactive-btn {
  width: 55px;
  height: 37.5px;
  margin: 0 5px;
  font-size: 20px;
  font-weight: 500;
  color: #a7a7a7;
}

.inactive-btn__wrapper {
  width: 140px;
  height: 55px;
  background-color: #fff;
  border-radius: 5px;
  border: 1px solid #fff;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: GilroyMedium;
}

.time-select__wrapper {
  width: 140px;
  height: 55px;
  background-color: #fff;
  border-radius: 5px;
  border: 1px solid #ededed;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: GilroyMedium;
  letter-spacing: -0.03em;
}
// .time-select__wrapper:hover {
//   border: 2px solid #41d8e6;
// }

.colon {
  font-size: 24px;
  font-weight: 500;
  color: #a7a7a7;
  margin: 0 5px;
}

.arrow-icon__wrapper {
  width: 14px;
  display: flex;
  align-items: center;
  margin-left: 5px;
  img {
    width: 100%;
    transform: rotate(180deg);
  }
}
.select-box {
  width: 140px;
  display: flex;
  padding: 10px;
  border: 1px solid #a7a7a7;
  border-radius: 5px;
}
.active-select-box {
  border: 1px solid #41d8e6;
}
.hour-btn {
  width: 50%;
  letter-spacing: -0.03em;
  font-size: 24px;
  font-weight: 500;
}

.hour-list {
  background-color: #fff;
  width: 140px;
  // height: 100%;
  min-height: 310px;
  max-height: 65vh;
  overflow: scroll;
  position: absolute;
  bottom: 125px;
  right: 30px;
  border-radius: 5px;
  border: 2px solid #41d8e6;
}

ul {
  padding: 0 !important;
  list-style: none;
  z-index: 5;
}

.hour-item {
  font-family: GilroyMedium;
  letter-spacing: -0.03em;
  font-size: 24px;
  font-weight: 500;
  text-align: left;
  width: 100%;
  padding: 5px 10px 5px 30px;
}
.hour-item:hover {
  background-color: #c9f4f8;
}

.minute-list {
  background-color: #fff;
  width: 140px;
  height: 340px;
  max-height: 70vh;
  overflow: scroll;
  position: absolute;
  bottom: 125px;
  right: 30px;
  border-radius: 5px;
  border: 2px solid #41d8e6;
}
.minute-item {
  font-family: GilroyMedium;
  letter-spacing: -0.03em;
  font-size: 24px;
  font-weight: 500;
  text-align: right;
  width: 100%;
  padding: 10px 30px 10px 10px;
}

.minute-item:hover {
  background-color: #c9f4f8;
}
</style>
