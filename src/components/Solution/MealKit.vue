<template>
  <div>
    <ErrorModal v-if="showErrorModal" :error="error" @isClicked="isClicked" />
    <div @click="goToMealKitDetail">
      <div class="mealkit-card" @click="mealkitSelectHandler(foodId)">
        <div class="mealkit-image">
          <img :src="img" />
        </div>

        <div class="mealkit-title">
          <div>
            {{
              foodName.length > 15 ? `${foodName.slice(0, 15)}...` : foodName
            }}
          </div>
        </div>
        <div class="mealkit-info">
          <div>{{ foodEnergy }}kcal</div>
          <div>{{ foodAmount }}g</div>
        </div>
        <div class="mealkit-card__radiobtn" v-if="showSelectBtnHandler">
          <img
            :src="
              selected
                ? require('@/assets/images_assets/icons/radio-activebtn-ic.png')
                : require('@/assets/images_assets/icons/radio-btn-ic.png')
            "
          />
          <!-- @click="toggleCheckbox" -->
          <!-- <img src="@/assets/images_assets/icons/radio-activebtn-ic.png" v-if="selected && showSelectBtnHandler" />
          <img src="@/assets/images_assets/icons/radio-btn-ic.png" v-else /> -->
        </div>
        <div class="mealkit-card__ic" v-if="type === 'bookmark' || favorite">
          <img src="@/assets/images_assets/icons/bookmark-y-ic.png" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ErrorModal from "@/components/Common/ErrorModal.vue";

export default {
  props: {
    page: String,
    foodInfo: Object,
    foodId: Number,
    foodImage: String,
    foodName: String,
    foodEnergy: Number,
    foodAmount: Number,
    type: String,
    favorite: Boolean,
    selectedItemLength: Number,
  },
  components: { ErrorModal },
  data() {
    return {
      selected: false,
      img: `https://food-cym-s3.s3.ap-northeast-2.amazonaws.com/cym/${this.foodImage}.jpg`,
      showErrorModal: false,
      error: this.$i18n.t("over_ten"),
    };
  },
  watch: {
    showSelectBtnHandler(newVal) {
      if (!newVal) {
        this.selected = false;
      }
    },
    avaibleSelect(newVal) {
      // console.log(newVal);
    },
  },
  computed: {
    showSelectBtnHandler() {
      return this.$store.state.showSelectBtn;
    },
  },
  mounted() {
    // console.log(this.foodId)
  },
  methods: {
    isClicked() {
      this.showErrorModal = false;
    },
    // 음식카드 선택
    mealkitSelectHandler(foodId) {
      // console.log(foodId);
      // this.selectedItemLength < 10 ? this.$emit("getSelectedItem", foodId) : null;
      // this.$emit("getSelectedItem", foodId);
      this.$store.commit("openSelectModal", true);
      // if (!this.selected && this.selectedItemLength < 10) {
      //   this.selected = true;
      // } else if (!this.selected && this.selectedItemLength >= 10) {
      //   this.showErrorModal = true;
      // } else {
      //   this.selected = false;
      // }

      if (!this.selected) {
        if (this.selectedItemLength < 10) {
          this.selected = true;
          this.$emit("getSelectedItem", foodId);
        } else if (this.selectedItemLength >= 10) this.showErrorModal = true;
        else {
          this.selected = false;
          this.$emit("getSelectedItem", foodId);
        }
      } else {
        if (this.selectedItemLength <= 10) {
          this.selected = false;
          this.$emit("getSelectedItem", foodId);
        }
      }
    },

    // 음식에 대한 상세정보 페이지가기
    goToMealKitDetail() {
      this.$emit("foodCardClickHandler", true);
      this.$store.commit("getFoodCard", this.foodInfo);
      this.$store.commit("selectedFoodId", this.foodId);
      // const selected = this.$store.state.selectedFood;
      // console.log(selected);
      if (!this.showSelectBtnHandler) {
        this.page === "bookmark"
          ? this.$router.push({
              path: `/solution/${this.foodInfo.id}`,
              query: { savedId: this.foodId },
            })
          : this.$router.push({
              path: `/solution/${this.foodInfo.id}`,
              query: { foodId: this.foodId },
            });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.mealkit-card {
  position: relative;
  width: 100%;
  border: 1px solid #ededed;
  border-radius: 5px;
}

.mealkit-image {
  width: 100%;
  img {
    width: 100%;
    height: 100px;
    object-fit: cover;
    border-radius: 4px 4px 0px 0px;
  }
}

.mealkit-title {
  text-align: left;
  font-weight: 500;
  font-size: 16px;
  color: #646464;
  padding: 0 10px;
}
.mealkit-info {
  padding: 0px 10px 5px 10px;
  display: flex;
  justify-content: space-between;

  div {
    font-size: 14px;
    font-family: GilroyMedium;
    color: #a7a7a7;
  }
}

.v-application a {
  color: #646464;
}

.mealkit-card__radiobtn {
  position: absolute;
  z-index: 1;
  top: 10px;
  right: 10px;
  img {
    width: 22px;
  }
}
.mealkit-card__ic {
  position: absolute;
  z-index: 1;
  top: 0px;
  left: 10px;
  img {
    width: 20px;
  }
}
</style>
