<template>
  <div>
    <div class="search-result-list__hide-compo">
      <div class="food-info--large">
        <div class="food-info__wrapper">
          <div class="info__input">
            <input type="number" inputmode="numeric" pattern="[0-9]*" v-model="amount" @blur="inputHandler" />
          </div>
          <div class="info__unit">g</div>
        </div>
        <div class="food-info">
          <div>
            {{ (foodKcal * ratio).toFixed(1) }}
          </div>
          <div class="info__unit">kcal</div>
        </div>
      </div>
      <div class="food-info--small">
        <!-- v-for="(item, idx) in foodInfo" :key="idx" -->
        <div class="food-info__item">
          <!-- <div class="food-item__num">{{ (item.value * ratio).toFixed(1) }}</div>
          <div class="food-item__txt">{{ item.name }}</div> -->
          <div class="food-item__num">{{ (foodInfo.carb * ratio).toFixed(1) }}</div>
          <div class="food-item__txt">{{ $t("carbohydrate") }}(g)</div>
        </div>
        <div class="food-info__item">
          <div class="food-item__num">{{ (foodInfo.protein * ratio).toFixed(1) }}</div>
          <div class="food-item__txt">{{ $t("protein_ingredient") }}(g)</div>
        </div>
        <div class="food-info__item">
          <div class="food-item__num">{{ (foodInfo.fat * ratio).toFixed(1) }}</div>
          <div class="food-item__txt">{{ $t("fat") }}(g)</div>
        </div>
        <div class="food-info__item">
          <div class="food-item__num">{{ (foodInfo.sugar * ratio).toFixed(1) }}</div>
          <div class="food-item__txt">{{ $t("sugar") }}(g)</div>
        </div>
        <div class="food-info__item">
          <div class="food-item__num">{{ (foodInfo.sodium * ratio).toFixed(1) }}</div>
          <div class="food-item__txt">{{ $t("sodium") }}(mg)</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    foodInfo: Object,
    foodKcal: Number,
    foodAmount: Number,
  },
  data() {
    return {
      originAmout: 0,
      amount: 0,
      ratio: 0,
    };
  },
  methods: {
    inputHandler(e) {
      // const ratio = e.target.value / 100;
      // this.ratio = ratio.toFixed(1);
      // console.log(ratio);
    },
  },
  watch: {
    amount(newVal) {
      const ratio = (newVal / this.originAmout).toFixed(1);
      // console.log(ratio);
      this.ratio = ratio;
      const foodInfo = this.foodInfo;
      foodInfo.newAmount = Number(newVal);
      // { amount: Number(newVal), id: this.foodInfo.id };
      this.$emit("inputAmount", foodInfo);
    },
  },
  mounted() {
    // console.log(this.foodInfo);
    this.amount = this.foodAmount;
    this.originAmout = this.foodAmount;
    this.ratio = 1;
  },
};
</script>

<style lang="scss" scoped>
.search-result-list__hide-compo {
  background: #ededed;
  transition: all ease-in-out 0.8s;
}

.food-info--large {
  padding: 20px 10px 0px 12px;
  display: flex;
  justify-content: space-between;
  border-top: 0.5px solid #a7a7a7;
}

.food-info__wrapper {
  display: flex;
  align-items: flex-end;
  font-size: 22px;
  letter-spacing: -0.03em;
  font-family: GilroyMedium;
}

.food-info {
  border-bottom: 1px solid #a7a7a7;
  display: flex;
  align-items: flex-end;
  width: 100px;
  justify-content: space-between;
  font-size: 22px;
  letter-spacing: -0.03em;
  font-family: GilroyMedium;
}

.info__input {
  width: 110px;
  text-align: left;
  text-indent: 5px;
  border-radius: 5px;
  input {
    width: 120px;
    height: 35px;
    background-color: #ffffff;
    border-radius: 5px;
    padding: 0 20px;
    outline: none;
    &:hover {
      border: 1px solid #41d8e6;
    }
  }
}

.v-text-field .v-text-field--solo .v-input__control input {
  caret-color: #41d8e6 !important;
}
.info__unit {
  font-size: 16px;
  line-height: 19px;
  letter-spacing: -0.03em;
  padding: 0 5px 3px 0;
}

.food-info--small {
  padding: 16px 10px;
  display: flex;
  justify-content: space-between;
}

.food-info__item {
}
.food-item__num {
  min-width: 55px;
  font-family: GilroyMedium;
  border-bottom: 0.5px solid #a7a7a7;
}
.food-item__txt {
  font-size: 11px;
  letter-spacing: -0.05rem;
  color: #646464;
}
</style>
