<template>
  <div class="result-list">
    <ErrorModal v-if="showErrorModal" :error="error" @isClicked="isClicked" />
    <div v-if="isSearchedItem" class="add-btn__wrapper">
      <v-btn elevation="0" color="#41D8E6" class="add-btn" :disabled="!isSelectedFood" @click="addBtnHandler">
        {{ $t("add_record") }}
      </v-btn>
    </div>
    <div class="no-result__wrapper" v-else-if="isSearched && !isSearchedItem">
      <div class="no-result-title">{{ $t("no_result_food") }}</div>
      <div class="no-result-text">{{ $t("please_wait_solution_food") }}</div>
    </div>
    <div class="list__wrapper" @scroll="handleFoodListScroll">
      <div class="search-result-list" v-for="(food, idx) in searchResult" :key="food.id + Math.random()">
        <div class="search-result-list__item">
          <div class="list-item--left">
            <div class="list-item__title" v-if="isKo">{{ food.korean }}</div>
            <div class="list-item__title" v-else>{{ food.english }}</div>
            <div class="list-item__subtitle" @click="openFoodInfoBoxHandler(idx)">
              <span>{{ $t("per_serving") }} {{ food.amount }}g</span>
              <img src="@/assets/images_assets/icons/below-blue-arrow-ic.png" />
            </div>
          </div>
          <div class="list-item--right">
            <div class="list-item__info">{{ food.calorie }}kcal</div>
            <div class="list-item__selectBtn" @click="selectHandler(food.id)">
              <img :src="isSelected(food.id) ? activeIcon : inactiveIcon" />
            </div>
          </div>
        </div>
        <FoodInfoBox
          v-show="idx === clickedId"
          :foodInfo="food"
          :foodAmount="food.amount"
          :foodKcal="food.calorie"
          @inputAmount="inputAmount"
        />
      </div>
    </div>
  </div>
</template>

<script>
import FoodInfoBox from "./FoodInfoBox.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";

export default {
  namd: "SearchResultItem",
  props: {
    searchResult: Array,
    isSearchedItem: Boolean,
    isSearched: Boolean,
  },
  components: {
    FoodInfoBox,
    ErrorModal,
  },
  data() {
    return {
      showErrorModal: false,
      error: this.$i18n.t("over_ten"),
      selectBtn: false,
      isKo: true,
      clickedId: null,
      page: 1,
      selectedFoods: [],
      enteredFoods: [],
      activeIcon: require("@/assets/images_assets/icons/radio-activebtn-ic.png"),
      inactiveIcon: require("@/assets/images_assets/icons/radio-btn-ic.png"),
    };
  },
  computed: {
    isSelectedFood() {
      return this.selectedFoods.length !== 0;
    },
  },
  methods: {
    handleFoodListScroll(e) {
      const { scrollHeight, scrollTop, clientHeight } = e.target;

      this.scrollTop = scrollTop;
      this.scrollToTop === 0 ? this.$store.commit("clickTitle", true) : this.$store.commit("clickTitle", false);
      const isAtTheBottom = this.isIos
        ? scrollHeight === scrollTop + clientHeight || scrollHeight === scrollTop + clientHeight + 0.5
        : scrollHeight <= scrollTop + clientHeight + 1;
      if (isAtTheBottom) this.handleLoadMore();
    },
    handleLoadMore() {
      // this.page += 1;
      this.$emit("pageHandler", true);
      // console.log("Need to Loading");
    },
    isClicked() {
      this.showErrorModal = false;
    },
    removeDuplicates(array, property) {
      return array.filter((item, index, self) => index === self.findIndex((obj) => obj[property] === item[property]));
    },
    inputAmount(fromChild) {
      // console.log(fromChild);
      this.searchResult.map((i) => {
        if (i.id === fromChild.id) {
          fromChild.amount !== fromChild.newAmount ? this.enteredFoods.push(fromChild) : null;
        }
      });
      const copyArr = this.enteredFoods;
      const uniqueArr = this.removeDuplicates(copyArr, "id");
      this.enteredFoods = uniqueArr;
      // console.log(this.enteredFoods);
      this.$emit("addBtnHandler", this.enteredFoods);
    },
    openFoodInfoBoxHandler(id) {
      // console.log(id);
      this.clickedId = this.clickedId === id ? null : id;
    },
    selectHandler(foodId) {
      if (this.isSelected(foodId)) {
        this.selectedFoods = this.selectedFoods.filter((id) => id !== foodId);
        this.$emit("selectBtnHandler", this.selectedFoods);
      } else if (this.selectedFoods.length < 10) {
        this.selectedFoods.push(foodId);
        this.$emit("selectBtnHandler", this.selectedFoods);
      } else {
        this.showErrorModal = true;
      }
    },
    isSelected(idx) {
      return this.selectedFoods.includes(idx);
    },
    addBtnHandler() {
      const filteredArr = [];
      this.searchResult
        .filter((i) => this.selectedFoods.includes(i.id))
        .map((food) => {
          this.enteredFoods.length !== 0
            ? filteredArr.push({ foodId: food.id, gram: food.newAmount })
            : filteredArr.push({ foodId: food.id, gram: food.amount });
        });
      this.$emit("searchItemHandler", filteredArr);
    },
  },
  mounted() {
    this.isKo = this.$i18n.locale === "ko";
  },
};
</script>

<style lang="scss" scoped>
.result-list {
  height: 90%;
  // height: calc(100vh - 200px);
}

.list__wrapper {
  padding: 0 10px 10vh;
  overflow: scroll;
  height: 100%;
  // height: calc(100vh - 250px);
}

.search-result-list {
  border-bottom: 0.5px solid #a7a7a7;
}

.search-result-list__item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0px;
}
.list-item__title {
  text-align: left;
  font-weight: 500;
  font-size: 18px;
  color: #646464;
}
.list-item__subtitle {
  font-size: 14px;
  color: #646464;
  line-height: 14px;
  display: flex;
  align-items: flex-end;
  img {
    width: 16px;
    object-fit: contain;
    margin-left: 3px;
  }
}

.list-item--right {
  display: flex;
  align-items: center;
}

.list-item__info {
  font-family: GilroyMedium;
  color: #646464;
}

.list-item__selectBtn {
  img {
    width: 22px;
    margin-top: 5px;
    margin-left: 10px;
  }
}

.add-btn__wrapper {
  display: flex;
  height: 60px;
  align-items: center;
  justify-content: end;
  padding: 30px 0 20px;
}
.add-btn {
  border: 1px solid #ededed !important;
  padding: 0 15px;
  height: 25px !important;
  min-width: 90px !important;
  border-radius: 5px !important;
  color: #858585 !important;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: -0.03em;
  white-space: nowrap;
}

.no-result__wrapper {
  width: 100%;
  text-align: left;
  padding: 0 10px;
}

.no-result-title {
  padding-top: 50px;
  font-size: 20px;
  font-weight: 500 !important;
}

.no-result-text {
  padding-top: 10px;
  font-size: 16px;
  color: #646464;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #ffffff !important;
  border: 1px solid #ededed !important;
}
::v-deep .theme--light.v-btn.v-btn--disabled {
  color: #858585 !important;
}
::v-deep .theme--light.v-btn {
  color: #ffffff !important;
  border: 1px solid #41d8e6 !important;
}
</style>
