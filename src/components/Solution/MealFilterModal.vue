<template>
  <div>
    <div class="care-setting-modal__wrapper">
      <!-- header section -->
      <div class="care-setting-modal__nav">
        <div class="nav-item__canclebtn" @click="closeFilterModal">{{ $t("cancle_btn") }}</div>
        <div class="nav-item__title">{{ $t("btn_filter") }}</div>
        <div class="nav-item__canclebtn" @click="resetFilter">{{ $t("reset") }}</div>
      </div>
      <div class="filter-contents__wrapper">
        <div class="meal-filter-content__box">
          <div class="filter-content__title">{{ $t("diet_profile_edit") }}</div>
          <div class="filter-content__items">
            <v-btn-toggle v-model="diet" multiple>
              <v-btn class="food-tag" text v-for="(diet, idx) in categories" :key="idx">
                {{ diet.name }}
              </v-btn>
            </v-btn-toggle>
          </div>
          <div class="filter-content__items">
            <v-btn-toggle v-model="vege">
              <v-btn text class="food-tag" v-for="(type, idx) in vegetarian" :key="idx">
                {{ type.name }}
              </v-btn>
            </v-btn-toggle>
          </div>
        </div>
        <div class="meal-filter-content__box">
          <div class="filter-content__title">{{ $t("type_category") }}</div>
          <div class="filter-content__items">
            <v-btn-toggle v-model="type" multiple>
              <v-btn text class="food-tag" v-for="(item, idx) in foodTypeCategories" :key="idx">
                {{ item.name }}
              </v-btn>
            </v-btn-toggle>
          </div>
        </div>
        <div class="meal-filter-content__box">
          <div class="filter-content__title">{{ $t("calories_filter") }}</div>
          <div class="slider-txt">
            <div class="slider-txt--ic">0</div>
            <div class="slider-txt--ic">500+</div>
          </div>
          <div class="filter-content__items">
            <div class="slider__wrapper">
              <v-range-slider
                v-model="calorie"
                color="#ededed"
                track-color="#fff"
                track-fill-color="#41d8e6"
                :min="0"
                :max="5"
                ticks="always"
                step="1"
              ></v-range-slider>
              <!-- <v-range-slider max="5" min="0"></v-range-slider> -->
            </div>
          </div>
        </div>
      </div>
      <div class="btn__wrapper">
        <div class="go-to-btn" @click="saveFilter">{{ $t("view_meals") }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      categories: [
        { en: "high_fat", name: this.$i18n.t("tag_high_fat") },
        { en: "low_carb", name: this.$i18n.t("tag_low_carb") },
        { en: "low_calorie", name: this.$i18n.t("tag_low_carl") },
        { en: "high_protein", name: this.$i18n.t("tag_high_protein") },
        { en: "low_sodium", name: this.$i18n.t("tag_low_sodium") },
        { en: "low_sugar", name: this.$i18n.t("tag_low_sugar") },
      ],
      vegetarian: [
        { en: "vegan", name: this.$i18n.t("tag_vegan") },
        { en: "lacto_ovo", name: this.$i18n.t("tag_lacto") },
        { en: "pesco", name: this.$i18n.t("tag_pesco") },
      ],
      foodTypeCategories: [
        { en: "korean", name: this.$i18n.t("tag_korean") },
        { en: "chinese", name: this.$i18n.t("tag_chinese") },
        { en: "japanese", name: this.$i18n.t("tag_japanese") },
        { en: "western", name: this.$i18n.t("tag_western") },
        { en: "snack", name: this.$i18n.t("tag_snack") },
        { en: "salad", name: this.$i18n.t("tag_salad") },
        { en: "etc", name: this.$i18n.t("tag_other") },
      ],
      calorie: [0, 0],
      diet: [],
      vege: "",
      type: [],
    };
  },
  watch: {
    diet(newVal) {
      // console.log(newVal);
    },
  },
  methods: {
    closeFilterModal() {
      this.$emit("closeFilterModal");
    },
    resetFilter() {
      this.calorie = [0, 0];
      this.diet = [];
      this.vege = "";
      this.type = [];
    },
    saveFilter() {
      const selected = { diet: this.diet, vege: this.vege, type: this.type, calorie: this.calorie };
      localStorage.setItem("foodFilter", JSON.stringify(selected));
      this.$emit("selectedFilterHandler");
      this.closeFilterModal();
    },
    getFilter() {
      const foodFilter = JSON.parse(localStorage.getItem("foodFilter"));
      // console.log(foodFilter);
      this.$nextTick(() => {
        if (foodFilter !== null) {
          this.diet = foodFilter.diet;
          this.vege = foodFilter.vege;
          this.type = foodFilter.type;
          this.calorie = foodFilter.calorie;
        }
      });
    },
  },
  created() {
    this.getFilter();
  },
  mounted() {
    this.getFilter();
  },
};
</script>

<style lang="scss" scoped>
.care-setting-modal__wrapper {
  padding: 10px 30px 20vh;
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999999;
  background: rgba(0, 0, 0, 0.81);
  opacity: 1;
  height: 93.5%;
  border-radius: 20px 20px 0px 0px;
  animation: showEditModal 0.7s forwards;
}

@keyframes showEditModal {
  0% {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}

.care-setting-modal__nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  // background: rgba(0, 0, 0, 0.7);
  border-radius: 20px 20px 0px 0px;
  // border-bottom: 1px solid #a7a7a7;
  div {
    color: #fff;
  }
}

.nav-item__canclebtn {
  font-size: 16px;
}

.filter-contents__wrapper {
  height: 100%;
  padding-bottom: 10vh;
  overflow: scroll;
}

.nav-item__title {
  font-size: 22px;
  font-weight: 500;
}

.care-setting-modal__contents {
  padding: 40px 50px;
}

.content-item__title {
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 29px;
  text-align: left;
  /* identical to box height */
  letter-spacing: -0.02em;
  /* WHT */
  color: #ffffff;
  padding-bottom: 10px;
}

.meal-filter-content__box {
  margin-top: 10px;
}
.filter-content__title {
  color: #fff;
  font-size: 18px;
  font-weight: 500;
  text-align: left;
  padding: 10px 0px;
}
// .filter-content__items {
//   text-align: left;
//   display: flex;
//   width: 100%;
// }
// .food-tag {
//   background-color: #f8f8f8;
//   border-radius: 20px;
//   // display: flex;
//   // flex-direction: column;
//   padding: 3px 13px;
//   font-size: 18px;
//   line-height: 25px;
//   // margin: 0 10px 8px 0px;
//   letter-spacing: -0.03em;
//   font-weight: 400;
//   font-style: normal;
// }

.slider-txt {
  display: flex;
  justify-content: space-between;
}

.slider-txt--ic {
  color: #fff;
  font-family: GilroyMedium;
  text-indent: 10px;
}
::v-deep .v-messages {
  display: none !important;
}
::v-deep .v-slider__tick {
  border-left: 3px solid #fff !important;
  height: 17px !important;
  position: absolute;
  left: 50%;
  margin-left: 0px;
  top: -7px !important;
  background-color: #fff !important;
}
::v-deep .v-slider__thumb {
  width: 25px !important;
  height: 25px !important;
  box-shadow: 2px 2px 5px rgb(0 0 0 / 20%) !important;
}

::v-deep .v-slider--horizontal .v-slider__ticks-container {
  height: 3px;
}

::v-deep .v-input__slot {
  padding-right: 15px;
  overflow: hidden;
}

// food tag btn styling

.v-btn-toggle > .v-btn.v-btn {
  opacity: 1;
  background-color: #f8f8f8;
  border-radius: 20px;
  padding: 3px 13px;
  font-size: 18px;
  line-height: 25px;
  margin: 0 10px 8px 0px;
  letter-spacing: -0.03em;
  font-weight: 400;
  font-style: normal;
}

::v-deep .v-btn-toggle > .v-btn.v-btn:first-child {
  border-radius: 20px !important;
}
::v-deep .v-btn-toggle > .v-btn.v-btn:last-child {
  border-radius: 20px !important;
}

::v-deep .v-btn:not(.v-btn--round).v-size--default {
  font-size: 16px !important;
  height: 32px !important;
  min-width: 64px !important;
  padding: 0 10px !important;
  margin: 0 10px 15px 0 !important;
}

::v-deep .v-item-group {
  // width: 100%;
  display: flex;
  flex-wrap: wrap;
  // background-color: #000000;
}

.theme--light.v-btn-toggle:not(.v-btn-toggle--group) {
  background: transparent;
}

::v-deep .theme--light.v-btn.v-btn--has-bg {
  opacity: 1 !important;
  border-radius: 20px;
  font-size: 18px;
  line-height: 25px;
  margin: 0 10px 8px 0px;
  letter-spacing: -0.03em;
  font-weight: 400;
}

::v-deep .v-btn--active::before {
  // background-color: #f8f8f8f8 !important;
  opacity: 0 !important;
}
// ::v-deep .v-item--active {
//   background-color: #c9f4f8 !important;
//   opacity: 1 !important;
// }
::v-deep .v-btn-toggle > .v-btn.v-btn--active {
  background-color: #c9f4f8 !important;
  opacity: 1 !important;
}

::v-deep .theme--light.v-btn-toggle:not(.v-btn-toggle--group) .v-btn.v-btn {
  border: none;
}

.btn__wrapper {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 50px;
  padding: 0 30px;
}
.go-to-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  background-color: #41d8e6;
  width: 100%;
  color: #fff;
  border-radius: 10px;
  line-height: 50px;
  font-size: 22px;
  font-weight: 700;
}
</style>
