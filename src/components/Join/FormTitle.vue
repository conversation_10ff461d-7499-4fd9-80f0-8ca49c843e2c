<template>
  <div>
    <div v-if="currentStateIdx === 3 && join"></div>
    <div
      v-else-if="!join"
      :class="isIos ? 'form-title__wrapper' : 'form-title__wrapper-android'"
    >
      {{ title[currentStateIdx + 4] }}
    </div>
    <div
      v-else
      :class="isIos ? 'form-title__wrapper' : 'form-title__wrapper-android'"
    >
      {{ title[currentStateIdx] }}
    </div>
  </div>
</template>

<script>
export default {
  props: {
    currentStateIdx: Number,
    join: Boolean,
  },
  mounted() {
    // console.log(this.currentStateIdx);
  },
  data() {
    return {
      title: [
        this.$i18n.t("comment_phone_cert_title"),
        this.$i18n.t("join_id_title"),
        this.$i18n.t("join_pwd_title"),
        this.$i18n.t("purpose_answer"),
        this.$i18n.t("join_terms_title"),
        this.$i18n.t("exercise_answer"),
        this.$i18n.t("chronic_answer"),
        this.$i18n.t("drinking_answer"),
        this.$i18n.t("smoking_answer"),
        this.$i18n.t("diet_answer"),
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.form-title__wrapper {
  width: 100%;
  font-weight: 500;
  font-size: 26px;
  text-align: center;
  padding: 30px 30px 0 30px;
}
.form-title__wrapper-android {
  width: 100%;
  font-weight: 500;
  font-size: 26px;
  text-align: center;
  padding: 20px 30px 0 30px;
}
</style>
