<template>
  <div class="bg-modal">
    <div class="modal__wrapper">
      <div class="alert-window">
        <div class="alert-window__content" v-html="content"></div>
        <div class="btn__wrapper">
          <div class="alert-window__btn" @click="closeModal">{{ $t("confirm_btn") }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    content: String,
    alert: String,
    isContent: Boolean,
  },
  methods: {
    closeModal() {
      this.$emit("isConfirmed", true);
    },
  },
};
</script>

<style lang="scss" scoped>
.bg-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 450px;
  height: 100%;
  z-index: 99999;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.modal__wrapper {
  display: flex;
  flex-direction: column;
}

.alert-window {
  background-color: #fff;
  width: 100%;
  max-height: 80vh;
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 15px 10px;
  text-align: center;
  display: flex;
}

.alert-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.alert-window__content {
  overflow-y: scroll;
  font-size: 14px;
  text-align: left;
  color: #323232;
  align-items: flex-end;
  height: 95%;
}

.btn__wrapper {
  width: 100%;
  display: flex;
  padding-top: 5px;
}

.alert-window__btn {
  width: 100%;
  // height: 30px;
  text-align: right;
  font-size: 16px;
  font-weight: 700;
  color: #41d8e6;
}
</style>
