<template>
  <div>
    <div :class="isIos ? 'form-header' : 'form-header-android'">
      <img src="@/assets/images_assets/icons/left-arrow-ic.png" @click="moveBack" class="moveBack-arrow-ic" />
      <v-progress-linear
        :value="returnProgressValue"
        background-color="#fff"
        color="#41d8e6"
        rounded
        height="6"
      ></v-progress-linear>
      <div class="blank-space"></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    currentStateIdx: Number,
    join: Boolean,
  },

  data() {
    return {
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },

  computed: {
    returnProgressValue() {
      return this.join ? (this.currentStateIdx + 1) * 20 : (this.currentStateIdx + 1) * 16.7;
    },
  },

  methods: {
    moveBack() {
      if (this.currentStateIdx === 0 && this.join) {
        this.$router.push({ path: "/login" });
      } else if (this.currentStateIdx === 0 && !this.join) {
        this.$router.push("/");
      }

      this.$emit("moveBack", 1);
    },
  },
};
</script>

<style lang="scss" scoped>
.form-header {
  display: flex;
  align-items: center;
  padding: 70px 30px 10px 30px;
  position: fixed;
  width: 100%;
  max-width: 450px !important;
}
.form-header-android {
  display: flex;
  align-items: center;
  padding: 50px 30px 10px 30px;
  position: fixed;
  width: 100%;
  max-width: 450px !important;
}

.moveBack-arrow-ic {
  margin-right: 1.875rem;
  width: 10px;
}

.blank-space {
  // margin-left: 1.25rem;
}

::v-deep .v-progress-linear {
  width: 75%;
}
</style>
