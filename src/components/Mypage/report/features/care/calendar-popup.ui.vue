<template>
  <div
    class="vc-popover-content-wrapper is-interactive"
    :style="popoverStyle"
    data-popper-placement="bottom-start"
  >
    <div
      tabindex="-1"
      class="vc-popover-content direction-bottom vc-nav-popover-container"
      @click.stop
    >
      <div class="vc-nav-container">
        <!-- Header with year navigation -->
        <div class="vc-nav-header">
          <span
            role="button"
            tabindex="0"
            class="vc-nav-arrow is-left"
            @click="navigateYear(-1)"
            @keydown.enter="navigateYear(-1)"
            @keydown.space.prevent="navigateYear(-1)"
          >
            <svg
              width="12px"
              height="12px"
              viewBox="0 -1 16 34"
              class="vc-svg-icon"
            >
              <path
                d="M11.196 10c0 0.143-0.071 0.304-0.179 0.411l-7.018 7.018 7.018 7.018c0.107 0.107 0.179 0.268 0.179 0.411s-0.071 0.304-0.179 0.411l-0.893 0.893c-0.107 0.107-0.268 0.179-0.411 0.179s-0.304-0.071-0.411-0.179l8.321-8.321c-0.107-0.107-0.179-0.268-0.179-0.411s0.071-0.304 0.179-0.411l0.893-0.893c0.107 0.107 0.179 0.25 0.179 0.411z"
              ></path>
            </svg>
          </span>

          <span
            role="button"
            tabindex="0"
            class="vc-nav-title vc-grid-focus"
            style="white-space: nowrap"
            @click="resetToCurrentYear"
          >
            {{ currentYear }}
          </span>

          <span
            role="button"
            tabindex="0"
            class="vc-nav-arrow is-right"
            @click="navigateYear(1)"
            @keydown.enter="navigateYear(1)"
            @keydown.space.prevent="navigateYear(1)"
          >
            <svg
              width="12px"
              height="12px"
              viewBox="-5 -1 16 34"
              class="vc-svg-icon"
            >
              <path
                d="M10.625 17.429c0 0.143-0.071 0.304-0.179 0.411l-8.321 8.321c-0.107 0.107-0.268 0.179-0.411 0.179s-0.304-0.071-0.411-0.179l-0.893-0.893c-0.107-0.107-0.179-0.25-0.179-0.411 0-0.143 0.071-0.304 0.179-0.411l7.018-7.018-7.018-7.018c-0.107-0.107-0.179-0.268-0.179-0.411s0.071-0.304 0.179-0.411l0.893-0.893c0.107-0.107 0.268-0.179 0.411-0.179s0.304 0.071 0.411 0.179l8.321 8.321c0.107 0.107 0.179 0.268 0.179 0.411z"
              ></path>
            </svg>
          </span>
        </div>

        <!-- Month navigation items -->
        <div class="vc-nav-items">
          <span
            v-for="month in months"
            :key="`${currentYear}.${month.value}`"
            role="button"
            :data-id="`${currentYear}.${month.value}`"
            :aria-label="`${month.name} ${currentYear}`"
            tabindex="0"
            class="vc-nav-item"
            :class="{
              'is-active': isActiveMonth(month.value),
              'is-current': isCurrentMonth(month.value),
            }"
            @click="selectMonth(month.value)"
            @keydown.enter="selectMonth(month.value)"
            @keydown.space.prevent="selectMonth(month.value)"
          >
            {{ month.name }}
          </span>
        </div>
      </div>

      <!-- Popover caret - Removed as per new design -->
      <div class="vc-caret"></div>
    </div>
  </div>
</template>

<script>
/**
 * Dynamic Calendar Navigation Component
 *
 * A reusable Vue 2 component for calendar month/year navigation
 * with keyboard accessibility and dynamic positioning
 *
 * @component CalendarNavigation
 * @example
 * <CalendarNavigation
 *   :isOpen="showCalendar"
 *   :position="{ x: 20, y: 41.5 }"
 *   :selectedDate="new Date()"
 *   :locale="'ko'"
 *   @month-selected="handleMonthSelected"
 *   @close="handleClose"
 * />
 */
export default {
  name: "CalendarNavigation",

  props: {
    /**
     * Currently selected date
     * @type {Date}
     * @default new Date()
     */
    selectedDate: {
      type: Date,
      default: () => new Date(),
    },

    /**
     * Locale for month names (supports 'ko', 'en')
     * @type {String}
     * @default 'ko'
     */
    locale: {
      type: String,
      default: "ko",
      validator: (value) => ["ko", "en"].includes(value),
    },

    /**
     * Minimum selectable year
     * @type {Number}
     * @default 1900
     */
    minYear: {
      type: Number,
      default: 1900,
    },

    /**
     * Maximum selectable year
     * @type {Number}
     * @default 2100
     */
    maxYear: {
      type: Number,
      default: 2100,
    },
  },

  data() {
    return {
      currentYear: new Date().getFullYear(),
      selectedMonth: new Date().getMonth() + 1,
      today: new Date(),
    };
  },

  computed: {
    /**
     * Generates month list based on locale
     * Time Complexity: O(1) - constant 12 months
     * @returns {Array<Object>} Array of month objects with name and value
     */
    months() {
      const monthNames =
        this.locale === "ko"
          ? [
              "1월",
              "2월",
              "3월",
              "4월",
              "5월",
              "6월",
              "7월",
              "8월",
              "9월",
              "10월",
              "11월",
              "12월",
            ]
          : [
              "Jan",
              "Feb",
              "Mar",
              "Apr",
              "May",
              "Jun",
              "Jul",
              "Aug",
              "Sep",
              "Oct",
              "Nov",
              "Dec",
            ];

      return monthNames.map((name, index) => ({
        name,
        value: String(index + 1).padStart(2, "0"),
      }));
    },

    /**
     * Calculates dynamic popover positioning styles
     * Time Complexity: O(1)
     * @returns {Object} CSS style object for positioning
     */
    popoverStyle() {
      return {
        position: "absolute",
        inset: "0px auto auto 0px",
        margin: "0px",
        transform: `translate3d(20px, 242.5px, 0px)`,
      };
    },
  },

  watch: {
    /**
     * Syncs component state when selectedDate prop changes
     * @param {Date} newDate - New selected date
     */
    selectedDate: {
      handler(newDate) {
        if (newDate instanceof Date && !isNaN(newDate)) {
          this.currentYear = newDate.getFullYear();
          this.selectedMonth = String(newDate.getMonth() + 1).padStart(2, "0");
        }
      },
      immediate: true,
    },
  },

  beforeDestroy() {
    // Cleanup event listener to prevent memory leaks
    document.removeEventListener("click", this.handleOutsideClick);
  },

  methods: {
    /**
     * Navigates year by specified direction with boundary validation
     * Time Complexity: O(1)
     * @param {Number} direction - Direction to navigate (-1 or 1)
     */
    navigateYear(direction) {
      const newYear = this.currentYear + direction;

      if (newYear >= this.minYear && newYear <= this.maxYear) {
        this.currentYear = newYear;
        this.$emit("year-changed", newYear);
      }
    },

    /**
     * Resets current year to today's year
     * Time Complexity: O(1)
     */
    resetToCurrentYear() {
      this.currentYear = this.today.getFullYear();
      this.$emit("year-changed", this.currentYear);
    },

    /**
     * Handles month selection and emits selected date
     * Time Complexity: O(1)
     * @param {String} monthValue - Selected month value (01-12)
     */
    selectMonth(monthValue) {
      this.selectedMonth = monthValue;
      const selectedDate = new Date(
        this.currentYear,
        parseInt(monthValue) - 1,
        1
      );

      /**
       * Emitted when a month is selected
       * @event month-selected
       * @param {Date} selectedDate - The selected date object
       * @param {Number} year - Selected year
       * @param {Number} month - Selected month (1-12)
       */
      this.$emit("month-selected", {
        date: selectedDate,
        year: this.currentYear,
        month: parseInt(monthValue),
      });

      this.$emit("close");
    },

    /**
     * Checks if month is currently active (selected)
     * Time Complexity: O(1)
     * @param {String} monthValue - Month value to check
     * @returns {Boolean} True if month is active
     */
    isActiveMonth(monthValue) {
      if (!this.selectedDate) return false;
      
      return (
        parseInt(monthValue) === this.selectedDate.getMonth() + 1 &&
        this.currentYear === this.selectedDate.getFullYear()
      );
    },

    /**
     * Checks if month is current month (today)
     * Time Complexity: O(1)
     * @param {String} monthValue - Month value to check
     * @returns {Boolean} True if month is current
     */
    isCurrentMonth(monthValue) {
      return (
        parseInt(monthValue) === this.today.getMonth() + 1 &&
        this.currentYear === this.today.getFullYear()
      );
    },

    /**
     * Handles clicks outside the component to close popover
     * Time Complexity: O(1)
     * @param {Event} event - Click event
     */
    handleOutsideClick(event) {
      if (!this.$el.contains(event.target)) {
        this.$emit("close");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/* Component Styles - Matching the turquoise design */
.vc-popover-content-wrapper {
  z-index: 1000;
  pointer-events: auto;
}

.vc-popover-content {
  background: #4dd0e1;
  border-radius: 17px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: none;
  overflow: hidden;
  position: relative;
}

.vc-popover-content::before {
  content: "";
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
  border-bottom: 12px solid #4dd0e1;
}

.vc-nav-popover-container {
  min-width: 140px;
  width: 140px;
  padding: 0;
}

.vc-nav-container {
  padding: 6px;
}

.vc-nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  margin-bottom: 0;
  border-bottom: none;
}

.vc-nav-title {
  font-weight: 700;
  font-size: 16px;
  color: white;
  cursor: pointer;
  padding: 0;
  border-radius: 0;
  transition: opacity 0.2s ease;
}

.vc-nav-title:hover {
  opacity: 0.8;
  background-color: transparent;
}

.vc-nav-arrow {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.vc-nav-arrow:focus {
  outline: 2px solid white;
  outline-offset: 2px;
}

.vc-svg-icon {
  fill: white;
  width: 12px;
  height: 12px;
}

.vc-nav-items {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-row-gap: 0;
  grid-column-gap: 0;
}

.vc-nav-item {
  width: 43px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  color: white;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  min-height: 24px;
  background: transparent;
  padding: 0;
}

.vc-nav-item:focus {
  outline: 2px solid white;
  outline-offset: 2px;
}

.vc-nav-item.is-active {
  background-color: white;
  color: #333;
  font-weight: 700;
}

.vc-nav-item.is-current {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 600;
}

/* Animation for smooth transitions */
.vc-popover-content-wrapper {
  animation: fadeInScale 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translate3d(20px, 35px, 0px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate3d(20px, 41.5px, 0px) scale(1);
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .vc-nav-popover-container {
  }

  .vc-nav-container {
    padding: 6px;
  }

  .vc-nav-item {
    min-height: 24px;
    font-size: 14px;
  }

  .vc-nav-title {
    font-size: 14px;
  }
}
</style>
