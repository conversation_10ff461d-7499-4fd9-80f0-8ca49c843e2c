<template>
  <nav class="category-item">
    <div class="category-icon-container">
      <img
        :src="require(`@/assets/images/care/main_${categoryKey}.png`)"
        :alt="`${categoryKey}_icon`"
        loading="lazy"
        class="category-icon"
      />
      <span class="category-title">{{ categoryType }}</span>
    </div>
    <div>
      <p class="value" v-if="categoryKey !== 'woman'">
        {{ categoryValue
        }}<span v-if="categoryUnit !== ''" class="unit">
          {{ categoryUnit }}
        </span>
      </p>
      <p class="unit" v-if="categoryKey === 'woman'">
        {{ categoryValue }}
      </p>
    </div>
  </nav>
</template>

<script>
export default {
  name: "CareCategory",

  props: {
    categoryType: {
      type: String,
      required: true,
      default: "",
    },

    categoryValue: {
      type: String,
      required: true,
      default: "",
    },

    categoryUnit: {
      type: String,
      required: false,
      default: "",
    },

    categoryKey: {
      type: String,
      required: true,
      default: "",
    },
  },

  data() {
    return {
      isStarted: false,
      periodStarted: false,
      isFutureDate: false,
      isEnded: false,
      isPeriod: false,
      isFertileWindow: false,
      formattedDate: null,
      days: null,
      nextStatus: "",
      remain: null,
      fertileWindow: null,
      isKo: true,
    };
  },

  computed: {},

  watch: {},

  mounted() {
    console.log("categoryValue",this.categoryKey, this.categoryValue);
  },
};
</script>

<style lang="scss" scoped>
.category-item {
  width: 100%;
  max-width: 450px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;

  &:last-child {
    margin-bottom: 0;
  }
}

.category-icon {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.category-icon-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.category-title {
  color: #646464;
  font-weight: bold;
  font-size: 16px;
}

.value {
  font-family: GilroyMedium !important;
  font-size: 28px;
  display: flex;
  align-items: flex-end;
  gap: 5px;

  .unit {
    font-family: GilroyMedium !important;
    margin-bottom: 5px;
  }
}

.unit {
  font-size: 16px;
  color: black;
}
</style>
