<template>
  <div class="care-calendar">
    <!-- Calendar Header -->
    <div class="calendar-header">
      <div class="month-year" @click="showMonthPicker = true">
        {{ currentYear }}.{{ String(currentMonth).padStart(2, "0") }}
        <span
          :class="isIos ? 'move-btn' : 'move-btn-android'"
          @click.stop="goToToday"
          >{{ $t("today") }}</span
        >
      </div>
      <div class="nav-buttons">
        <img
          :src="prevIcon"
          alt="link"
          @click="previousMonth"
          class="nav-btn"
        />
        <img :src="nextIcon" alt="link" @click="nextMonth" class="nav-btn" />
      </div>
    </div>

    <!-- Calendar Popup -->
    <CalendarPopup
      v-show="showMonthPicker"
      :selectedDate="selectedDate"
      :caretPosition="popupCaretPosition"
      :style="popupStyle"
      @month-selected="handleMonthSelected"
      @close="closeCalendarPopup"
    />

    <!-- Calendar Grid -->
    <div class="calendar-grid">
      <!-- Day Headers -->
      <div class="day-header" v-for="day in dayHeaders" :key="day">
        {{ day }}
      </div>

      <!-- Calendar Days -->
      <div
        v-for="date in calendarDays"
        :key="date.key"
        :class="[
          'calendar-day',
          {
            'other-month': !date.isCurrentMonth,
            selected: isDateSelected(date),
            'has-progress': date.calendarData,
          },
        ]"
        @click="selectDate(date, $event)"
      >
        <div class="day-content">
          <div
            v-if="date.calendarData && date.calendarData.progress"
            class="progress-donut"
            :class="{ 'animate-progress': shouldAnimate }"
            :style="getDonutStyle(date.calendarData.progress)"
            :data-progress="date.calendarData.progress"
            :key="`donut-${date.key}-${animationKey}`"
            ref="progressDonut"
          >
            <span class="day-number">{{ date.day }}</span>
          </div>
          <span v-else class="day-number">{{ date.day }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CalendarPopup from "./calendar-popup.ui.vue";
import { CalendarUtils } from "./utils/calendar.utils";
import { ProgressUtils } from "./utils/progress.utils";

export default {
  name: "CareCalendarTable",

  components: {
    CalendarPopup,
  },

  props: {
    selectedDate: {
      type: Date,
      required: true,
    },
    calendarData: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      currentDate: new Date(),
      showMonthPicker: false,
      showCalendarPopup: false,
      pickerYear: new Date().getFullYear(),
      dayHeaders: ["일", "월", "화", "수", "목", "금", "토"],
      clickedElement: null,
      popupCaretPosition: "top",
      popupStyle: {},
      shouldAnimate: false,
      animationKey: 0,
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
      platform:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1
          ? "ios"
          : "android",
      // Animation control properties - removed unused properties
      isInitialLoad: true,
    };
  },

  computed: {
    currentYear() {
      return this.currentDate.getFullYear();
    },
    currentMonth() {
      return this.currentDate.getMonth() + 1;
    },
    calendarDays() {
      return CalendarUtils.generateCalendarDays(
        this.currentYear,
        this.currentMonth,
        null, // selectedDate 종속성 제거
        this.calendarData
      );
    },
    selectedDateInfo() {
      if (!this.selectedDate) return null;

      const dateKey = CalendarUtils.formatDateKey(this.selectedDate);
      const dayData = this.calendarData.find((item) => item.date === dateKey);

      if (dayData) {
        return {
          hasData: true,
          summary: ProgressUtils.calculateDaySummary(dayData, this.platform),
        };
      }

      return {
        hasData: false,
      };
    },
    isPrevData() {
      return true;
    },
    isNextData() {
      const today = new Date();
      const currentViewMonth = new Date(
        this.currentYear,
        this.currentMonth - 1,
        1
      );
      const todayMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const nextMonth = new Date(currentViewMonth);
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      return nextMonth <= todayMonth;
    },
    prevIcon() {
      return this.isPrevData
        ? require("@/assets/images_assets/icons/prev_chevron.png")
        : require("@/assets/images_assets/icons/prev_btn.png");
    },
    nextIcon() {
      return this.isNextData
        ? require("@/assets/images_assets/icons/next_chevron.png")
        : require("@/assets/images_assets/icons/next_btn.png");
    },
  },

  watch: {
    calendarData: {
      handler(newData, oldData) {
        // calendarData가 변경될 때만 애니메이션 실행
        if (newData && newData.length > 0) {
          this.$nextTick(() => {
            this.triggerProgressAnimations(this.calendarDays);
          });
        }
      },
      deep: true,
    },
  },

  mounted() {
    this.pickerYear = this.currentYear;
    this.dayHeaders = this.getDays();

    // Initial load animation delay
    this.$nextTick(() => {
      setTimeout(() => {
        this.isInitialLoad = false;
      }, 100);
    });
  },

  methods: {
    getDays() {
      const days = {
        ko: ["일", "월", "화", "수", "목", "금", "토"],
        en: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
      };
      return this.$i18n.locale === "ko" ? days.ko : days.en;
    },

    /**
     * 날짜가 선택된 날짜인지 확인
     * @param {Object} date - 캘린더 날짜 객체
     * @returns {boolean} 선택된 날짜 여부
     */
    isDateSelected(date) {
      if (!date.isCurrentMonth || !this.selectedDate || !date.date) {
        return false;
      }

      return (
        date.date.getFullYear() === this.selectedDate.getFullYear() &&
        date.date.getMonth() === this.selectedDate.getMonth() &&
        date.date.getDate() === this.selectedDate.getDate()
      );
    },

    /**
     * Determines if a date should have animated progress (simplified)
     * @param {Object} date - Calendar date object
     * @returns {boolean} Always true for consistent animation
     */
    shouldAnimateProgress(date) {
      return true; // Always animate for consistent UX
    },

    /**
     * Triggers progressive fill animations for donut charts
     * Each donut animates from 0% to actual progress value
     * @param {Array} days - Array of calendar day objects
     */
    triggerProgressAnimations(days) {
      this.$nextTick(() => {
        const progressDays = days.filter(
          (day) =>
            day.isCurrentMonth && day.calendarData && day.calendarData.progress
        );

        progressDays.forEach((day, index) => {
          // Find the corresponding DOM element
          const dayElements = this.$el.querySelectorAll(".calendar-day");
          const dayElement = Array.from(dayElements).find((el) => {
            const dayNumber = el.querySelector(".day-number");
            return (
              dayNumber && dayNumber.textContent.trim() === day.day.toString()
            );
          });

          if (dayElement) {
            const donutElement = dayElement.querySelector(".progress-donut");
            if (donutElement) {
              // Stagger animations with delay
              setTimeout(() => {
                const targetAngle = (day.calendarData.progress / 100) * 360;
                this.animateDonutProgress(donutElement, targetAngle);
              }, index * 100); // 100ms stagger delay
            }
          }
        });
      });
    },

    /**
     * Animates a single donut chart from 0 to target angle
     * @param {HTMLElement} element - The donut element to animate
     * @param {number} targetAngle - Target angle in degrees
     */
    animateDonutProgress(element, targetAngle) {
      const duration = 1200; // 1.2 seconds
      const startTime = performance.now();
      const startAngle = 0;

      // Easing function for smooth animation (ease-out-cubic)
      const easeOutCubic = (t) => 1 - Math.pow(1 - t, 3);

      const animate = (currentTime) => {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Apply easing
        const easedProgress = easeOutCubic(progress);
        const currentAngle =
          startAngle + (targetAngle - startAngle) * easedProgress;

        // Update CSS custom property and background
        element.style.setProperty("--current-angle", `${currentAngle}deg`);
        element.style.background = `conic-gradient(
          var(--progress-color) 0deg ${currentAngle}deg, 
          transparent ${currentAngle}deg 360deg
        )`;

        // Continue animation if not complete
        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };

      // Start animation
      requestAnimationFrame(animate);
    },

    previousMonth() {
      if (!this.isPrevData) {
        console.warn("Previous month navigation is disabled");
        return;
      }

      this.currentDate = CalendarUtils.addMonths(this.currentDate, -1);

      // selectedDate를 변경된 월의 첫 번째 날로 업데이트
      const newSelectedDate = new Date(
        this.currentDate.getFullYear(),
        this.currentDate.getMonth(),
        1
      );
      this.$emit("update:selectedDate", newSelectedDate);

      // 월 변경 이벤트 emit
      this.$emit("month-changed", {
        year: this.currentDate.getFullYear(),
        month: this.currentDate.getMonth() + 1,
      });
    },

    nextMonth() {
      if (!this.isNextData) {
        console.error("Cannot navigate to future months");
        return;
      }

      this.currentDate = CalendarUtils.addMonths(this.currentDate, 1);

      // selectedDate를 변경된 월의 첫 번째 날로 업데이트
      const newSelectedDate = new Date(
        this.currentDate.getFullYear(),
        this.currentDate.getMonth(),
        1
      );
      this.$emit("update:selectedDate", newSelectedDate);

      // 월 변경 이벤트 emit
      this.$emit("month-changed", {
        year: this.currentDate.getFullYear(),
        month: this.currentDate.getMonth() + 1,
      });
    },

    goToToday() {
      const today = new Date();
      const oldMonth = this.currentDate.getMonth();
      const oldYear = this.currentDate.getFullYear();

      this.currentDate = new Date(today);
      this.$emit("update:selectedDate", today);

      // 월이 변경되었는지 확인하고 emit
      if (oldYear !== today.getFullYear() || oldMonth !== today.getMonth()) {
        this.$emit("month-changed", {
          year: today.getFullYear(),
          month: today.getMonth() + 1,
        });
      }
    },

    selectDate(date, event) {
      console.log("selectDate called:", date, event);

      if (!date.isCurrentMonth) {
        console.log("Date is not in current month, returning");
        return;
      }

      const newSelectedDate = new Date(
        this.currentYear,
        this.currentMonth - 1,
        date.day
      );

      // 추가 데이터와 함께 emit
      const dateInfo = {
        selectedDate: newSelectedDate,
        calendarData: date.calendarData,
        dayNumber: date.day,
        hasProgress: !!date.calendarData,
        progressValue: date.calendarData?.progress || 0,
      };

      console.log("Setting popup state:", {
        clickedElement: event.currentTarget,
        showCalendarPopup: true,
        dateInfo,
      });

      this.clickedElement = event.currentTarget;
      this.calculatePopupPosition();
      this.showCalendarPopup = true;

      console.log("showCalendarPopup after setting:", this.showCalendarPopup);

      this.$emit("update:selectedDate", newSelectedDate);
      this.$emit("date-clicked", dateInfo); // 추가 데이터 emit
    },

    selectMonth(month) {
      const oldMonth = this.currentDate.getMonth();
      const oldYear = this.currentDate.getFullYear();

      this.currentDate = new Date(this.pickerYear, month - 1, 1);
      this.showMonthPicker = false;

      // 월이 변경되었는지 확인하고 emit
      if (oldYear !== this.pickerYear || oldMonth !== month - 1) {
        this.$emit("month-changed", {
          year: this.pickerYear,
          month: month,
        });
      }
    },

    changeYear(delta) {
      this.pickerYear += delta;
    },

    formatSelectedDate(date) {
      return CalendarUtils.formatDisplayDate(date);
    },

    getProgressItems(progressData) {
      return ProgressUtils.getProgressItems(progressData, this.platform);
    },

    /**
     * Generates animated donut chart style with progressive fill animation
     * @param {number} progress - Progress percentage (0-100)
     * @returns {Object} CSS style object with animation support
     */
    getDonutStyle(progress) {
      const percentage = Math.min(Math.max(progress, 0), 100);
      const targetAngle = (percentage / 100) * 360;

      // Progress color
      let color = "#41D8E6";

      return {
        // Set target angle for animation
        "--target-angle": `${targetAngle}deg`,
        "--progress-color": color,
        // Start with 0 progress, will animate to target
        "--current-angle": "0deg",
      };
    },

    calculatePopupPosition() {
      if (!this.clickedElement) return;

      const rect = this.clickedElement.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const popupWidth = 320;
      const popupHeight = 400;

      let left = rect.left + rect.width / 2 - popupWidth / 2;
      let top = rect.bottom + 10;
      let caretPosition = "top";

      // Boundary checks
      if (left + popupWidth > viewportWidth - 20) {
        left = viewportWidth - popupWidth - 20;
      }
      if (left < 20) {
        left = 20;
      }
      if (top + popupHeight > viewportHeight - 20) {
        top = rect.top - popupHeight - 10;
        caretPosition = "bottom";
      }
      if (top < 20) {
        top = 20;
        caretPosition = "left";
        left = rect.right + 10;
        if (left + popupWidth > viewportWidth - 20) {
          left = rect.left - popupWidth - 10;
          caretPosition = "right";
        }
      }

      this.popupStyle = {
        position: "fixed",
        left: `${left}px`,
        top: `${top}px`,
        zIndex: 1000,
      };

      this.popupCaretPosition = caretPosition;
    },

    /**
     * 달력 팝업에서 월이 선택되었을 때 처리
     * @param {Object} monthData - 선택된 월 데이터 {date, year, month}
     */
    handleMonthSelected(monthData) {
      console.log("Month selected from popup:", monthData);
      
      // 현재 날짜를 선택된 월의 첫 번째 날로 업데이트
      const newDate = new Date(monthData.year, monthData.month - 1, 1);
      this.currentDate = newDate;
      
      // selectedDate도 업데이트
      this.$emit("update:selectedDate", newDate);
      
      // 월 변경 이벤트 emit (부모 컴포넌트에서 API 호출)
      this.$emit("month-changed", {
        year: monthData.year,
        month: monthData.month,
      });
      
      this.closeCalendarPopup();
    },

    handleDateSelected(selectedDate) {
      this.$emit("update:selectedDate", selectedDate);
      this.closeCalendarPopup();
    },

    closeCalendarPopup() {
      this.showMonthPicker = false;
      this.showCalendarPopup = false;
      this.clickedElement = null;
      this.popupStyle = {};
    },
  },
};
</script>

<style lang="scss" scoped>
.care-calendar {
  max-width: 400px;
  padding: 30px 20px;
  margin-bottom: 40px;
  background: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.month-year {
  font-family: GilroyMedium !important;
  font-size: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  margin-left: 15px;
}

.move-btn {
  z-index: 99;
  border: 1px solid #ededed;
  color: #858585;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  line-height: 16px;
  padding: 0 9px;
  height: 28px;
  display: grid;
  place-content: center;
}

.move-btn-android {
  z-index: 99;
  border: 1px solid #ededed;
  color: #858585;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  line-height: 16px;
  padding: 0 9px;
  height: 28px;
  display: grid;
  place-content: center;

  &:active {
    background-color: #ededed;
  }
}

.nav-buttons {
  display: flex;
  gap: 24px;
}

.nav-btn {
  width: 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  border-radius: 8px;
  overflow: hidden;
}

.day-header {
  background: white;
  padding: 0 4px;
  text-align: center;
  font-size: 14px;
  letter-spacing: -5%;
  color: #646464;
}

.calendar-day {
  min-height: 48px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4px;
  cursor: pointer;
  position: relative;
  transition: background-color 0.2s ease;

  &.other-month {
    color: #ccc;
    cursor: default;
  }

  &.today {
    background: #e3f2fd;
  }

  &.selected {
    .day-number {
      width: 24px;
      height: 24px;
      background: #c9f4f8;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }
  }
}

.day-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.progress-donut {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  /* CSS custom properties for animation */
  --current-angle: 0deg;
  --target-angle: 0deg;
  --progress-color: #41d8e6;

  /* Initial state - no progress visible */
  background: conic-gradient(
    var(--progress-color) 0deg var(--current-angle),
    transparent var(--current-angle) 360deg
  );

  /* Smooth transitions for transform effects */
  transition: transform 0.15s ease;

  /* Inner circle background */
  &::before {
    content: "";
    position: absolute;
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    z-index: 1;
    transition: all 0.3s ease;
  }
}

/* Remove old keyframe animations - using JS animation instead */

.progress-donut .day-number {
  font-family: GilroyMedium !important;
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  z-index: 2;
  position: relative;
  transition: all 0.3s ease;
}

.day-number {
  font-family: GilroyMedium !important;
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  margin-bottom: 2px;
  transition: color 0.2s ease;
}

/* Remove calendar day entrance animations - keep only chart animations */

.progress-indicators {
  display: flex;
  gap: 2px;
  flex-wrap: wrap;
  justify-content: center;
}

.progress-item {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.selected-date-info {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.selected-date-info h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

.data-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-label {
  font-size: 14px;
  color: #666;
}

.item-value {
  font-size: 14px;
  font-weight: 500;
}

.no-data {
  text-align: center;
  color: #999;
}

.month-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.month-picker {
  background: white;
  border-radius: 12px;
  padding: 20px;
  min-width: 280px;
}

.year-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.year-nav {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
}

.current-year {
  font-size: 18px;
  font-weight: 600;
}

.months-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.month-btn {
  padding: 12px;
  border: none;
  background: #f5f5f5;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background: #e5e5e5;
  }

  &.active {
    background: #4fc3f7;
    color: white;
  }
}
</style>
