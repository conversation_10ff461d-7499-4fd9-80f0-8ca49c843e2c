<template>
  <div class="category-list">
    <CareCategory
      v-for="category in filteredCategories"
      :key="category.categoryKey"
      :categoryType="category.categoryType"
      :categoryValue="categoryValues[category.categoryKey]"
      :categoryUnit="category.categoryUnit"
      :categoryKey="category.categoryKey"
    />
  </div>
</template>

<script>
import CareCategory from "./care-category.ui.vue";

import { CARE_CATEGORIES } from "./constants/care-category.constants";

export default {
  name: "CareCategoryList",

  components: {
    CareCategory,
  },

  props: {
    selectedDate: {
      type: Date,
      required: true,
      validator(value) {
        return value instanceof Date && !isNaN(value.getTime());
      },
    },
    detailData: {
      type: Object,
      required: true,
      default: () => ({
        data: {
          weight: [],
          water: [],
          urine: [],
          bloodPressure: [],
          step: null,
          woman: null,
        },
        menstruation: null,
      }),
      validator(value) {
        // detailData 구조 검증
        return (
          value &&
          typeof value === "object" &&
          value.data &&
          typeof value.data === "object"
        );
      },
    },
  },

  data() {
    return {
      CARE_CATEGORIES,
      categoryValues: {
        weight: "0",
        water: "0",
        pee: "0",
        bloodPressure: "0/0",
        step: "0",
        woman: "-",
      },
    };
  },

  computed: {
    /**
     * 현재 기기가 iOS인지 확인
     * @returns {boolean} iOS 여부
     */
    isIOS() {
      const userAgent = navigator.userAgent.toLowerCase();
      return (
        userAgent.indexOf("iphone") > -1 ||
        userAgent.indexOf("ipad") > -1 ||
        userAgent.indexOf("ipod") > -1
      );
    },

    /**
     * 기기 타입에 따라 필터링된 카테고리 목록
     * @returns {Array} 필터링된 카테고리 배열
     */
    filteredCategories() {
      if (this.isIOS) {
        // iOS일 때는 step 카테고리 제외
        return this.CARE_CATEGORIES.filter(
          (category) => category.categoryKey !== "step"
        );
      }
      // AOS일 때는 모든 카테고리 표시
      return this.CARE_CATEGORIES;
    },
  },

  methods: {
    /**
     * 안전한 데이터 접근을 위한 헬퍼 함수
     * @param {any} obj - 접근할 객체
     * @param {string} path - 접근 경로 (예: 'data.weight')
     * @param {any} defaultValue - 기본값
     * @returns {any} 안전하게 접근된 값
     */
    safeGet(obj, path, defaultValue = null) {
      try {
        return path.split(".").reduce((current, key) => {
          return current && current[key] !== undefined
            ? current[key]
            : defaultValue;
        }, obj);
      } catch (error) {
        console.warn(`Safe get failed for path: ${path}`, error);
        return defaultValue;
      }
    },

    /**
     * 배열인지 안전하게 확인
     * @param {any} value - 확인할 값
     * @returns {boolean} 배열 여부
     */
    isValidArray(value) {
      return Array.isArray(value) && value.length >= 0;
    },

    /**
     * 숫자 값인지 안전하게 확인
     * @param {any} value - 확인할 값
     * @returns {boolean} 숫자 여부
     */
    isValidNumber(value) {
      return typeof value === "number" && !isNaN(value);
    },

    checkDetailData(detail, key) {
      if (!detail) return this.getDefaultValue(key);

      try {
        switch (key) {
          case "bloodPressure": {
            if (!this.isValidArray(detail)) return "0/0";

            const firstValidData = detail.find(
              (item) =>
                item &&
                typeof item === "object" &&
                this.isValidNumber(item.systolic) &&
                this.isValidNumber(item.diastolic)
            );

            return firstValidData
              ? `${firstValidData.systolic}/${firstValidData.diastolic}`
              : "0/0";
          }

          case "step": {
            if (!detail || typeof detail !== "object") return "0";

            const steps = this.safeGet(detail, "totalStepCount", 0);
            return this.isValidNumber(steps)
              ? steps.toLocaleString("ko-KR")
              : "0";
          }

          case "woman":
            return this.calculateMenstruationStatus(
              this.detailData.menstruation,
              this.$i18n.locale.includes("ko") ? "ko" : "en"
            );

          case "urine": {
            return this.isValidArray(detail) ? String(detail.length) : "0";
          }

          default: {
            if (!this.isValidArray(detail)) return "0";

            const sum = detail.reduce((total, item) => {
              const value = this.safeGet(item, "value", 0);
              return total + (this.isValidNumber(value) ? value : 0);
            }, 0);

            return sum.toLocaleString("ko-KR");
          }
        }
      } catch (error) {
        console.error(`Error processing ${key} data:`, error);
        return this.getDefaultValue(key);
      }
    },

    /**
     * 키에 따른 기본값 반환
     * @param {string} key - 데이터 키
     * @returns {string} 기본값
     */
    getDefaultValue(key) {
      const defaults = {
        bloodPressure: "0/0",
        step: "0",
        woman: "-",
        urine: "0",
        default: "0",
      };
      return defaults[key] || defaults.default;
    },

    /**
     * 날짜 문자열을 Date 객체로 변환
     * @param {string} dateString - YYYY-MM-DD 형식의 날짜 문자열
     * @returns {Date|null} Date 객체 또는 null
     */
    parseDate(dateString) {
      if (!dateString || typeof dateString !== "string") return null;
      const date = new Date(dateString);
      return isNaN(date.getTime()) ? null : date;
    },

    /**
     * 두 날짜 간의 차이를 일 단위로 계산
     * @param {Date} date1 - 첫 번째 날짜
     * @param {Date} date2 - 두 번째 날짜
     * @returns {number} 일 차이 (date2 - date1)
     */
    getDaysDifference(date1, date2) {
      if (!date1 || !date2) return 0;
      const timeDiff = date2.getTime() - date1.getTime();
      return Math.ceil(timeDiff / (1000 * 3600 * 24));
    },

    /**
     * Calculate menstruation status with internationalization support
     * All logic contained in a single method for simplicity
     * Time complexity: O(1) - all operations are constant time
     * @param {Object} menstruationData - Menstruation cycle data
     * @param {string} [locale] - Locale for translation (defaults to this.$i18n.locale or 'ko')
     * @returns {string} Localized menstruation status
     */
    calculateMenstruationStatus(menstruationData, locale = null) {
      try {
        // Translation templates - embedded for single method approach
        const translations = {
          ko: {
            menstruation_day: "생리 {day}일째",
            fertile_day: "가임기 {day}일",
            before_fertile: "가임기 {day}일전",
            before_menstruation: "생리 {day}일전",
            unknown: "-",
          },
          en: {
            menstruation_day: "Period Day {day}",
            fertile_day: "Fertile Day {day}",
            before_fertile: "{day} days before fertile window",
            before_menstruation: "{day} days before period",
            unknown: "-",
          },
          ja: {
            menstruation_day: "生理{day}日目",
            fertile_day: "妊娠可能期{day}日目",
            before_fertile: "妊娠可能期{day}日前",
            before_menstruation: "生理{day}日前",
            unknown: "-",
          },
          zh: {
            menstruation_day: "月经第{day}天",
            fertile_day: "排卵期第{day}天",
            before_fertile: "排卵期前{day}天",
            before_menstruation: "月经前{day}天",
            unknown: "-",
          },
        };

        // Get current locale with fallback chain
        const currentLocale = locale || this.$i18n?.locale || "ko";
        const t = translations[currentLocale] || translations.ko;

        // Helper function: format translation with day number
        const formatText = (template, day) => template.replace("{day}", day);

        // Input validation
        if (!menstruationData || typeof menstruationData !== "object") {
          return t.unknown;
        }

        const currentDate = new Date(this.selectedDate);
        currentDate.setHours(0, 0, 0, 0); // 시간 정규화

        // 현재 생리 기간 확인
        const currentMenstruation = menstruationData.menstruation;
        if (currentMenstruation) {
          const currentStart = this.parseDate(currentMenstruation.startDate);
          const currentEnd = this.parseDate(currentMenstruation.endDate);

          if (
            currentStart &&
            currentEnd &&
            currentDate >= currentStart &&
            currentDate <= currentEnd
          ) {
            const dayOfPeriod =
              this.getDaysDifference(currentStart, currentDate) + 1;
            return formatText(t.menstruation_day, dayOfPeriod);
          }
        }

        // 가임기 확인
        const fertileWindow = menstruationData.fertileWindow;
        if (fertileWindow) {
          const fertileStart = this.parseDate(fertileWindow.startDate);
          const fertileEnd = this.parseDate(fertileWindow.endDate);

          if (fertileStart && fertileEnd) {
            // 가임기 중인지 확인
            if (currentDate >= fertileStart && currentDate <= fertileEnd) {
              const dayOfFertile =
                this.getDaysDifference(fertileStart, currentDate) + 1;
              return formatText(t.fertile_day, dayOfFertile);
            }

            // 가임기 전인지 확인
            if (currentDate < fertileStart) {
              const daysToFertile = this.getDaysDifference(
                currentDate,
                fertileStart
              );
              return formatText(t.before_fertile, daysToFertile);
            }
          }
        }

        // 다음 생리 예정일 확인
        const nextMenstruation = menstruationData.nextMenstruation;
        if (nextMenstruation) {
          const nextStart = this.parseDate(nextMenstruation.startDate);

          if (nextStart && currentDate < nextStart) {
            const daysToNext = this.getDaysDifference(currentDate, nextStart);
            return formatText(t.before_menstruation, daysToNext);
          }
        }

        return t.unknown;
      } catch (error) {
        console.error("Error calculating menstruation status:", error);
        return "-";
      }
    },
    init(newData) {
      // 데이터가 없을 때
      if (!newData.data || Object.keys(newData.data).length === 0) {
        return;
      }

      const data = { ...newData.data, woman: newData.menstruation };

      Object.entries(data).forEach(([key, value]) => {
        const detailData = this.checkDetailData(value, key);

        if (detailData) {
          if (key === "urine") {
            this.categoryValues["pee"] = detailData;
          } else {
            this.categoryValues[key] = detailData;
          }
        }
      });
    },
  },

  watch: {
    detailData: {
      handler(newData) {
        console.log("watch triggered:", newData);
        this.init(newData);
      },
      deep: true,
      immediate: true, // 컴포넌트 생성 시 즉시 실행
    },
  },

  mounted() {
    // detailData가 실제 데이터를 가지고 있는지 확인
    if (this.detailData.data && Object.keys(this.detailData.data).length > 0) {
      // 기본값이 아닌 실제 데이터가 있는지 확인
      const hasRealData = Object.values(this.detailData.data).some(
        (value) =>
          value !== "0" &&
          value !== null &&
          value !== undefined &&
          (Array.isArray(value) ? value.length > 0 : true)
      );

      if (hasRealData) {
        this.init(this.detailData);
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.category-list {
  width: 100%;
  max-width: 450px;
  height: auto;
  padding: 30px;
  background: #ffffff;
}
</style>
