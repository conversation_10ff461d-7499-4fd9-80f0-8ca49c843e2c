import i18n from "@/i18n";

export const ProgressUtils = {
  /**
   * Get progress items based on platform
   */
  getProgressItems(progressData, platform) {
    const baseItems = [
      {
        type: "weight",
        label: i18n.t("weight_title"),
        color: "#ff6b6b",
        key: "weight",
      },
      {
        type: "water",
        label: i18n.t("water_title"),
        color: "#4ecdc4",
        key: "water",
      },
      {
        type: "exercise",
        label: i18n.t("exercise_title"),
        color: "#45b7d1",
        key: "exercise",
      },
      {
        type: "blood",
        label: i18n.t("bloodPressure_title"),
        color: "#96ceb4",
        key: "bloodPressure",
      },
    ];

    // Add steps for AOS platform only
    if (platform === "aos") {
      baseItems.push({
        type: "steps",
        label: i18n.t("step_title"),
        color: "#feca57",
        key: "steps",
      });
    }

    return baseItems.map((item) => ({
      ...item,
      completed:
        progressData[item.key] !== undefined && progressData[item.key] !== null,
    }));
  },

  /**
   * Calculate daily summary for selected date
   */
  calculateDaySummary(dayData, platform) {
    const summaryItems = [];

    if (dayData.weight) {
      summaryItems.push({
        type: "weight",
        label: i18n.t("weight_title"),
        value: `${dayData.weight}kg`,
      });
    }

    if (dayData.water) {
      summaryItems.push({
        type: "water",
        label: i18n.t("water_title"),
        value: `${dayData.water}ml`,
      });
    }

    if (dayData.exercise) {
      summaryItems.push({
        type: "exercise",
        label: i18n.t("exercise_title"),
        value: `${dayData.exercise}분`,
      });
    }

    if (dayData.bloodPressure) {
      summaryItems.push({
        type: "blood",
        label: "bloodPressure_title",
        value: `${dayData.bloodPressure.systolic}/${dayData.bloodPressure.diastolic}`,
      });
    }

    // Add steps for AOS platform only
    if (platform === "aos" && dayData.steps) {
      summaryItems.push({
        type: "steps",
        label: "step_title",
        value: `${dayData.steps.toLocaleString()}보`,
      });
    }

    return summaryItems;
  },

  /**
   * Calculate progress percentage for a day
   */
  calculateProgressPercentage(dayData, platform) {
    const totalItems = platform === "aos" ? 5 : 4;
    const completedItems = this.getProgressItems(dayData, platform).filter(
      (item) => item.completed
    ).length;

    return Math.round((completedItems / totalItems) * 100);
  },
};
