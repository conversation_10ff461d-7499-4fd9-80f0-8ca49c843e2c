export const ProgressUtils = {
  /**
   * Get progress items based on platform
   */
  getProgressItems(progressData, platform) {
    const baseItems = [
      { type: "weight", label: "체중", color: "#ff6b6b", key: "weight" },
      { type: "water", label: "수분", color: "#4ecdc4", key: "water" },
      { type: "exercise", label: "운동", color: "#45b7d1", key: "exercise" },
      { type: "blood", label: "혈압", color: "#96ceb4", key: "bloodPressure" },
    ];

    // Add steps for AOS platform only
    if (platform === "aos") {
      baseItems.push({
        type: "steps",
        label: "걸음",
        color: "#feca57",
        key: "steps",
      });
    }

    return baseItems.map((item) => ({
      ...item,
      completed:
        progressData[item.key] !== undefined && progressData[item.key] !== null,
    }));
  },

  /**
   * Calculate daily summary for selected date
   */
  calculateDaySummary(dayData, platform) {
    const summaryItems = [];

    if (dayData.weight) {
      summaryItems.push({
        type: "weight",
        label: "체중",
        value: `${dayData.weight}kg`,
      });
    }

    if (dayData.water) {
      summaryItems.push({
        type: "water",
        label: "수분",
        value: `${dayData.water}ml`,
      });
    }

    if (dayData.exercise) {
      summaryItems.push({
        type: "exercise",
        label: "운동",
        value: `${dayData.exercise}분`,
      });
    }

    if (dayData.bloodPressure) {
      summaryItems.push({
        type: "blood",
        label: "혈압",
        value: `${dayData.bloodPressure.systolic}/${dayData.bloodPressure.diastolic}`,
      });
    }

    // Add steps for AOS platform only
    if (platform === "aos" && dayData.steps) {
      summaryItems.push({
        type: "steps",
        label: "걸음",
        value: `${dayData.steps.toLocaleString()}보`,
      });
    }

    return summaryItems;
  },

  /**
   * Calculate progress percentage for a day
   */
  calculateProgressPercentage(dayData, platform) {
    const totalItems = platform === "aos" ? 5 : 4;
    const completedItems = this.getProgressItems(dayData, platform).filter(
      (item) => item.completed
    ).length;

    return Math.round((completedItems / totalItems) * 100);
  },
};
