export const CalendarUtils = {
  /**
   * Generates calendar grid with dynamic week count (5 or 6 weeks)
   * Time complexity: O(35) or O(42) depending on month structure
   * Space complexity: O(35) or O(42) for storing the day objects
   *
   * @param {number} year - Target year
   * @param {number} month - Target month (1-12)
   * @param {Date|null} selectedDate - Currently selected date (optional)
   * @param {Array} calendarData - Array containing calendar data with { date: string, progress: number } format
   * @returns {Array} Array of calendar day objects (35 or 42 items depending on month)
   */
  generateCalendarDays(year, month, selectedDate, calendarData = []) {
    const firstDay = new Date(year, month - 1, 1);
    const lastDayOfMonth = new Date(year, month, 0).getDate();
    const today = new Date();

    // Calculate the starting date (Sunday of the first week)
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - startDate.getDay());

    // Determine if we need 6 weeks or 5 weeks
    const lastDay = new Date(year, month - 1, lastDayOfMonth);
    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));

    // Calculate total days needed
    const totalDays =
      Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
    const weeksNeeded = totalDays === 35 ? 5 : 6;
    const daysToGenerate = weeksNeeded * 7;

    const days = [];
    const current = new Date(startDate);

    // Generate calendar days based on calculated weeks
    for (let i = 0; i < daysToGenerate; i++) {
      const isCurrentMonth = current.getMonth() === month - 1;

      if (isCurrentMonth) {
        // Current month: create full day object with Date instance
        const dateKey = this.formatDateKey(current);
        const isToday = this.isSameDate(current, today);
        const isSelected = selectedDate
          ? this.isSameDate(current, selectedDate)
          : false;

        // Find data for this date in the array
        const dayData = calendarData.find(item => item.date === dateKey) || null;

        days.push({
          key: `${current.getFullYear()}-${current.getMonth()}-${current.getDate()}`,
          day: current.getDate(),
          date: new Date(current), // Real Date object for current month
          isCurrentMonth: true,
          isToday,
          isSelected,
          calendarData: dayData,
        });
      } else {
        // Previous/Next month: create empty slot with empty string date
        days.push({
          key: `empty-${i}`, // Unique key for empty slots
          day: "", // Empty day number
          date: "", // Empty string instead of Date object
          isCurrentMonth: false,
          isToday: false,
          isSelected: false,
          calendarData: null,
        });
      }

      current.setDate(current.getDate() + 1);
    }

    return days;
  },

  /**
   * Add months to a date
   */
  addMonths(date, months) {
    const result = new Date(date);
    result.setMonth(result.getMonth() + months);
    return result;
  },

  /**
   * Check if two dates are the same day
   */
  isSameDate(date1, date2) {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  },

  /**
   * Format date as key for data lookup
   */
  formatDateKey(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  },

  /**
   * Format date for display
   */
  formatDisplayDate(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const dayNames = ["일", "월", "화", "수", "목", "금", "토"];
    const dayName = dayNames[date.getDay()];

    return `${year}.${String(month).padStart(2, "0")}.${String(day).padStart(
      2,
      "0"
    )} (${dayName})`;
  },
};
