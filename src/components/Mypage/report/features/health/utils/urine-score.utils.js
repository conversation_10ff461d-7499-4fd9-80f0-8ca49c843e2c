function getUrineScore(type, value, isKetoneMode) {
  switch (type) {
    case "blood": {
      const levels = ["negative", "warning", "caution", "danger"];

      return levels[value - 1];
    }
    case "ph": {
      const levels = [
        "negative",
        "negative",
        "negative",
        "negative",
        "negative",
        "warning",
      ];

      return levels[value - 1];
    }
    case "ketone": {
      if (!isKetoneMode) {
        // * ketoneMode === false
        const levels = ["negative", "warning", "caution", "danger", "danger"];

        return levels[value - 1];
      }
      // * ketoneMode === true

      const levels = [
        "exertion",
        "enter",
        "ketone_normal",
        "ketone_good",
        "ketone_warning",
      ];

      return levels[value - 1];
    }
    case "protein": {
      const levels = [
        "negative",
        "negative",
        "warning",
        "caution",
        "danger",
        "danger",
      ];
      return levels[value - 1];
    }
    case "glucose": {
      const levels = [
        "negative",
        "negative",
        "warning",
        "caution",
        "danger",
        "danger",
      ];
      return levels[value - 1];
    }
    default: {
      console.warn(
        `type is not defined. check parameter 'type'\n type: ${type}, value: ${value}, isKetoneMode: ${isKetoneMode}`
      );
      break;
    }
  }
}

export { getUrineScore };
