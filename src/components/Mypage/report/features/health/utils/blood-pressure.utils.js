import { BP_STATUSES } from "../constants/blood-pressure.constant";

/**
 * 혈압 수치에 따른 상태 판정
 * @param {Array} systolic - 수축기 혈압 범위 [최소, 최대]
 * @param {Array} diastolic - 이완기 혈압 범위 [최소, 최대]
 * @returns {Object} 현재 혈압 상태 객체
 */
export function determineBloodPressureStatus(systolic, diastolic) {
  const isNotArray = !Array.isArray(systolic) || !Array.isArray(diastolic);
  const [minSystolic, maxSystolic] = systolic;
  const [minDiastolic, maxDiastolic] = diastolic;

  if (
    isNotArray ||
    !minSystolic ||
    !maxSystolic ||
    !minDiastolic ||
    !maxDiastolic
  ) {
    return BP_STATUSES.NONE;
  }

  // 평균값 계산 - O(1) 시간복잡도
  const avgSystolic = (systolic[0] + systolic[1]) / 2;
  const avgDiastolic = (diastolic[0] + diastolic[1]) / 2;

  // 상태 판정 - O(1) 시간복잡도 (상수 시간)
  if (avgSystolic < 90 || avgDiastolic < 60) {
    return BP_STATUSES.LOW;
  } else if (avgSystolic <= 119 && avgDiastolic <= 79) {
    return BP_STATUSES.NORMAL;
  } else if (avgSystolic >= 120 && avgSystolic <= 129 && avgDiastolic < 80) {
    return BP_STATUSES.ELEVATED;
  } else if (
    (avgSystolic >= 130 && avgSystolic <= 139) ||
    (avgDiastolic >= 80 && avgDiastolic <= 89)
  ) {
    return BP_STATUSES.STAGE1;
  } else if (avgSystolic >= 180 || avgDiastolic >= 120) {
    return BP_STATUSES.CRISIS;
  } else if (avgSystolic >= 140 || avgDiastolic >= 90) {
    return BP_STATUSES.STAGE2;
  }

  // 기본값
  return BP_STATUSES.NORMAL;
}
