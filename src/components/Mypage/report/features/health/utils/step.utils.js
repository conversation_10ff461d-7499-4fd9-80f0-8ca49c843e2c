// 주차별 데이터 정리 유틸 함수들

/**
 * 주어진 날짜가 해당 월의 몇 번째 주인지 계산합니다.
 * @param {Date} date - 계산할 날짜 객체
 * @returns {number} 해당 월의 주차 (예: 1, 2, 3...)
 */
function getWeekOfMonth(dateString) {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = date.getMonth(); // 0부터 시작 (0: 1월, 11: 12월)
  const day = date.getDate();

  // 해당 월의 첫 번째 날짜 객체 생성
  const firstDayOfMonth = new Date(year, month, 1);
  // 해당 월의 첫 번째 날이 무슨 요일인지 (0: 일요일, 6: 토요일)
  const firstDayOfWeek = firstDayOfMonth.getDay();

  // 날짜가 1일인 경우 무조건 1주차입니다.
  if (day === 1) {
    return 1;
  }

  // 해당 날짜까지의 총 일수 계산 (해당 월의 1일부터 현재 일자까지의 일수)
  // 여기에 첫 번째 날의 요일에 따른 오프셋을 더합니다.
  // 예를 들어, 1일이 수요일(3)이라면 3일만큼 오프셋이 있습니다.
  const daysSinceFirstDayOfMonth = day + firstDayOfWeek - 1;

  // 주차 계산: (지난 일수 + 오프셋)을 7로 나누고 올림
  // Math.ceil을 사용하여 소수점 이하를 올림 처리하여 주차를 정확히 계산합니다.
  return Math.ceil(daysSinceFirstDayOfMonth / 7);
}
/**
 * 데이터를 주차별로 그룹화하고 집계하는 함수
 * @param {Array} data - 원본 데이터 배열
 * @returns {Array} 주차별로 집계된 데이터 배열
 */
export const groupByWeek = (data) => {
  const weeklyData = {};

  data.forEach((item) => {
    const weekNum = getWeekOfMonth(item.date);
    const weekKey = `${weekNum}주`;

    if (!weeklyData[weekKey]) {
      weeklyData[weekKey] = {
        week: weekKey,
        weekNumber: weekNum,
        totalSteps: 0,
        totalDistance: 0,
        totalCalories: 0,
        count: 0,
        dates: [],
      };
    }

    weeklyData[weekKey].totalSteps += item.totalStepCount;
    weeklyData[weekKey].totalDistance += item.totalDistance;
    weeklyData[weekKey].totalCalories += item.totalCalories;
    weeklyData[weekKey].count += 1;
    weeklyData[weekKey].dates.push(item.date);
  });

  // 주차 순서로 정렬
  return Object.values(weeklyData).sort((a, b) => a.weekNumber - b.weekNumber);
};

/**
 * 주차별 평균을 계산하는 함수
 * @param {Array} weeklyData - groupByWeek 함수의 결과
 * @returns {Object} 평균값들
 */
export const calculateWeeklyAverages = (weeklyData) => {
  const totalWeeks = weeklyData.length;

  if (totalWeeks === 0) return { avgSteps: 0, avgDistance: 0, avgCalories: 0 };

  const totals = weeklyData.reduce(
    (acc, week) => ({
      totalSteps: acc.totalSteps + week.totalSteps,
      totalDistance: acc.totalDistance + week.totalDistance,
      totalCalories: acc.totalCalories + week.totalCalories,
    }),
    { totalSteps: 0, totalDistance: 0, totalCalories: 0 }
  );

  return {
    avgSteps: Math.round(totals.totalSteps / totalWeeks),
    avgDistance: Math.round(totals.totalDistance / totalWeeks),
    avgCalories: Math.round(totals.totalCalories / totalWeeks),
  };
};

/**
 * 차트용 데이터로 변환하는 함수
 * @param {Array} weeklyData - groupByWeek 함수의 결과
 * @param {string} metric - 'steps', 'distance', 'calories' 중 하나
 * @returns {Array} 차트용 데이터 배열
 */
export const formatForChart = (weeklyData, metric = "steps") => {
  return weeklyData.map((week) => ({
    week: week.week,
    value:
      metric === "steps"
        ? week.totalSteps
        : metric === "distance"
        ? week.totalDistance
        : week.totalCalories,
    label:
      metric === "steps"
        ? `${week.totalSteps.toLocaleString()}`
        : metric === "distance"
        ? `${week.totalDistance.toLocaleString()}m`
        : `${week.totalCalories.toLocaleString()}kcal`,
  }));
};
