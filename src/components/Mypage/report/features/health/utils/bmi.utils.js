/**
 * @description BMI 범위에 따른 카테고리를 반환하는 메서드
 * @param {number} bmi
 * @returns {string}
 */
function getBMICategory(bmi) {
  const thresholds = [18.5, 22.9, 24.9, 29.9];
  const labels = ["저체중", "정상 체중", "과체중", "비만 1단계", "비만 2단계"];

  let start = 0,
    end = thresholds.length - 1;

  while (start <= end) {
    const middle = Math.floor((start + end) / 2);
    if (bmi < thresholds[middle]) {
      end = middle - 1;
    } else {
      start = middle + 1;
    }
  }

  return labels[start];
}

export { getBMICategory };
