/**
 * @description BMI 범위에 따른 카테고리 키를 반환하는 메서드
 * @param {number} bmi
 * @returns {string} i18n 번역 키
 */
function getBMICategory(bmi) {
  const thresholds = [18.5, 22.9, 24.9, 29.9];
  const labelKeys = [
    "bmi_underweight",
    "bmi_normal",
    "bmi_overweight",
    "bmi_obese_1",
    "bmi_obese_2",
  ];

  let start = 0,
    end = thresholds.length - 1;

  while (start <= end) {
    const middle = Math.floor((start + end) / 2);
    if (bmi < thresholds[middle]) {
      end = middle - 1;
    } else {
      start = middle + 1;
    }
  }

  return labelKeys[start];
}

export { getBMICategory };
