/**
 * @description 수분 섭취량 데이터를 가공하는 메서드
 *
 * @param {number} waterValue
 * @param {number} [decimalPlaces]
 * @return {string} water
 */
function formatWaterVolume(waterValue, decimalPlaces = 1) {
  // 1L 미만일 때
  if (waterValue < 1000) return `${waterValue}ml`;

  // 1L 이상일 때
  const waterLiters = waterValue / 1000;

  const roundedWater =
    Math.round(waterLiters * Math.pow(10, decimalPlaces)) /
    Math.pow(10, decimalPlaces);

  const formattedValue =
    roundedWater % 1 === 0
      ? roundedWater.toString()
      : roundedWater.toFixed(decimalPlaces);

  if (isNaN(formattedValue)) {
    throw new Error("water is NaN");
  }

  return `${formattedValue}L`;
}

export { formatWaterVolume };
