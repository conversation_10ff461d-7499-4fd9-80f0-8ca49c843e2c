/**
 * @typedef {Object} BloodPressureStatus
 * @property {string} id - 상태 식별자
 * @property {string} label - 상태 레이블
 * @property {string} color - 상태 색상 코드
 * @property {Array<number>} systolicRange - 수축기 혈압 범위 [최소, 최대]
 * @property {Array<number>} diastolicRange - 이완기 혈압 범위 [최소, 최대]
 * @property {string} message - 상태 메시지
 */

/**
 * @typedef {Object} BloodPressureProps
 * @property {string} selectedDate - 선택된 날짜 (예: '2024-02')
 * @property {Array<number>} systolic - 수축기 혈압 범위 [최소, 최대]
 * @property {Array<number>} diastolic - 이완기 혈압 범위 [최소, 최대]
 */
