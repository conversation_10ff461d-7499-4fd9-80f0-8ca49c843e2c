<template>
  <article class="blood-pressure-card">
    <!-- 헤더 섹션 -->
    <header class="card-header">
      <h2 class="title">혈압</h2>
      <span class="record-info">총 {{ totalDaysRecord }}일 기록</span>
    </header>

    <!-- 혈압 수치 섹션 -->
    <section class="values-section">
      <div class="value-row">
        <span class="label">수축기 혈압</span>
        <span class="value">{{ systolicMessage }}</span>
      </div>

      <div class="value-row">
        <span class="label">이완기 혈압</span>
        <span class="value">{{ diastolicMessage }}</span>
      </div>
    </section>

    <!-- 상태 표시 섹션 -->
    <StatusIndicator :status="currentStatus" />

    <!-- 혈압 범위 바 섹션 -->
    <section class="range-bar-container" aria-hidden="true">
      <div class="range-bar">
        <div
          v-for="segment in rangeBarSegments"
          :key="segment.id"
          class="range-segment"
          :class="segment.id"
          :style="{ backgroundColor: segment.color }"
        ></div>
      </div>
    </section>

    <!-- 상태 메시지 섹션 -->
    <footer class="status-message">
      <p>{{ currentStatus.message }}</p>
    </footer>
  </article>
</template>

<script>
import StatusIndicator from "../../ui/blood-pressure-indicator.ui.vue";
import { determineBloodPressureStatus } from "./utils/blood-pressure.utils";
import { RANGE_BAR_SEGMENTS } from "./constants/blood-pressure.constant";

export default {
  name: "ReportBloodPressure",

  components: {
    StatusIndicator,
  },

  props: {
    selectedDate: {
      type: String,
      required: true,
      default: "2024-02",
    },
    systolic: {
      type: Array,
      required: true,
      default: () => [0, 0],
    },
    diastolic: {
      type: Array,
      required: true,
      default: () => [0, 0],
    },
  },

  computed: {
    // 기록된 날짜 수 계산
    totalDaysRecord() {
      if (this.systolic.length === 0 || this.diastolic.length === 0) return 0;

      const date = this.selectedDate;
      const [year, month] = date.split("-").map(Number);
      const lastDay = new Date(year, month, 0).getDate();
      return lastDay;
    },
    /**
     * 현재 혈압 상태 계산
     * @returns {Object} 현재 혈압 상태
     */
    currentStatus() {
      return determineBloodPressureStatus(this.systolic, this.diastolic);
    },

    /**
     * 범위 바 세그먼트 가져오기
     * @returns {Array} 범위 바 세그먼트 배열
     */
    rangeBarSegments() {
      return RANGE_BAR_SEGMENTS;
    },

    // 혈압 수축기 메세지 정의
    systolicMessage() {
      const [min, max] = this.systolic;

      // 기록이 없을 때 0으로 표시
      if (!min || !max) return "0 mmHg";

      return `${min}-${max} mmHg`;
    },

    // 혈압 이완기 메세지 정의
    diastolicMessage() {
      const [min, max] = this.diastolic;

      // 기록이 없을 때 0으로 표시
      if (!min || !max) return "0 mmHg";

      return `${min}-${max} mmHg`;
    },
  },
};
</script>

<style lang="scss" scoped>
// 변수 정의
$card-bg: white;
$card-radius: 8px;
$card-padding: 24px;
$card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
$card-max-width: 400px;

$text-primary: #374151;
$text-secondary: #6b7280;
$text-dark: #1f2937;

$bg-message: #f8f8f8;

// 카드 스타일
.blood-pressure-card {
  background: $card-bg;
  border-radius: $card-radius;
  padding: $card-padding;
  max-width: $card-max-width;
  padding: 30px;

  // 헤더 스타일
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .title {
      font-size: 18px;
      font-weight: bold;
      color: black;
    }

    .record-info {
      font-size: 16px;
      color: black;
      font-weight: 500;
    }
  }

  // 혈압 수치 섹션 스타일
  .values-section {
    margin-bottom: 24px;

    .value-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: #000000;
        font-weight: 500;
        font-size: 14px;
      }

      .value {
        color: #000000;
        font-weight: 500;
        font-size: 14px;
      }
    }
  }

  // 범위 바 스타일
  .range-bar-container {
    margin: 24px 0;

    .range-bar {
      height: 12px;
      border-radius: 6px;
      overflow: hidden;
      display: flex;

      .range-segment {
        &.low {
          width: 46.67%;
        }
        &.normal {
          width: 14.67%;
        }
        &.elevated {
          width: 4.67%;
        }
        &.stage1 {
          width: 4.67%;
        }
        &.stage2 {
          width: 19.33%;
        }
        &.crisis {
          width: 10%;
        }
      }
    }
  }

  // 상태 메시지 스타일
  .status-message {
    background-color: $bg-message;
    border-radius: $card-radius;
    padding: 30px 20px;

    p {
      font-size: 14px;
      color: #000000;
      text-align: center;
      line-height: 1.5;
      font-weight: 500;
      margin: 0;
    }
  }
}
</style>
