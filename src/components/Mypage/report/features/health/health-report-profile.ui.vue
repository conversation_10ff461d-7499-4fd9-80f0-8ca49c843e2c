<template>
  <main class="profile-wrapper">
    <header class="report-header">
      <!-- 날짜 컴포넌트 -->
      <p class="report-date" @click="handleOpenModal">
        {{ showReportDate }}
        <img
          :src="arrowImage"
          alt="arrow-ic"
          class="down-arrow"
          loading="lazy"
        />
      </p>
      <div class="userInfo-container">
        <nav>
          <div class="profile-text">
            {{ userName }}님, <br />{{ selectedMonth }}월 심 건강 점수는
            <br />
            {{ averageScore }}
            점 입니다.
          </div>
        </nav>
        <nav>
          <img
            :src="profileImage"
            alt="profile image"
            @error="handleImageError"
            class="profile-img"
          />
        </nav>
      </div>
    </header>
  </main>
</template>

<script>
export default {
  name: "HealthReportProfile",

  props: {
    selectedDate: {
      type: [String, Date],
      default: "",
      required: true,
      validator: (value) => {
        // 형식: `YYYY-MM` (예: "2024-02", "2025-12")
        if (!value) return false;

        const dateRegex = /^\d{4}-(0[1-9]|1[0-2])$/;
        if (!dateRegex.test(value)) return false;

        const MIN_YEAR = 2000;
        const CURRENT_YEAR = new Date().getFullYear();

        const [year, month] = value.split("-");
        const yearNum = parseInt(year);
        const monthNum = parseInt(month);

        // 년도는 MIN_YEAR - CURRENT_YEAR 범위, 월은 1-12 범위
        return (
          yearNum >= MIN_YEAR &&
          yearNum <= CURRENT_YEAR + 3 &&
          monthNum >= 1 &&
          monthNum <= 12
        );
      },
    },

    profileData: {
      type: Object,
      required: true,
      default: () => {
        const username = localStorage.getItem("username") ?? "";

        return {
          averageScore: 0,
          subject: {
            nickname: username,
            image: "@/assets/images_assets/mypage-icon/sub-default.png",
          },
        };
      },
    },
  },

  data() {
    return {
      arrowImage: require("@/assets/images_assets/icons/arrow-ic.png"),
    };
  },

  watch: {
    selectedDate: {
      handler(newDate) {
        // props가 변경될 때마다 data 업데이트
        this.updateDisplayData(newDate);
      },
      immediate: true, // 컴포넌트 생성 시에도 실행
    },
  },

  computed: {
    // 포멧팅 함수
    showReportDate() {
      if (!this.selectedDate) return "";

      const dateStr = this.selectedDate.toString();
      const [year, month] = dateStr.split("-");

      if (!year || !month) return "";

      // 해당 월의 마지막 날 구하기
      const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate();

      // 월을 2자리로 포맷팅
      const formattedMonth = month.padStart(2, "0");

      return `${year}-${formattedMonth}-01~${formattedMonth}-${lastDay}`;
    },

    // selectedDate에서 month만 추출
    selectedMonth() {
      if (!this.selectedDate) return null;

      const [, month] = this.selectedDate.split("-");
      return month ? parseInt(month) : null;
    },

    // profileData에서 username 추출
    userName() {
      return this.profileData &&
        this.profileData.subject &&
        this.profileData.subject.nickname
        ? this.profileData.subject.nickname
        : "";
    },

    // profileData에서 image 추출
    profileImage() {
      // 기본 이미지를 import로 가져오기
      const DEFAULT_IMAGE = require("@/assets/images_assets/mypage-icon/sub-default.png");

      // profileData에서 이미지 URL 확인
      if (
        this.profileData &&
        this.profileData.subject &&
        this.profileData.subject.image
      ) {
        const imageUrl = this.profileData.subject.image;

        // 외부 URL인지 확인 (http:// 또는 https://로 시작)
        if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
          return imageUrl;
        }

        return DEFAULT_IMAGE;
      }

      return DEFAULT_IMAGE;
    },

    // profileData에서 averageScore 추출
    averageScore() {
      const DEFAULT_SCORE = "__";

      return this.profileData && this.profileData.averageScore
        ? parseFloat(
            Number(this.profileData.averageScore).toFixed(1)
          ).toString()
        : DEFAULT_SCORE;
    },
  },

  methods: {
    // 기간 설정 모달 열어주는 메서드
    handleOpenModal() {
      this.$store.commit("SET_SELECT_DATE_MODAL", true);
    },

    // 이미지 로딩 에러 처리
    handleImageError(event) {
      console.warn("cannot load image");
      event.target.src = require("@/assets/images_assets/mypage-icon/sub-default.png");
    },

    // props 변경에 따른 데이터 업데이트
    updateDisplayData(selectedDate) {
      if (!selectedDate) {
        this.displayData = null;
        return;
      }

      // selectedDate에 따라 다른 데이터 설정
      const [year, month] = selectedDate.split("-");

      this.displayData = {
        year: parseInt(year),
        month: parseInt(month),
        period: this.showReportDate,
        dataCount: Math.floor(Math.random() * 100), // 예시 데이터
      };
    },
  },
};
</script>

<style lang="scss" scoped>
$CYM_COLOR: #41d8e6;

.profile-wrapper {
  width: 100% !important;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-content: flex-start;
}

.report-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.report-date {
  width: fit-content;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  color: #000000;
}

.down-arrow {
  width: 6px;
  height: 11px;
  transform: rotate(90deg) translateY(-5px);
}

.userInfo-container {
  width: 100%;
  display: flex;
  justify-content: space-between;

  > nav {
    display: flex;
    align-items: flex-end;
  }
}

.profile-text {
  width: 100%;
  height: fit-content;
  font-weight: bold;
  font-size: 20px;
  text-align: start;
}

.profile-img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 5px solid $CYM_COLOR;
  -webkit-user-drag: none;
}
</style>
