<template>
  <main class="weight-section">
    <header class="weight-header">
      <h3 class="weight-title">체중</h3>
      <span>총 {{ totalDaysRecord }}일 기록</span>
    </header>
    <section>
      <nav>
        <div class="scale">
          <div class="indicator"></div>
          <div class="weight-text">
            <span class="weight-number">{{
              Number(currentWeight).toFixed(1)
            }}</span>
            Kg
          </div>
        </div>
      </nav>
      <article>
        <p class="bmi-text" v-html="bmiText" />
      </article>
      <article>
        <BmiRangeBar />
      </article>
    </section>
    <footer class="weight-guide">
      <span class="weight-guide-text">{{ checkWeightText }}</span>
    </footer>
  </main>
</template>

<script>
import { getBMICategory } from "./utils/bmi.utils";
import BmiRangeBar from "../../ui/bmi-range-bar.ui.vue";

export default {
  name: "ReportWeight",

  props: {
    targetWeight: {
      type: Number,
      required: true,
      default: 0,
    },
    currentWeight: {
      type: Number,
      required: true,
      default: 0,
    },
    bmi: {
      type: Number,
      required: true,
      default: 0,
    },
    selectedDate: {
      type: String,
      required: true,
      default: () => {
        const date = new Date();

        return `${date.getFullYear()}-${date.getMonth() + 1}`;
      },
    },
  },

  components: {
    BmiRangeBar,
  },

  data() {
    return {
      arrowImage: require("@/assets/images_assets/icons/arrow-ic.png"),
    };
  },

  computed: {
    bmiText() {
      if (!this.bmi || this.bmi < 0) {
        return `나의 bmi 기록이 없습니다.   
            <img
              src="${this.arrowImage}"
              alt="bmi-standard-table btn"
              loading="lazy"
              class="arrow-image"
              @click="${this.handleOpenModal}"
            />`;
      }

      return `나의 BMI는
          <span class="bmi-number">${this.bmi}</span>
          이며, ${this.bmiRange} 범위 입니다.
          <img
            src="${this.arrowImage}"
            alt="bmi-standard-table btn"
            loading="lazy"
            class="arrow-image"
            @click="${this.handleOpenModal}"
          />`;
    },

    totalDaysRecord() {
      if (this.currentWeight === 0) return 0;

      const date = this.selectedDate;
      const [year, month] = date.split("-").map(Number);
      const lastDay = new Date(year, month, 0).getDate();

      return lastDay;
    },

    bmiRange() {
      const categoryKey = getBMICategory(this.bmi);
      return this.$t(categoryKey);
    },

    checkWeightText() {
      let text = "";

      if (!this.bmi || !this.currentWeight) {
        return "몸무게 변화를 확인하려면 기록이 중요해요! 지금 체중을 입력해보세요.";
      }

      if (this.targetWeight === this.currentWeight) {
        text = "목표를 달성했습니다!👏🏻​";
      } else {
        const weightDiff = this.targetWeight - this.currentWeight;

        console.log(this.targetWeight, this.currentWeight);

        text = `목표까지 ${Math.abs(weightDiff.toFixed(2))}kg ${
          weightDiff < 0 ? "감량" : "증량"
        }이 필요합니다.`;
      }

      return `나의 목표 체중은 ${this.targetWeight}Kg 입니다. ${text}`;
    },
  },

  methods: {
    handleOpenModal() {
      this.$store.commit("SET_BMI_INFO_MODAL", true);
    },
  },
};
</script>

<style lang="scss" scoped>
.weight-section {
  width: 100% !important;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-content: flex-start;

  > section > nav {
    display: flex;
    justify-content: center;
  }
}

.weight-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.weight-guide {
  width: 100%;
  background: #f8f8f8;
  padding: 30px 20px;
  border-radius: 10px;
}

.scale {
  width: 146px;
  height: 146px;
  padding: 20px 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid #f8f8f8;
  background: #f8f8f8;
  border-radius: 25%;
  gap: 20px;
}

.indicator {
  width: 69px;
  height: 32px;
  border-radius: 3px;
  background: #ffffff;
}

.weight-text {
  width: 100%;
  font-family: GilroyBold !important;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: -0.03em;
  color: #646464;
}

.weight-number {
  font-family: GilroyBold !important;
  font-size: 24px;
  line-height: 24px;
  letter-spacing: -0.03em;
  color: #646464;
  margin-top: 10px;
}

/* v-html로 생성된 요소에 스타일 적용 */
:deep(.bmi-number) {
  font-weight: 800;
  font-size: 16px;
  font-family: GilroyBold !important;
  color: #41d8e6;
}

/* 또는 전역 스타일로 적용 */
.bmi-number {
  font-weight: 800;
  font-size: 16px;
  font-family: GilroyBold !important;
  color: #41d8e6;
}

.bmi-text {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  text-align: start;
  margin-top: 30px;
}

:deep(.arrow-image) {
  width: 6px;
  height: 11px;
  margin-left: 5px;
}

.weight-guide-text {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  line-height: 25px;
  letter-spacing: -3%;
}
</style>
