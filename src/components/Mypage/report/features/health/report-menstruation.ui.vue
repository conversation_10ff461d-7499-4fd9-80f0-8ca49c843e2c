<template>
  <main class="menstruation-section">
    <h1 class="title">우먼</h1>

    <section class="section-card">
      <p class="card-text">{{ getMenstruationCycleText }}</p>
      <ProgressBar
        :currentValue="getMenstruationCycle"
        :averageValue="getAverageCycle"
        unit="일"
        :labelCurrent="`${getMonth}월 주기`"
        labelAverage="평균주기"
      />
    </section>

    <hr class="divider" />

    <section class="section-card">
      <p class="card-text">
        {{ getMenstruationPeriod }}
      </p>
      <ProgressBar
        :currentValue="getMenstruationPeriodDays"
        :averageValue="getAveragePeriodDays"
        unit="일"
        :labelCurrent="`${getMonth}월 기간`"
        labelAverage="평균기간"
      />
    </section>

    <footer class="info-box">
      <p v-html="getNextMenstruationDate"></p>
    </footer>
  </main>
</template>

<script>
import ProgressBar from "../../ui/woman-menstruation-bar.ui.vue";

import { formatMenstruationData } from "./utils/woman-menstruation.utils";

export default {
  name: "MenstrualCycleTracker",

  components: {
    ProgressBar,
  },

  props: {
    selectedDate: {
      type: String,
      default: () => new Date().toISOString(),
    },

    menstruationData: {
      type: Object,
      default: () => ({
        fertileWindow: {
          startDate: "",
          endDate: "",
        },
        ovulationDay: "",
        nextMenstruation: {
          startDate: "",
          endDate: "",
        },
        menstruation: [],
      }),
    },
  },

  data() {
    return {
      MAX_WEEK: 7,
    };
  },

  methods: {},

  computed: {
    formattedData() {
      // menstruationData가 변경될 때마다 자동으로 재계산
      return formatMenstruationData(this.menstruationData);
    },
    getMenstruationCycleText() {
      const selectedDate = this.selectedDate;
      const [, month] = selectedDate.split("-");

      // 생리 주기 기록이 없을 때  (기획서 p.g 21 번호 6, 7)
      if (!this.formattedData.hasRecord) {
        return `${parseInt(month)}월 생리 주기 기록이 없습니다.`;
      }

      if (this.formattedData.isCurrentlyMenstruating) {
        return `${parseInt(month)}월 생리 주기가 진행 중입니다.`;
      }

      const cycle = this.formattedData.menstruationCycle;

      return `${parseInt(month)}월 생리 주기는 ${cycle}일 입니다.`;
    },

    getMenstruationCycle() {
      return this.formattedData.menstruationCycle || 0;
    },

    getAverageCycle() {
      // 평균 생리 주기 (일반적으로 28일)
      return 28;
    },

    getMenstruationPeriodDays() {
      return this.formattedData.menstruationPeriodDays || 0;
    },

    getAveragePeriodDays() {
      // 평균 생리 기간 (일반적으로 5-7일)
      return 6;
    },

    getMonth() {
      const selectedDate = this.selectedDate;
      const [, month] = selectedDate.split("-");

      return parseInt(month);
    },

    getNextMenstruationDate() {
      if (!this.formattedData.hasRecord) {
        return "기록이 없습니다. Cym<sup>702</sup> 앱 내 우먼케어를 활용하여 생리 주기 관리를 해보세요!";
      }

      const avoidDate = this.formattedData.avoidUrineTestDate;
      const [, month, day] = avoidDate.split("-");
      const avoidUrineTestDate = `${parseInt(month)}월 ${parseInt(day)}일`;

      return `다음 생리 예상일은 ${avoidUrineTestDate} 입니다. 생리 기간에는 소변검사를 피해주세요.`;
    },

    getMenstruationPeriod() {
      const period = this.formattedData.menstruationPeriod;
      const selectedDate = this.selectedDate;
      const [, month] = selectedDate.split("-");

      // 생리 주기 기록이 없을 때  (기획서 p.g 21 번호 6, 7)
      if (!this.formattedData.hasRecord) {
        return `${parseInt(month)}월 생리 주기 기록이 없습니다.`;
      }

      if (this.formattedData.isCurrentlyMenstruating) {
        return `${parseInt(month)}월 생리 주기가 진행 중입니다.`;
      }

      return `${parseInt(month)}월 생리 기간 ${period ?? ""}로 총 ${
        this.formattedData.menstruationPeriodDays
      }일 입니다.`;
    },
  },
};
</script>

<style lang="scss" scoped>
.menstruation-section {
  width: 100% !important;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-content: flex-start;

  > section > nav {
    display: flex;
    justify-content: center;
  }
}

.title {
  font-size: 1.5rem; /* Equivalent to text-2xl */
  font-weight: bold;
  margin-bottom: 1.5rem; /* Equivalent to mb-6 */
  text-align: start;
}

.divider {
  margin: 30px 0;
  border: 2px solid #ededed;
  border-radius: 10px;
}

.info-box {
  margin-top: 30px;
  padding: 20px 30px; /* Equivalent to p-4 */
  background-color: #f8f8f8; /* Equivalent to bg-gray-100 */
  border-radius: 10px; /* Equivalent to rounded-lg */
  font-size: 14px; /* Equivalent to text-base */
  line-height: 1.625; /* Equivalent to leading-relaxed */
  color: #000000;
  > p {
    margin: 0;
  }
}

.card-text {
  width: 100%;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: -0.03em;
  color: #000000;
  font-weight: 500;
  text-align: left;
  margin-bottom: 30px;
}
</style>
