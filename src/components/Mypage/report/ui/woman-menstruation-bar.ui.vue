<template>
  <div class="progress-bar-container">
    <!--bar-->
    <div
      class="progress-bar-track"
      role="progressbar"
      :aria-valuenow="currentValue"
      :aria-valuemin="0"
      :aria-valuemax="averageValue"
    >
      <div
        class="progress-bar-fill"
        :style="{ width: progressWidth }"
        :class="{ 'partial-fill': !isFullyFilled }"
      ></div>
    </div>

    <!--labels-->
    <div class="progress-labels">
      <div class="label-item">
        <span class="dot gray-dot"></span>
        <span>{{ labelAverage }} {{ averageValue }}{{ unit }}</span>
      </div>
      <div class="label-item">
        <span class="dot pink-dot"></span>
        <span>{{ labelCurrent }} {{ differenceText }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ProgressBar",

  props: {
    currentValue: {
      type: Number,
      required: true,
    },
    averageValue: {
      type: Number,
      required: true,
    },
    unit: {
      type: String,
      default: "",
    },
    labelCurrent: {
      type: String,
      default: "현재",
    },
    labelAverage: {
      type: String,
      default: "평균",
    },
  },

  computed: {
    progressWidth() {
      if (this.averageValue === 0) return "0%";
      const width = (this.currentValue / this.averageValue) * 100;
      return `${Math.min(100, width)}%`; // Ensure it doesn't exceed 100%
    },

    isFullyFilled() {
      if (this.averageValue === 0) return false;
      const percentage = (this.currentValue / this.averageValue) * 100;
      return percentage >= 100;
    },

    difference() {
      return this.currentValue - this.averageValue;
    },

    differenceText() {
      const sign = this.difference > 0 ? "+" : "";
      return `${sign}${this.difference}${this.unit}`;
    },
  },
};
</script>

<style lang="scss" scoped>
.progress-bar-track {
  max-width: 120px;
  height: 20px; /* Equivalent to h-3 */
  border-radius: 9999px; /* Equivalent to rounded-full */
  background-color: #a7a7a7; /* Equivalent to bg-gray-300 */
  overflow: hidden;
  margin: 0 auto;
}

.progress-bar-fill {
  height: 100%;
  background-color: #ffb5b5; /* Equivalent to bg-pink-300 */
  border-radius: 9999px; /* Equivalent to rounded-full */

  &.partial-fill {
    border-radius: 9999px 0 0 9999px; /* Only left side rounded when not fully filled */
  }
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px; /* Equivalent to text-sm */
  margin-top: 30px;

  @media (min-width: 640px) {
    /* sm breakpoint */
    font-size: 1rem; /* Equivalent to sm:text-base */
  }
}

.label-item {
  display: flex;
  align-items: center;
}

.dot {
  width: 0.5rem; /* Equivalent to w-2 */
  height: 0.5rem; /* Equivalent to h-2 */
  border-radius: 9999px; /* Equivalent to rounded-full */
  margin-right: 0.5rem; /* Equivalent to mr-2 */
}

.gray-dot {
  background-color: #a7a7a7; /* Equivalent to bg-gray-500 */
}

.pink-dot {
  background-color: #ffb5b5; /* Equivalent to bg-pink-500 */
}
</style>
