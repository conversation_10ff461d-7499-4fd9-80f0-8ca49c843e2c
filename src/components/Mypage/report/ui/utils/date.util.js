export function getWeeksInMonth(date) {
  // 월은 0부터 시작하므로 month - 1
  const d = new Date(date);
  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const firstDay = new Date(year, month - 1, 1);
  const lastDay = new Date(year, month, 0);

  // 해당 달의 첫 날 요일 (일요일: 0, 월요일: 1, ...)
  const firstDayOfWeek = firstDay.getDay();

  // 해당 달의 마지막 날
  const lastDate = lastDay.getDate();

  // 총 일수
  const totalDays = lastDate;

  // 첫 주에서 첫 요일 이전의 빈칸을 채우는 일수
  const daysBeforeFirstWeek = firstDayOfWeek;

  // 전체 일수 + 첫 주 앞부분의 빈칸 일수
  const daysWithPadding = totalDays + daysBeforeFirstWeek;

  // 7로 나누어 올림하면 총 주 수
  const weeks = Math.ceil(daysWithPadding / 7);

  return weeks;
}
