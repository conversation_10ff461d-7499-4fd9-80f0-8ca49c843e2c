<template>
  <div
    class="bmi-info-modal"
    v-show="isModalOpen"
    @click="closeModal"
    @touchmove="preventBackgroundScroll"
    @wheel="preventBackgroundScroll"
    ref="modal"
  >
    <div class="info-modal-item" @click.stop>
      <header class="modal-header">
        <button class="close-icon" @click="closeModal">
          <img src="@/assets/images_assets/icons/close-btn-solid-ic.png" />
        </button>
      </header>

      <div class="modal-content">
        <div class="bmi-title">BMI {{ bmi.toFixed(1) }}</div>
        <div class="bmi-status">{{ getBMIStatus() }}</div>

        <div class="bmi-ranges">
          <div class="underweight-info">
            <div class="color-indicator underweight-color"></div>
            <span class="label">{{ $t("bmi_underweight") }}:</span>
            <span class="value">18.5 미만</span>
          </div>

          <div
            class="bmi-range-item normal"
            :class="{ active: isNormalWeight() }"
          >
            <div class="range-text">
              <div class="color-indicator normal-color"></div>
              <span class="label">{{ $t("bmi_normal") }}:</span>
              <span class="value">18.5 ~ 22.9</span>
            </div>
          </div>

          <div
            class="bmi-range-item overweight"
            :class="{ active: isOverweight() }"
          >
            <div class="range-text">
              <div class="color-indicator overweight-color"></div>
              <span class="label">{{ $t("bmi_overweight") }}:</span>
              <span class="value">23 ~ 24.9</span>
            </div>
          </div>

          <div class="bmi-range-item obese1" :class="{ active: isObese1() }">
            <div class="range-text">
              <div class="color-indicator obese1-color"></div>
              <span class="label">{{ $t("bmi_obese_1") }}:</span>
              <span class="value">25 ~ 29.9</span>
            </div>
          </div>

          <div class="bmi-range-item obese2" :class="{ active: isObese2() }">
            <div class="range-text">
              <div class="color-indicator obese2-color"></div>
              <span class="label">{{ $t("bmi_obese_2") }}:</span>
              <span class="value">30 이상</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getBMICategory } from "../features/health/utils/bmi.utils";

export default {
  name: "BMIInfoModal",

  props: {
    bmi: {
      type: Number,
      required: true,
      default: 0,
    },
  },

  data() {
    return {
      isModalOpen: false,
      scrollY: 0,
    };
  },

  watch: {
    modalState: {
      handler(newVal) {
        this.isModalOpen = newVal;
      },
      immediate: true,
    },

    isModalOpen(newVal) {
      console.log("BMI Modal isModalOpen changed:", newVal);
      if (newVal) {
        this.scrollY = window.scrollY;
        document.body.style.overflow = "hidden";
        document.body.style.position = "fixed";
        document.body.style.width = "100%";
        document.body.style.top = `-${this.scrollY}px`;
      } else {
        document.body.style.overflow = "";
        document.body.style.position = "";
        document.body.style.width = "";
        document.body.style.top = "";
        window.scrollTo(0, this.scrollY);
      }
    },
  },

  computed: {
    modalState() {
      return this.$store.state.setting.showBmiInfoModal || false;
    },
  },

  methods: {
    closeModal() {
      this.isModalOpen = false;
      this.$store.commit("SET_BMI_INFO_MODAL", false);
    },

    preventBackgroundScroll(e) {
      if (e.target === this.$refs.modal) {
        e.preventDefault();
      }
    },

    getBMIStatus() {
      const categoryKey = getBMICategory(this.bmi);
      const category = this.$t(categoryKey);

      return `${category}입니다.`;
    },

    isNormalWeight() {
      return this.bmi >= 18.5 && this.bmi < 23;
    },

    isOverweight() {
      return this.bmi >= 23 && this.bmi < 25;
    },

    isObese1() {
      return this.bmi >= 25 && this.bmi < 30;
    },

    isObese2() {
      return this.bmi >= 30;
    },
  },

  beforeDestroy() {
    if (this.isModalOpen) {
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
      document.body.style.top = "";
      window.scrollTo(0, this.scrollY);
    }
  },
};
</script>

<style lang="scss" scoped>
.bmi-info-modal {
  $modal-background: rgba(0, 0, 0, 0.5);
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  background: $modal-background;
  position: fixed;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  border: none;
  z-index: 99999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.info-modal-item {
  width: 80%;
  max-width: 400px;
  background: #ffffff;
  padding: 30px 25px;
  border-radius: 20px;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.modal-header {
  position: absolute;
  width: 25px;
  height: 25px;

  .close-icon {
    width: 25px;
    height: 25px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.modal-content {
  text-align: center;

  .bmi-title {
    font-family: GilroyBold !important;
    font-size: 32px;
    font-weight: 800;
    color: #41d8e6;
    margin-top: 10px;
    margin-bottom: 8px;
    letter-spacing: 5%;
    line-height: 32px;
  }

  .bmi-status {
    font-size: 16px;
    color: #333;
    margin-bottom: 30px;
    font-weight: 500;
  }
}

.bmi-ranges {
  text-align: left;
  display: flex;
  flex-direction: column;
  align-items: center;

  .underweight-info {
    width: 70%;
    display: flex;
    align-items: center;
    padding: 8px 0;
    font-size: 14px;
    color: #000;
    font-weight: 500;

    .color-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .label {
      font-weight: 500;
    }

    .value {
    }
  }

  .bmi-range-item {
    width: 70%;
    display: flex;
    align-items: center;
    padding: 8px 0;
    font-size: 14px;
    color: #000;
    font-weight: 500;

    .color-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .range-text {
      display: flex;
      align-items: center;
      width: 100%;

      .label {
        font-weight: 500;
      }

      .value {
      }
    }

    &.active {
      color: #000;
    }
  }
}

.underweight-color {
  background-color: #fff4f4;
}

.normal-color {
  background-color: #ffb5b5;
}

.overweight-color {
  background-color: #bb0090;
}

.obese1-color {
  background-color: #742d61;
}

.obese2-color {
  background-color: #333333;
}
</style>
