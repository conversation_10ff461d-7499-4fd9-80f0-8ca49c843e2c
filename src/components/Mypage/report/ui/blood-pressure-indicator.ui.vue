<template>
  <div
    class="status-indicator"
    @click="handleOpenBloodPressureCategoryModalClick"
  >
    <span
      class="status-dot"
      :style="{ backgroundColor: status.color }"
      role="presentation"
      aria-hidden="true"
    ></span>
    <span class="status-label">{{ $t(status.labelKey) }}</span>
    <svg class="chevron-icon" viewBox="0 0 24 24" aria-hidden="true">
      <path
        d="M9 5l7 7-7 7"
        stroke="currentColor"
        stroke-width="2"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  </div>
</template>

<script>
export default {
  name: "BloodPressureIndicator",
  props: {
    status: {
      type: Object,
      required: true,
    },
  },

  methods: {
    handleOpenBloodPressureCategoryModalClick() {
      this.$store.commit("SET_BLOOD_PRESSURE_CATEGORY_MODAL", true);
    },
  },
};
</script>

<style lang="scss" scoped>
.status-indicator {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .status-dot {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 12px;
  }

  .status-label {
    color: #000000;
    font-weight: 500;
    margin-right: 8px;
  }

  .chevron-icon {
    width: 16px;
    height: 16px;
    color: #9ca3af;
  }
}
</style>
