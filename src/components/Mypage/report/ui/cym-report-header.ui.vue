<template>
  <header>
    <fixed-header :threshold="50">
      <div class="nav-bar">
        <div
          class="fixed-header"
          :class="isIos ? 'nav-space' : 'nav-space-android'"
        >
          <div>
            <div class="history-header_nav">
              <router-link :to="backPath">
                <v-icon>$back_btn_bold</v-icon>
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </fixed-header>
    <div class="care-header">
      <div
        :class="isIos ? 'care-header__wrapper' : 'care-header__wrapper-android'"
      >
        <div class="care-header_nav">
          <router-link :to="backPath">
            <v-icon>$back_btn_bold</v-icon>
          </router-link>
          <img
            v-show="tabName === 'health-report'"
            :src="require('@/assets/images/share.png')"
            alt="web-share-button"
            loading="lazy"
            class="share-btn"
            @click="handleShareButtonClick"
          />
        </div>
        <div class="care-header_title">
          <span class="care-header__title--en">Cym Report</span>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { defineComponent } from "vue";
import FixedHeader from "vue-fixed-header";

export default defineComponent({
  name: "CymReportHeader",

  components: {
    FixedHeader,
  },

  props: {
    tabName: {
      type: String,
      required: true,
      default: "health-report",
    },
  },

  data() {
    return {
      /**
       * Store the previous URL path for redirect logic
       * Used to determine navigation target based on referrer
       */
      previousUrlPath: "",
      /**
       * Store the previous route object for meta-based navigation
       */
      previousRoute: null,
    };
  },

  computed: {
    /**
     * Determines the back navigation path based on previous URL
     * Redirects to '/hi' if previous URL contains 'd', otherwise to '/bys'
     *
     * Time Complexity: O(n) where n is the length of the URL string
     * Space Complexity: O(1)
     *
     * @returns {string} The target path for back navigation
     */
    backPath() {
      // 세션 스토리지에서 이전 경로 정보 가져오기 (가장 신뢰할 수 있는 방법)
      const previousPath = sessionStorage.getItem("cymReportPreviousPath");

      if (previousPath) {
        // 현재 라우트의 meta 정보에서 getBackPath 함수 확인
        const currentRoute = this.$route;
        if (
          currentRoute.meta &&
          typeof currentRoute.meta.getBackPath === "function"
        ) {
          const fromRoute = { path: previousPath };
          return currentRoute.meta.getBackPath(fromRoute);
        }

        // meta 함수가 없을 경우 직접 판단
        if (previousPath.includes("/home")) {
          return "/home";
        } else if (previousPath.includes("/mypage")) {
          return "/mypage";
        }
      }

      // 기존 로직 유지 (fallback)
      const fallbackPath =
        this.previousUrlPath || this.getPreviousUrlFromReferrer();

      if (!fallbackPath) {
        return "/mypage";
      }

      if (fallbackPath.includes("home")) {
        return "/home";
      } else {
        return "/mypage";
      }
    },

    /**
     * Legacy computed property maintained for backward compatibility
     * @deprecated Use backPath instead
     */
    beforePath() {
      return this.backPath;
    },

    /**
     * Detects iOS platform for conditional styling
     * Uses efficient platform detection with fallback
     *
     * @returns {boolean} True if running on iOS device
     */
    isIos() {
      return /iPad|iPhone|iPod/.test(navigator.userAgent);
    },
  },

  /**
   * Component lifecycle hook - captures navigation context on mount
   * Stores previous URL information for optimal redirect performance
   */
  mounted() {
    this.capturePreviousUrl();
  },

  methods: {
    /**
     * share this report page to other website
     */
    async handleShareButtonClick() {
      // alert("기능 개발 중입니다.");
      try {
        if (navigator.share) {
          await navigator.share({
            title: `Cym-Report-${"Username"}`,
            url: "https://report.yellosis.com",
            text: `Check out my Cym Report!\n hi`, // \n으로 줄바꿈 가능
          });

          console.log("share success");
          return;
        }

        alert("해당 기기에서는 공유기능을 지원하지 않습니다.");
      } catch (error) {
        console.error("failed share report : ", error);
        throw Error(error);
      }
    },

    /**
     * Captures and stores the previous URL for redirect logic
     * Uses multiple fallback strategies for maximum compatibility
     *
     * Priority order:
     * 1. Vue Router's from parameter (most reliable)
     * 2. Document referrer (cross-domain navigation)
     * 3. Browser history state (SPA navigation)
     * 4. Default fallback
     */
    capturePreviousUrl() {
      try {
        // Method 1: Get from Vue Router navigation guard (if available)
        if (this.$router?.currentRoute?.query?.from) {
          this.previousUrlPath = this.$router.currentRoute.query.from;
          return;
        }

        // Method 2: Extract from document referrer
        if (document.referrer) {
          const referrerUrl = new URL(document.referrer);
          this.previousUrlPath = referrerUrl.pathname + referrerUrl.search;
          return;
        }

        // Method 3: Check browser history state
        if (window.history.state?.previous) {
          this.previousUrlPath = window.history.state.previous;
          return;
        }

        // Method 4: Fallback to empty string (will trigger '/bys' redirect)
        this.previousUrlPath = "";
      } catch (error) {
        // Error handling for URL parsing failures
        console.warn("Failed to capture previous URL:", error);
        this.previousUrlPath = "";
      }
    },

    /**
     * Utility method to extract previous URL from document referrer
     * Separated for testability and reusability
     *
     * @returns {string} The previous URL path or empty string
     */
    getPreviousUrlFromReferrer() {
      try {
        if (!document.referrer) return "";

        const referrerUrl = new URL(document.referrer);
        return referrerUrl.pathname + referrerUrl.search;
      } catch (error) {
        console.warn("Failed to parse referrer URL:", error);
        return "";
      }
    },

    /**
     * Public method to manually update the previous URL
     * Useful for programmatic navigation or testing
     *
     * @param {string} urlPath - The URL path to set as previous
     */
    setPreviousUrl(urlPath) {
      if (typeof urlPath === "string") {
        this.previousUrlPath = urlPath;
      }
    },
  },

  /**
   * Navigation guard to capture route changes
   * Automatically updates previous URL context for optimal UX
   */
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // Store the 'from' route path for redirect logic
      if (from && from.path) {
        vm.previousUrlPath = from.fullPath;
        vm.previousRoute = from;
      }
    });
  },

  /**
   * Navigation guard for route updates (when same component is reused)
   * Updates previous route information when navigating to same route
   */
  beforeRouteUpdate(to, from, next) {
    // Update previous route information when route changes
    if (from && from.path) {
      this.previousUrlPath = from.fullPath;
      this.previousRoute = from;
    }
    next();
  },
});
</script>

<style lang="scss" scoped>
.nav-bar.vue-fixed-header--isFixed .fixed-header {
  background-color: #c9f4f8;
  display: block !important;
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 450px;
  z-index: 999;
}

.nav-bar.vue-fixed-header--isFixed .fixed-header {
  display: block !important;
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 450px;
  z-index: 999;
}

.care-header {
  margin-bottom: 15px;
}

.nav-bar.vue-fixed-header .fixed-header {
  display: none;
}

.nav-space {
  padding: 55px 0 15px 10px;
}
.nav-space-android {
  padding: 20px 0 15px 10px;
}

.history-header_nav {
  display: flex;
  justify-content: flex-start;
  padding-left: 20px;
}

.care-header__wrapper {
  padding-top: 65px;
  padding-left: 30px;
  padding-right: 30px;
}
.care-header__wrapper-android {
  padding-top: 40px;
  padding-left: 30px;
  padding-right: 30px;
}

.care-header_nav {
  text-align: left;
  display: flex;
  justify-content: space-between;
}

.care-header_title {
  text-align: left;
  font-size: 28px;
  padding-top: 10px;
  line-height: 30px;
}

.care-header_subtitle {
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  padding-top: 22px;
  padding-bottom: 10px;
  letter-spacing: -0.03em;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 25px;
}

.care-header__title--ko {
  line-height: 30px;
  font-weight: 700;
  font-size: 30px;
  margin-bottom: 5px;
}

.care-header__title--en {
  line-height: 30px;
  font-family: GilroyBold !important;
  font-size: 36px;
}

.share-btn {
  width: 24px;
  height: 24px;
}
</style>
