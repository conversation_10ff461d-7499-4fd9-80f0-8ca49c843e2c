<template>
  <div class="bmi-range-bar">
    <div class="range-bar-container">
      <div class="gradient-bar">
        <div class="range-segment underweight"></div>
        <div class="range-segment normal"></div>
        <div class="range-segment overweight"></div>
        <div class="range-segment obese1"></div>
        <div class="range-segment obese2"></div>
      </div>

      <!-- BMI 값 표시 -->
      <div class="bmi-values">
        <span class="bmi-value" style="left: 16.67%">18.5</span>
        <span class="bmi-value" style="left: 50%">23</span>
        <span class="bmi-value" style="left: 66.67%">25</span>
        <span class="bmi-value" style="left: 91.67%">30</span>
      </div>

      <!-- 현재 BMI 표시 인디케이터 -->
      <div
        v-if="currentBMI && currentBMI >= 15 && currentBMI <= 35"
        class="current-bmi-indicator"
        :style="{ left: getBMIPosition() + '%' }"
      >
        <div class="indicator-line"></div>
        <div class="indicator-value">{{ currentBMI.toFixed(1) }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "BMIRangeBar",

  props: {
    currentBMI: {
      type: Number,
      default: null,
    },
  },

  methods: {
    getBMIPosition() {
      if (!this.currentBMI) return 0;

      const bmi = this.currentBMI;
      let position = 0;

      // BMI 값에 따른 위치 계산 (15~35 범위로 정규화)
      if (bmi < 18.5) {
        // 저체중 구간 (15-18.5)
        position = ((bmi - 15) / (18.5 - 15)) * 16.67;
      } else if (bmi < 23) {
        // 정상 구간 (18.5-23)
        position = 16.67 + ((bmi - 18.5) / (23 - 18.5)) * 33.33;
      } else if (bmi < 25) {
        // 과체중 구간 (23-25)
        position = 50 + ((bmi - 23) / (25 - 23)) * 16.67;
      } else if (bmi < 30) {
        // 비만1단계 구간 (25-30)
        position = 66.67 + ((bmi - 25) / (30 - 25)) * 25;
      } else {
        // 비만2단계 구간 (30-35)
        position = 91.67 + ((bmi - 30) / (35 - 30)) * 8.33;
      }

      return Math.max(0, Math.min(100, position));
    },
  },
};
</script>

<style lang="scss" scoped>
.bmi-range-bar {
  width: 100%;
  margin-bottom: 30px;
}

.range-bar-container {
  position: relative;
  width: 100%;
}

.gradient-bar {
  width: 100%;
  height: 20px;
  display: flex;

  border-radius: 40px;
  overflow: hidden;

  .range-segment {
    height: 100%;

    &.underweight {
      width: 16.67%;
      background: #fff4f4;
    }

    &.normal {
      width: 33.33%;
      background: #ffb5b5;
    }

    &.overweight {
      width: 16.67%;
      background: #bb0090;
    }

    &.obese1 {
      width: 25%;
      background: #742d61;
    }

    &.obese2 {
      width: 8.33%;
      background: #000000;
    }
  }
}

.bmi-values {
  position: relative;
  margin-top: 15px;
  height: 20px;

  .bmi-value {
    position: absolute;
    transform: translateX(-50%);
    font-size: 16px;
    font-weight: 500;
    color: #000;
  }
}

.current-bmi-indicator {
  position: absolute;
  top: -10px;
  transform: translateX(-50%);
  z-index: 10;

  .indicator-line {
    width: 2px;
    height: 40px;
    background: #ff4444;
    margin: 0 auto;
    border-radius: 1px;
    box-shadow: 0 2px 4px rgba(255, 68, 68, 0.3);
  }

  .indicator-value {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: #ff4444;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    white-space: nowrap;

    &::after {
      content: "";
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-top: 4px solid #ff4444;
    }
  }
}

// 반응형 디자인
@media (max-width: 480px) {
  .range-bar-container {
    max-width: 450px;
  }

  .bmi-values .bmi-value {
    font-size: 12px;
  }

  .current-bmi-indicator .indicator-value {
    font-size: 11px;
    padding: 3px 6px;
  }
}
</style>
