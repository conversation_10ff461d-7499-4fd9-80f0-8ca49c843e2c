<template>
  <div class="popup-overlay" v-if="isVisible" @click="handleCloseModalClick">
    <div class="popup-container" @click.stop>
      <header>
        <button class="close-btn" @click="handleCloseModalClick">
          <img src="@/assets/images_assets/icons/close-btn-solid-ic.png" />
        </button>

        <h2 class="popup-title">혈압 범주(mmHg)</h2>
      </header>

      <div class="table-container">
        <table class="bp-table">
          <thead>
            <tr>
              <th></th>
              <th class="bp-table-th">수축기</th>
              <th class="bp-table-th">이완기</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="status in filteredBpStatuses" :key="status.id">
              <td class="category-cell">
                <div
                  class="category-indicator"
                  :style="{ backgroundColor: status.color }"
                ></div>
                <span class="category-name">{{ status.label }}</span>
              </td>
              <td class="value-cell">
                {{ formatRange(status.systolicRange) }}
              </td>
              <td class="value-cell">
                {{ formatRange(status.diastolicRange) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import { BP_STATUSES } from "../features/health/constants/blood-pressure.constant";

export default {
  name: "BloodPressurePopup",

  data() {
    return {
      bpStatuses: BP_STATUSES,
    };
  },

  computed: {
    isVisible() {
      return this.$store.state.setting.showBloodPressureCategoryModal;
    },
    filteredBpStatuses() {
      // NONE 상태는 제외하고 나머지만 표시
      return Object.values(this.bpStatuses).filter(
        (status) => status.id !== "none"
      );
    },
  },
  methods: {
    handleCloseModalClick() {
      this.$store.commit("SET_BLOOD_PRESSURE_CATEGORY_MODAL", false);
    },
    handleKeydown(event) {
      if (event.key === "Escape" && this.isVisible) {
        this.closePopup();
      }
    },
    formatRange(range) {
      if (!range || range.length !== 2) return "-";

      const [min, max] = range;

      // 특별한 경우들 처리
      if (min === 0 && max === 79) return "<80";
      if (min === 0 && max === 119) return "<120";
      if (min === 120 && max === 129) return "<120-129";
      if (min === 130 && max === 139) return "<130-139";
      if (min === 80 && max === 89) return "<80-89";
      if (min === 140 && max === 179) return "≥140";
      if (min === 90 && max === 119) return "≥90";
      if (min === 180 && max === 300) return "≥180";
      if (min === 120 && max === 200) return "≥120";

      // 기본 범위 표시
      if (max >= 300 || max >= 200) {
        return `≥${min}`;
      }

      return `${min}-${max}`;
    },
  },
  mounted() {
    document.addEventListener("keydown", this.handleKeydown);
  },
  beforeDestroy() {
    document.removeEventListener("keydown", this.handleKeydown);
  },
};
</script>

<style lang="scss" scoped>
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99999;
  padding: 0 30px;
}

.popup-container {
  background: white;
  border-radius: 16px;
  max-width: 300px;
  width: 100%;
  max-height: 90vh;
  padding: 25px 15px 5px 15px;
}

header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

@keyframes popupSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.close-btn {
  position: absolute;
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;

  &:hover {
    background: #e5e7eb;
    color: #374151;
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.popup-title {
  width: 100%;
  font-size: 16px;
  color: #323232;
  font-weight: 400;
  text-align: center;
}

.table-container {
  overflow-x: auto;
}

.bp-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background: #f8f8f8;

  th {
    padding: 7px 10px;
    text-align: center;
    font-weight: 400;
    color: #323232;
    border-bottom: 1px solid #ededed;

    &:first-child {
      text-align: left;
      border-top-left-radius: 8px;
    }

    &:last-child {
      border-top-right-radius: 8px;
    }
  }

  td {
    padding: 7px 15px;
  }

  tr:last-child td {
    border-bottom: none;
  }
}

.category-cell {
  display: flex;
  align-items: center;
}

.category-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  flex-shrink: 0;
  margin-right: 5px;
}

.category-name {
  font-weight: 400;
  color: #000000;
  font-size: 14px;
}

.value-cell {
  min-width: 72px;
  font-family: GilroyMedium !important;
  text-align: center;
  font-weight: 500;
  color: #000000;

  padding: 0 !important;
  border-left: 1px solid #ededed;
}
</style>
