<template>
  <div>
    <fixed-header :threshold="50">
      <div :class="isIos ? 'mypage-header-wrapper' : 'mypage-header-wrapper-android'">
        <div class="header-left">
          <v-icon @click="moveBack">$back_btn_bold</v-icon>
        </div>
        <div class="header-center">{{ pageName }}</div>
      </div>
    </fixed-header>
    <div :class="isIos ? 'mypage-header-wrapper' : 'mypage-header-wrapper-android'">
      <div class="header-left">
        <v-icon @click="moveBack">$back_btn_bold</v-icon>
      </div>
      <div class="header-center">{{ pageName }}</div>
    </div>
  </div>
</template>

<script>
import FixedHeader from "@/components/Common/FixedHeader.vue";

export default {
  props: {
    pageName: String,
  },
  components: {
    FixedHeader,
  },
  data() {
    return {
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
  methods: {
    moveBack() {
      this.pageName === this.$i18n.t("useage_guide") ? this.$router.push({ path: "/mypage" }) : this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.mypage-header-wrapper {
  background-color: #c9f4f8;
  width: 100%;
  padding: 55px 30px 20px 30px;
  align-items: flex-end;
  display: flex;
}
.mypage-header-wrapper-android {
  background-color: #c9f4f8;
  width: 100%;
  padding: 25px 30px 20px 30px;
  align-items: flex-end;
  display: flex;
}

.header-left {
  display: flex;
  width: 10px;
}

.header-center {
  width: 100%;
  padding: 0 10px 4px 0;
  font-weight: 500;
  font-size: 22px;
  line-height: 29px;
  letter-spacing: -0.03em;
  text-align: center;
}
</style>
