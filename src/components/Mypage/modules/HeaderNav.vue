<template>
  <div>
    <div :class="isIos ? 'my-header-nav' : 'my-header-nav-android'">
      <div class="back-btn">
        <router-link to="home"><v-icon>$back_btn_bold</v-icon></router-link>
      </div>
      <div class="setting-btn">
        <router-link to="/mypage/settings"><v-icon>$setting_btn</v-icon></router-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
};
</script>

<style lang="scss" scoped>
.my-header-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 65px;
}
.my-header-nav-android {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 30px;
}
</style>
