<template>
  <div>
    <div class="userdetail-text-field__items">
      <v-text-field
        v-model="name"
        @change="nameInputHandler"
        color="#41d8e6"
        :placeholder="this.$i18n.t('input_nickname_placeholder')"
        :error-messages="nameError"
        :persistent-placeholder="true"
      >
        <template v-slot:label>
          <div class="text-field-label">{{ $t("profile_name_title") }}</div>
        </template>
      </v-text-field>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      name: "",
      nameError: "",
      valid: false,
    };
  },
  methods: {
    nameInputHandler() {
      // this.$emit("nameInputHandler", this.name);
      // this.$emit("validName", this.valid);
    },
    nameValidation(name) {
      return /^[ㄱ-ㅎ|가-힣|a-z|A-Z|0-9|]{2,20}$/.test(name);
    },
  },
  watch: {
    name(newVal) {
      (this.name.length === 0 || this.nameValidation(newVal)) && this.name !== this.$store.state.username
        ? ((this.nameError = ""), (this.valid = true))
        : ((this.nameError = this.$i18n.t("etc_placeholder")), (this.valid = false));

      if (this.name === this.$store.state.username) (this.nameError = ""), (this.valid = false);
      this.$emit("nameInputHandler", this.name);
      this.$emit("validName", this.valid);
    },
  },
  mounted() {
    // console.log(this.$store.state.username)
    this.name = this.$store.state.username;
    // this.firstValidation();
  },
};
</script>

<style lang="scss" scoped>
.userdetail-text-field__items {
  display: flex;
}

::v-deep .v-text-field .v-label {
  top: -10px !important;
  font-size: 12px;
  color: #41d8e6;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 18px !important;
    letter-spacing: -0.03em;
    line-height: 25px !important;
  }
}

::v-deep .v-text-field input {
  font-size: 18px !important;
  letter-spacing: -0.03em;
  line-height: 25px !important;
  padding-bottom: 5px;
}
::v-deep .v-input input {
  font-size: 18px !important;
  line-height: 25px;
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
</style>
