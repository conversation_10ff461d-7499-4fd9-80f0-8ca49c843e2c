<template>
  <div>
    <HeaderText :txt="txt" />

    <text-form :placeholder="placeholder" :label="label">
      <div class="btn__wrapper">
        <v-btn class="main-large-btn" elevation="0" color="primary" type="submit" @click="backToPage">
          {{ $t("confirm_btn") }}
        </v-btn>
      </div>
    </text-form>
  </div>
</template>

<script>
import HeaderText from "./modules/HeaderText.vue";
import TextForm from "./modules/TextForm.vue";
import { fetchUserEmail } from "@/api/user/index.js";
import NameForm from "@/components/Forms/NameForm.vue";
export default {
  components: {
    HeaderText,
    TextForm,
    // NameForm
  },
  data() {
    return {
      txt: "이메일은 심702 계정 로그인에 사용됩니다 <br /> 등록된 이메일은 직접 삭제 또는 변경할 수 없습니다",
      placeholder: "",
      label: "이메일 주소",
    };
  },
  mounted() {
    this.getUserEmail();
  },
  methods: {
    async getUserEmail() {
      try {
        this.loading = true;
        const { data } = await fetchUserEmail();
        this.loading = false;
        // console.log(data);
        if (data.data) {
          const userInfo = data.data[0];
          this.placeholder = userInfo.email;
        }
      } catch (error) {
        this.loading = false;
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    backToPage() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped></style>
