<template>
  <div>
    <CompleteAlert
      class="modal"
      :content="content"
      :btnText="this.$i18n.t('confirm_btn')"
      v-if="showCompleteAlert"
      @isConfirmed="isConfirmed"
    />
    <ErrorModal v-if="showErrorModal" :error="error" @isClicked="isClicked" />
    <div class="pt-30 dp-30">
      <div class="country__wrapper">
        <div class="dropdown__wrapper">
          <vue-country-dropdown
            @onSelect="onSelect"
            :disabledFetchingCountry="false"
            :preferredCountries="['KR', 'US', 'GB']"
            :enabledFlags="true"
            :enabledCountryCode="true"
            :showNameInput="true"
          />
        </div>
      </div>
      <v-text-field
        v-model="phone"
        :label="this.$i18n.t('input_phone')"
        color="#41D8E6"
        type="tel"
        maxlength="11"
        :rules="mobileNumberRule"
        :error-messages="findErrorMsg"
        @keydown.space.prevent
        required
      >
      </v-text-field>
      <slot></slot>
      <template v-if="showTextField">
        <v-text-field
          v-model="authNumber"
          :label="this.$i18n.t('verification_number')"
          color="#41D8E6"
          type="number"
          :error-messages="authErrorMsg"
          required
        >
          <template v-slot:append>
            <div class="v-timer">{{ minutes }}:{{ seconds }}</div>
            <div class="v-request-btn" @click="reSendSms">
              <button>{{ $t("request_btn") }}</button>
            </div>
          </template>
        </v-text-field>
      </template>
      <v-btn
        class="main-large-btn"
        :class="isKo ? '' : 'en-title'"
        elevation="0"
        color="#41D8E6"
        type="submit"
        @click="nextPhaseHandler"
        :disabled="!isPhoneNumberValid"
      >
        {{ buttonText }}
      </v-btn>
    </div>
  </div>
</template>

<script>
import { fetchNewPhoneNumber } from "@/api/user/index.js";
import CompleteAlert from "@/components/Common/ConfirmModal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";
import VueCountryDropdown from "vue-country-dropdown";

export default {
  components: {
    CompleteAlert,
    ErrorModal,
    VueCountryDropdown,
  },
  data() {
    return {
      phone: "",
      authNumber: null,
      timer: null,
      totalTime: 3 * 60,
      showTextField: false,
      isAlert: true,
      mobileNumberRule: [(v) => /^[0-9]/g.test(v) || this.$i18n.t("phone_error")],
      findErrorMsg: "",
      authErrorMsg: "",
      showErrorModal: false,
      showCompleteAlert: false,
      content: this.$i18n.t("send_text_modal_msg"),
      // content: this.$i18n.t("success_message_phone"),
      alert: this.$i18n.t("error_timeout"),
      error: this.$i18n.t("auth_error_modal"),
      selected: "",
      isKo: false,
    };
  },
  computed: {
    buttonText() {
      if (this.showTextField) {
        return this.$i18n.t("confirm_btn");
      } else {
        return this.$i18n.t("send_btn");
      }
    },
    isPhoneNumberValid() {
      return this.phone.length > 5 && this.phone.length < 16 && !this.selected.includes(this.$i18n.t("country_code"));
    },
    minutes() {
      const minutes = Math.floor(this.totalTime / 60);
      return this.padTime(minutes);
    },
    seconds() {
      const seconds = this.totalTime - this.minutes * 60;
      return this.padTime(seconds);
    },
    deviceId() {
      return this.$store.state.deviceId;
    },
  },
  methods: {
    onSelect({ name, iso2, dialCode }) {
      // console.log(name, iso2, dialCode);
      this.isKo = dialCode === "82" ? true : false;
      this.selected = `+${dialCode}`;
    },

    nextPhaseHandler() {
      if (!this.showTextField) {
        this.sendSms();
      } else {
        this.checkAuth();
      }
    },

    isConfirmed() {
      this.isAlert ? (this.showCompleteAlert = false) : this.$router.go(-1);
    },

    // getDeviceId() {
    //   /*global Webview*/
    //   /*eslint no-undef: "error"*/
    //   const message = {
    //     action: "getDeviceId",
    //   };

    //   Webview.getDeviceId(message);
    // },

    async sendSms() {
      // if (this.deviceId !== "") {
      const phoneNum = this.isKo && this.phone.charAt(0) !== "0" ? `0${this.phone}` : this.phone;
      // const type = this.$route.query.type === "kardio" ? this.$route.query.type : "human";
      const phoneNumber = {
        countryNumber: this.selected,
        phone: phoneNum,
      };
      // device: this.deviceId,
      // console.log(phoneNumber);
      try {
        const response = await fetchNewPhoneNumber(phoneNumber);
        // console.log(response);
        if (response.status === 204) {
          this.showTextField = true;
          this.showCompleteAlert = true;
          this.startTimer();
          this.totalTime = 3 * 60;
        }
      } catch (error) {
        console.log(error.response);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        } else if (error.response.status === 400) {
          this.error = this.$i18n.t("invalid_number");
          this.showErrorModal = true;
        } else {
          console.error(error);
          this.stopTimer();
          this.error = this.$i18n.t("error_send_msg");
          this.showErrorModal = true;
        }
      }
      // }
    },

    async checkAuth() {
      // this.$emit("nextPhaseHandler", 1);
      // if (this.deviceId !== "") {
      const phoneNum = this.isKo && this.phone.charAt(0) !== "0" ? `0${this.phone}` : this.phone;
      // const type = this.$route.query.type === "kardio" ? this.$route.query.type : "human";
      // console.log(koPhoneNum);
      try {
        const authNumbers = {
          countryNumber: this.selected,
          phone: phoneNum,
          code: this.authNumber,
        };
        // device: this.deviceId,
        const response = await fetchNewPhoneNumber(authNumbers);
        // console.log(response);
        if (response.status === 204) {
          this.isAlert = false;
          this.content = this.$i18n.t("success_message_phone");
          this.showCompleteAlert = true;
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        } else {
          this.stopTimer();
          this.showErrorModal = true;
          this.authErrorMsg = this.$i18n.t("invalid_error");
        }
      }
      // }
    },


    reSendSms() {
      this.totalTime = 3 * 60;
      this.sendSms();
      this.showTextField = true;
      this.showCompleteAlert = true;
    },

    countdown() {
      if (this.totalTime >= 1) {
        this.totalTime--;
      } else {
        this.totalTime = 0;
        this.resetTimer;
        this.isAlert = false;
        this.showCompleteAlert = true;
        this.content = this.$i18n.t("error_timeout");
        this.alertMessage = this.$i18n.t("error_timeout");
      }
    },

    startTimer() {
      clearInterval(this.timer);
      this.timer = setInterval(() => this.countdown(), 1000);
    },

    stopTimer() {
      clearInterval(this.timer);
    },

    padTime(time) {
      return (time < 10 ? "0" : "") + time;
    },

    isClicked() {
      this.showErrorModal = false;
    },
  },
  mounted() {
    // this.getDeviceId();
  },
};
</script>

<style lang="scss" scoped>
::v-deep .v-input {
  font-family: GilroyMedium !important;
  font-size: 20px;
}

::v-deep .v-text-field .v-label {
  top: -10px !important;
  font-size: 14px;
  color: #41d8e6;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 14px !important;
    line-height: 15px !important;
  }
}
::v-deep .v-input input {
  font-size: 20px !important;
  line-height: 23px;
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}

.main-large-btn {
  margin-top: 3px;
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

.v-timer {
  font-size: 18px;
  letter-spacing: -0.03em;
  color: #000 !important;
}
.v-request-btn {
  font-size: 16px;
  line-height: 20px;
  text-indent: 3px;
  white-space: nowrap;
  button {
    color: #41d8e6;
    font-weight: 700;
    letter-spacing: -0.03em;
  }
}

.country__wrapper {
  width: 100%;
  // display: flex;
  color: #41d8e6 !important;
}

.dropdown__wrapper {
  margin-bottom: 20px;
}

::v-deep .v-text-field input {
  padding: 8px 0 4px;
  line-height: 23px;
}

::v-deep .v-text-field .v-label {
  top: -15px !important;
  color: #a7a7a7;
  font-size: 14px;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}

::v-deep .vue-country-select .dropdown-list {
  max-height: 50vh !important;
  width: 100%;
  border: 1px solid #41d8e6;
  border-radius: 0 0 5px 5px;
  font-size: 16px;
  color: #000000;
  left: 0;
}

::v-deep .vue-country-select {
  border: none;
  border-bottom: 1px solid #a7a7a7 !important;
  border-radius: 0px !important;
  display: flex;
}

::v-deep .vue-country-select .current {
  font-size: 16px;
}

::v-deep .vue-country-select .dropdown {
  width: 100%;
  padding: 10px 0;
}

::v-deep .vue-country-select:focus-within {
  box-shadow: none !important;
  border-color: #41d8e6;
}

::v-deep .vue-country-select .dropdown:hover {
  background-color: transparent;
  border: none !important;
}

::v-deep .vue-country-select .dropdown-item {
  padding: 5px 3px;
}

::v-deep .vue-country-select .dropdown-item.highlighted {
  background-color: #c9f4f8;
}

::v-deep .theme--light.v-text-field > .v-input__control > .v-input__slot:before {
  border-color: #a7a7a7 !important;
}
</style>
