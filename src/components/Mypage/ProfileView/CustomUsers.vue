<template>
  <div class="users__wrapper">
    <div
      :class="idx === selectedUser ? 'active-profile' : 'profile__wrapper'"
      v-for="(user, idx) in groupUsers"
      :key="user.id"
      @click="userHandler(idx)"
    >
      <img
        :src="user.image || require('@/assets/images_assets/mypage-icon/default_profile.png')"
        alt="user profile"
        @error="replaceImage"
      />
      <div class="nickname">{{ user.nickname }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: { groupUsers: Array },
  data() {
    return {
      selectedUser: null,
    };
  },
  methods: {
    userHandler(id) {
      // console.log(id);
      if (Object.keys(this.groupUsers[id]).length !== 0) {
        this.selectedUser = id;
        sessionStorage.setItem("selectUser", id);
        this.$store.commit("setSubjectId", id);
        const ketoneMode = this.groupUsers[id].ketoneMode;
        localStorage.setItem("lastSelectedUser", this.groupUsers[id].id);
        localStorage.setItem("ketoneMode", ketoneMode);
        this.$router.go();
      }
    },
    replaceImage(e) {
      e.target.src = require("@/assets/images_assets/mypage-icon/default_profile.png");
    },
  },
  mounted() {
    // console.log(this.groupUsers);
    const subjects = JSON.parse(sessionStorage.getItem("subjects"));
    // console.log(subjects);
    this.selectedUser =
      JSON.parse(sessionStorage.getItem("selectUser")) !== null ? Number(sessionStorage.getItem("selectUser")) : null;
  },
};
</script>

<style lang="scss" scoped>
.users__wrapper {
  width: 100%;
  // height: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 40px 10px;
  padding: 5px 30px;
}

.profile__wrapper {
  width: 90px;
  height: 106px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  img {
    border-radius: 100%;
    border: 5px solid transparent;
    width: 100%;
    height: 90px;
    object-fit: cover;
  }
}

.active-profile {
  width: 90px;
  height: 90px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  img {
    border: 5px solid #41d8e6;
    border-radius: 100%;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.nickname {
  font-size: 16px;
  font-weight: 500;
  line-height: 18px;
}

@media (max-width: 390px) {
  .profile__wrapper {
    width: 70px;
    height: 90px;
    img {
      height: 70px;
    }
  }
  .active-profile {
    width: 70px;
    height: 70px;
  }
}
</style>
