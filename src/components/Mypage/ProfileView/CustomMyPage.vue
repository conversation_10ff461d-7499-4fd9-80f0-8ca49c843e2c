<template>
  <div class="group__wrapper">
    <div class="group-container">
      <!-- :class="['active','group']" -->
      <div
        :class="group.id === selectedGroup ? 'active' : 'group'"
        v-for="group in groups"
        :key="group.id"
        @click="groupHandler(group.id)"
      >
        {{ group.text }}
      </div>
      <div class="user-edit-wrapper">
        <!-- <router-link class="edit-btn__wrapper" to="/profile/edit/sub"> -->
        <div class="edit-btn__wrapper" @click="checkSelcetUser">
          <v-icon class="edit-icon">$edit</v-icon>
          <div class="btn-title">{{ $t("profile_title") }}</div>
        </div>
        <!-- </router-link> -->
        <div class="edit-btn__wrapper" @click="checkSubUser">
          <v-icon class="edit-icon">
            $user_plus
          </v-icon>
          <div class="btn-title">{{ $t("add_user") }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectedGroup: 0,
      groups: [
        { id: 0, text: "Healthy" },
        { id: 1, text: "HyperTension" },
        { id: 2, text: "Diabetes" },
        { id: 3, text: "CKD" },
      ],
    };
  },
  methods: {
    groupHandler(id) {
      // console.log(id);
      this.selectedGroup = id;
      this.$emit("selectedGroupId", id);
    },
    checkSelcetUser() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      // console.log(subjects);
      if (subjects !== null) {
        const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
        const subjectId = subjects[selectedId].id;
        this.$router.push({ name: "SubProfile", params: { id: subjectId } });
        // this.$router.push({ name: "SubProfileEdit" });
      }
    },
    checkSubUser() {
      this.$router.push({ name: "SubUserAdd", params: { type: "kardio" } });
    },
  },
  mounted() {
    // this.selectedGroup =
    //   this.$store.state.groupId === null ? Number(sessionStorage.getItem("groupId")) : this.$store.state.groupId;
    this.selectedGroup = Number(sessionStorage.getItem("groupId"));
  },
};
</script>

<style lang="scss" scoped>
.group__wrapper {
  width: 100%;
  display: flex;
  padding: 40px 0 30px;
}
.group-container {
  width: 100%;
  display: flex;
  gap: 30px 5px;
  flex-wrap: wrap;
  padding: 0 5vw;
}

.group {
  height: 35px;
  padding: 0 14px;
  font-size: 20px;
  line-height: 22px;
  font-family: GilroyMedium;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #646464;
  background-color: #ededed;
  border-radius: 20px;
  border: 5px solid #c9f4f8;
}

.active {
  background-color: #ffffff;
  border: 5px solid #41d8e6;
  border-radius: 30px;
  padding: 0 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #000000;
  font-family: GilroyBold;
  line-height: 22px;
  font-size: 20px;
  height: 35px;
}

.user-edit-wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding-top: 25px;
}

.edit-btn__wrapper {
  display: flex;
  height: 29px;
}

.edit-icon {
  margin-top: 2px;
}

.btn-title {
  font-size: 16px;
  font-weight: 400;
  color: #646464;
  letter-spacing: -0.03em;
}

@media (max-width: 390px) {
  .group {
    font-size: 18px;
  }
  .active {
    font-size: 18px;
  }
  .btn-title {
    padding-top: 2px;
    font-size: 14px;
  }
}
</style>
