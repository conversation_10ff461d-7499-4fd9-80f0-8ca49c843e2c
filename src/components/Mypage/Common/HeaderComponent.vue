<template>
  <div>
    <div class="header-left">
      <v-icon @click="moveBack">$back_btn_bold</v-icon>
    </div>
    <div class="mypage-header-wrapper">
      <div class="header-center">{{ pageName }}</div>
      <div class="header-right"></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    pageName: String,
  },
  data() {
    return {};
  },
  methods: {
    moveBack() {
      if (
        this.pageName === this.$t("profile_email") ||
        this.pageName === this.$t("profile_phone") ||
        this.pageName === this.$t("profile_name") ||
        this.pageName === this.$t("profile_pwd") ||
        this.pageName === this.$t("profile_survey")
      ) {
        this.$router.push({ path: "/mypage/profile" });
      } else if (this.pageName === this.$t("delete_account__btn")) {
        this.$router.push({ path: "/mypage/settings" });
      } else {
        this.$router.push({ path: "/mypage" });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.mypage-header-wrapper,
.header-left {
  padding-left: 30px;
  height: 65px;
  background-color: #c9f4f8;
  display: flex;
}

.mypage-header-wrapper {
  width: 100%;
  padding-right: 30px;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.header-left {
  position: absolute;
}

.header-center {
  font-family: Noto Sans KR;
  font-style: normal;
  font-weight: 500;
  font-size: 22px;
  line-height: 29px;
  letter-spacing: -0.03em;
}
</style>
