<template>
  <div>
    <div class="dp-30">
      <div>
        <v-text-field
          v-model="userId"
          :label="this.$i18n.t('input_id')"
          color="#41D8E6"
          type="text"
          :error-messages="userIdErrorMsg"
          :placeholder="this.$i18n.t('input_id_placeholder')"
          required
          @blur="focusOutHandler"
        >
          <!-- maxLength="10" -->
        </v-text-field>
      </div>
      <v-btn
        class="main-large-btn"
        elevation="0"
        color="#41D8E6"
        type="submit"
        :disabled="!isAvaliableUserId"
        @click="nextPhaseHandler"
      >
        {{ $t("next_btn") }}
      </v-btn>
    </div>
  </div>
</template>

<script>
import API from "@/api/auth/index.js";

export default {
  data() {
    return {
      userId: this.$store.state.join.account || "",
      userIdErrorMsg: "",
      isAvaliableUserId: false,
    };
  },

  watch: {
    userId(newVal) {
      // const id = newVal.replace(/\s/g, "");
      // console.log(newVal.includes(" "));
      if (this.userId.length === 0 || this.userIdValidationCheck(newVal)) {
        if (4 < newVal.length && newVal.length < 11 && this.userIdValidationCheck(newVal)) {
          this.userIdErrorMsg = "";
          this.isAvaliableUserId = true;
        } else if (newVal.length >= 11) {
          this.userIdErrorMsg = this.$i18n.t("input_id_placeholder");
          this.isAvaliableUserId = false;
        }
      } else {
        this.userIdErrorMsg = this.$i18n.t("input_id_placeholder");
        this.isAvaliableUserId = false;
      }
      if (newVal.includes(" ")) {
        this.userIdErrorMsg = this.$i18n.t("no_spaces");
        this.isAvaliableUserId = false;
      }
    },
  },
  methods: {
    async nextPhaseHandler() {
      const id = this.userId.replace(/\s/g, "");
      // console.log(id, this.$route.query.type);
      const type = this.$route.query.type === "kardio" ? "kardio" : "human";
      try {
        const userId = {
          account: id,
        };
        const { data, status } = await API.fetchCheckUserId(type, userId);
        // console.log(data, status);
        if (status === 201) {
          if (!data.accountExists) {
            this.$store.commit("GET_USERID", this.userId);
            // console.log(this.$store.state.join.account);
            this.$emit("nextPhaseHandler", 2);
          } else {
            this.userIdErrorMsg = this.$i18n.t("join_id_invalid");
          }
        }
      } catch (error) {
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        } else {
          console.log(error);
          this.userIdErrorMsg = this.$i18n.t("input_id_placeholder");
        }
      }
      // this.$store.commit("GET_USERID", this.userId);
      // this.$emit("nextPhaseHandler", 2);
    },
    userIdValidationCheck(userId) {
      return userId.length >= 4 && userId.length <= 10 && /^[a-zA-Z0-9]*$/.test(userId);
    },
    focusOutHandler() {
      // this.userIdErrorMsg = "";
    },
  },
  mounted() {
    // console.log(this.userIdValidationCheck(this.userId));
    this.userIdValidationCheck(this.userId)
      ? ((this.userIdErrorMsg = ""), (this.isAvaliableUserId = true))
      : (this.isAvaliableUserId = false);
  },
};
</script>

<style scoped>
::v-deep .theme--light.v-input input,
.theme--light.v-input textarea {
  color: #000000;
}

::v-deep .v-input input {
  font-size: 20px;
  font-weight: 500;
  font-family: "GilroyMedium";
  line-height: 30px;
}

::v-deep .v-text-field input {
  padding: 0 !important;
}
</style>
