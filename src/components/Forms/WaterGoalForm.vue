<template>
  <div>
    <div class="userdetail-text-field__items">
      <v-text-field
        color="#41d8e6"
        v-model="goalWater"
        :placeholder="this.$i18n.t('target_water_description')"
        :persistent-placeholder="true"
        type="number"
        inputmode="numeric"
        pattern="[0-9]*"
      >
        <template v-slot:label>
          <div class="text-field-label">{{ $t("target_water") }}</div>
        </template>
      </v-text-field>
    </div>
  </div>
</template>

<script>
export default {
  props: { initialWater: Number },
  data() {
    return {
      goalWater: 0,
    };
  },
  watch: {
    goalWater() {
      this.$emit("waterGoalInputHandler", this.goalWater);
    },
  },
  mounted() {
    // console.log(this.initialWater);
    this.goalWater = this.initialWater || 0;
  },
};
</script>

<style lang="scss" scoped>
::v-deep .v-text-field .v-label {
  top: -30px !important;
  font-weight: 500 !important;
  font-size: 18px;
  line-height: 25px;
}

::v-deep .v-input {
  font-family: GilroyMedium !important;
  margin-bottom: 20px;
  font-size: 20px;
  letter-spacing: -0.03em;
}

::v-deep .v-text-field input {
  padding: 0;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 14px !important;
    letter-spacing: -0.03em;
    padding: 0;
    font-weight: 400 !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 25px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
::v-deep .v-text-field input {
  font-size: 20px !important;
  font-family: GilroyMedium;
}
</style>
