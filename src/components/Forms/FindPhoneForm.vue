<template>
  <div>
    <div class="phone-form__wrapper">
      <div class="dropdown__wrapper">
        <vue-country-dropdown
          @onSelect="onSelect"
          :disabledFetchingCountry="false"
          :preferredCountries="['KR', 'US', 'GB']"
          :enabledFlags="true"
          :enabledCountryCode="true"
          :showNameInput="true"
        />
      </div>
      <div>
        <v-text-field
          v-model="phone"
          :label="this.$i18n.t('input_phone')"
          color="#41D8E6"
          type="tel"
          maxlength="15"
          :rules="mobileNumberRule"
          @keydown.space.prevent
          required
        >
        </v-text-field>
      </div>
    </div>
  </div>
</template>

<script>
import VueCountryDropdown from "vue-country-dropdown";

export default {
  components: {
    VueCountryDropdown,
  },
  data() {
    return {
      phone: "",
      authNumber: "",
      mobileNumberRule: [(v) => /^[0-9]/g.test(v) || this.$i18n.t("phone_error")],
      selected: "",
      isKo: false,
    };
  },
  methods: {
    onSelect({ name, iso2, dialCode }) {
      // console.log(name, iso2, dialCode);
      this.isKo = dialCode === "82" ? true : false;
      this.selected = `+${dialCode}`;
    },
    isPhoneNumberValid() {
      return this.phone.length > 5 && this.phone.length < 16 && !this.selected.includes(this.$i18n.t("country_code"));
    },
  },

  watch: {
    phone(newVal) {
      const phoneNum = this.isKo && newVal.charAt(0) !== "0" ? `0${this.phone}` : newVal;
      const authNumbers = {
        phone: phoneNum,
        code: this.selected,
        valid: this.isPhoneNumberValid(),
      };
      this.$emit("phoneInputHandler", authNumbers);
    },
  },
};
</script>

<style lang="scss" scoped>
.phone-form__wrapper {
  padding: 0 30px;
}

::v-deep .v-input {
  font-family: GilroyMedium !important;
  font-size: 20px;
}

::v-deep .v-menu__content .theme--light .menuable__content__active {
  padding: 0 !important;
  position: absolute !important;
  box-shadow: none !important;
  top: 310px !important;
}

::v-deep .v-list-item {
  &:hover {
    background-color: #c9f4f8 !important;
  }
}

::v-deep .v-list-item__content {
  text-align: left;
  color: #000000 !important;
  caret-color: #41d8e6 !important;
}
::v-deep .v-list-item--active {
  background-color: #c9f4f8 !important;
}

.dropdown__wrapper {
  margin-bottom: 20px;
}

.country {
  width: 60%;
  font-weight: 500;
}
.modal {
  text-align: left;
}
.main-large-btn {
  margin-top: 3px;
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

.v-timer {
  font-size: 18px;
  letter-spacing: -0.03em;
  color: #000 !important;
}
.v-request-btn {
  font-size: 16px;
  line-height: 20px;
  text-indent: 3px;

  button {
    color: #41d8e6;
    font-weight: 600;
    letter-spacing: -0.03em;
  }
}
::v-deep .v-text-field input {
  padding: 8px 0 4px;
  line-height: 23px;
}

::v-deep .v-text-field .v-label {
  top: -15px !important;
  color: #a7a7a7;
  font-size: 14px;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}

::v-deep .vue-country-select .dropdown-list {
  max-height: 50vh !important;
  width: 100%;
  border: 1px solid #41d8e6;
  border-radius: 0 0 5px 5px;
  font-size: 16px;
  color: #000000;
  left: 0;
}

::v-deep .vue-country-select {
  border: none;
  border-bottom: 1px solid #a7a7a7 !important;
  border-radius: 0px !important;
  display: flex;
}

::v-deep .vue-country-select .current {
  font-size: 16px;
}

::v-deep .vue-country-select .dropdown {
  width: 100%;
  padding: 10px 0;
}

::v-deep .vue-country-select:focus-within {
  box-shadow: none !important;
  border-color: #41d8e6;
}

::v-deep .vue-country-select .dropdown:hover {
  background-color: transparent;
  border: none !important;
}

::v-deep .vue-country-select .dropdown-item {
  padding: 5px 3px;
}

::v-deep .vue-country-select .dropdown-item.highlighted {
  background-color: #c9f4f8;
}

::v-deep .theme--light.v-text-field > .v-input__control > .v-input__slot:before {
  border-color: #a7a7a7 !important;
}
</style>
