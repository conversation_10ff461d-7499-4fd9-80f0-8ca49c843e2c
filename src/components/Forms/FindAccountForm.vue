<template>
  <div class="birth-form__container">
    <div class="age-input__title" v-html="this.$i18n.t('cym_account')"></div>
    <div class="userdetail-text-field__items">
      <div class="name-input__item">
        <v-text-field v-model="account" type="text" color="#41d8e6" :error-messages="accountError"> </v-text-field>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      account: null,
      accountError: "",
      accountValid: false,
    };
  },
  methods: {
    accountValidation(account) {
      return account.length > 4 && account.length < 11;
    },
  },
  watch: {
    account(newVal) {
      this.accountValidation(newVal)
        ? ((this.accountValid = true), (this.accountError = ""))
        : ((this.accountValid = false), (this.accountError = this.$i18n.t("invalid")));
      const account = { account: this.account, valid: this.accountValid };
      this.$emit("accountInputHandler", account);
    },
  },
};
</script>

<style lang="scss" scoped>
.birth-form__container {
  width: 100%;
  height: 90px;
  margin-top: 10px;
  padding: 0 30px;
}

.userdetail-text-field__items {
  display: flex;
}

.age-input__items {
  display: flex;
  padding-top: 15px;
}

.age {
  padding: 0 12px;
}
.text-field-label {
  padding-bottom: 14px;
}

.age-input__title {
  text-align: left;
  color: #a7a7a7 !important;
  font-size: 14px;
  transition: 0.1s ease-in-out;
}

.name-input__item {
  width: 100%;
}

::v-deep .v-text-field input {
  padding: 0;
}
::v-deep .v-input {
  font-family: GilroyMedium !important;
  margin-bottom: 20px;
  font-size: 20px;
}
::v-deep .v-text-field {
  padding-top: 5px;
}
::v-deep .v-input input {
  max-height: 25px;
}
::v-deep v-text-field input {
  font-size: 18px;
}
::v-deep .v-text-field .v-label {
  top: -15px !important;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 14px !important;
    color: #a7a7a7;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
</style>
