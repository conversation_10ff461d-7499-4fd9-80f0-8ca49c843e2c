<template>
  <div>
    <div class="option-comment">
      <span>{{ subTitleValue }}</span>
    </div>
    <div class="form__wrapper">
      <v-checkbox
        class="no-disease"
        off-icon="$check_box"
        on-icon="$check_box_inactive"
        @click="noDiseaseHandler"
        v-model="noDisease"
        color="#41D8E6"
        v-if="currentStateIdx === 2"
      >
        <template v-slot:label>
          <div class="checkbox-label">
            {{ $t("no_disease") }}
          </div>
        </template>
      </v-checkbox>
      <v-checkbox
        v-for="(value, idx) in survey.choice"
        :key="idx"
        @click="checkBoxHandler(answers[survey.id].choice)"
        v-model="answers[survey.id].choice"
        off-icon="$check_box"
        on-icon="$check_box_inactive"
        color="#41D8E6"
        v-show="currentStateIdx === 2 ? value.id !== 10 : true"
        :value="idx + 1"
        :multiple="isMultiple"
        :disabled="noDisease && currentStateIdx === 2"
        :class="{ 'disabled-checkbox': noDisease && currentStateIdx === 2 }"
      >
        <template v-slot:label>
          <div class="checkbox-label">
            {{ $t(value.text) }}
          </div>
        </template>
      </v-checkbox>
      <v-checkbox
        v-model="purposeInput"
        v-if="currentStateIdx === 0"
        off-icon="$check_box"
        on-icon="$check_box_inactive"
        color="#41D8E6"
      >
        <template v-slot:label>
          <div class="checkbox-label">{{ $t("input_etc") }}</div>
        </template>
      </v-checkbox>
      <v-textarea
        :placeholder="this.$i18n.t('etc_placeholder')"
        v-model="purposeEtc"
        v-if="purposeInput && currentStateIdx === 0"
        height="100"
        outlined
        name="input-7-4"
        minlength="5"
        maxlength="50"
      ></v-textarea>
      <!-- @change="purposeEtcHandler" -->
      <!-- @change="diseaseInputHandler"  -->
      <v-checkbox
        v-model="diseaseInput"
        v-if="currentStateIdx === 2"
        off-icon="$check_box"
        on-icon="$check_box_inactive"
        color="#41D8E6"
        :class="{ 'disabled-checkbox': noDisease && currentStateIdx === 2 }"
      >
        <template v-slot:label>
          <div class="checkbox-label">{{ $t("input_etc") }}</div>
        </template>
      </v-checkbox>
      <v-textarea
        :placeholder="this.$i18n.t('etc_placeholder')"
        v-model="diseaseEtc"
        :disabled="noDisease"
        v-if="diseaseInput && currentStateIdx === 2"
        height="100"
        outlined
        name="input-7-4"
        minlength="5"
        maxlength="50"
      ></v-textarea>
    </div>
    <div class="fixed-btn">
      <v-btn
        color="#41D8E6"
        class="main-large-btn"
        @click="nextPhaseHandler"
        elevation="0"
        :disabled="!healthInfoValid"
      >
        {{ $t("next_btn") }}
      </v-btn>
    </div>
  </div>
</template>

<script>
import API from "@/api/survey/index.js";

export default {
  name: "SurveyForm",
  props: {
    currentStateIdx: Number,
    checkedData: Object,
    questions: Array,
  },
  data() {
    return {
      subjectId: 0,
      customReason: "",
      purposeInput: false,
      diseaseInput: false,
      // healthInfoValid: false,
      // questions: {},
      questionsArr: [],
      survey: {},
      isMultiple: false,
      purposeEtc: "",
      diseaseEtc: "",
      noDisease: false,
      answers: [
        { questionId: 0, choice: [] },
        { questionId: 1, choice: [] },
        { questionId: 2, choice: [] },
        { questionId: 3, choice: [] },
        { questionId: 4, choice: [] },
        { questionId: 5, choice: [] },
        { questionId: 6, choice: [] },
        { questionId: 7, choice: [] },
        { questionId: 8, choice: [] },
      ],
      labelsIndex: 0,
    };
  },
  watch: {
    noDisease(newVal) {
      // console.log(newVal);
    },
    questions() {
      this.getSurveyCheckedData();
    },
    currentStateIdx(newVal) {
      // console.log("currentStateIdx", this.answers[newVal + 1].choice.length);
      localStorage.setItem("surveyIdx", newVal);
      this.survey = this.questions[newVal];
      this.isMultiple = this.questions[newVal].isMultipleChoice;
      // this.answers[newVal + 1].choice.length !== 0 ? (this.healthInfoValid = true) : (this.healthInfoValid = false);

      // console.log(this.answers[newVal + 1].choice.length);
      // console.log(newVal, this.survey.isMultipleChoice, this.isMultiple);
      // this.healthInfoValid = false;
    },
    purposeInput(newVal) {
      // console.log(newVal);
      // if (newVal) this.purposeEtc > 2 ? (this.healthInfoValid = true) : (this.healthInfoValid = false);
    },
    diseaseInput(newVal) {
      // console.log(newVal);
      // if (newVal) this.diseaseEtc > 2 ? (this.healthInfoValid = true) : (this.healthInfoValid = false);
    },
    purposeEtc(newVal) {
      // console.log("purposeEtc", newVal.length > 2);
      // newVal.length > 2 ? (this.healthInfoValid = true) : (this.healthInfoValid = false);
    },
    diseaseEtc(newVal) {
      // console.log("purposeEtc", newVal.length > 2);
      // newVal.length > 2 ? (this.healthInfoValid = true) : (this.healthInfoValid = false);
    },
    reloadSurveyData(newVal) {
      // console.log(newVal);
    },
  },
  computed: {
    subTitleValue() {
      if (this.isMultiple) {
        return this.$i18n.t("duplicate_check");
      } else {
        return this.$i18n.t("single_check");
      }
      // return this.$i18n.t("single_check");
    },
    healthInfoValid() {
      if (this.currentStateIdx === 0) {
        if (this.purposeInput) return this.purposeEtc.length >= 5;
        return this.answers[1].choice.length !== 0;
      }
      if (this.currentStateIdx === 1) return this.answers[2].choice !== null;
      if (this.currentStateIdx === 2) {
        if (this.noDisease) {
          return true;
        } else {
          if (this.diseaseInput) return this.diseaseEtc.length >= 5;
          return this.answers[3].choice.length !== 0;
        }
      }
      if (this.currentStateIdx === 3) {
        return this.answers[4].choice !== null;
      }
      if (this.currentStateIdx === 4) {
        return this.answers[5].choice !== null;
      }
      return false;
    },
  },
  methods: {
    getSurveyData() {
      this.survey = this.questions[this.currentStateIdx];
      this.isMultiple = this.questions[this.currentStateIdx].isMultipleChoice;
      // console.log("survey:", this.survey, this.isMultiple);
      // console.log(this.questions[2])
      this.$nextTick(() => this.getSurveyCheckedData());
    },
    // TODO: Survey.vue에서 호출하고 props로 내려주기
    getSurveyCheckedData() {
      // console.log(this.checkedData);
      if (this.checkedData.resultAnswers !== null) {
        try {
          const questionArr = this.checkedData.resultAnswers.question;
          this.questionsArr = this.checkedData.resultAnswers.question;
          // console.log(this.checkedData);
          this.answers.map((ans) => {
            // console.log(ans);
            const q = questionArr.find((q) => q.id === ans.questionId);
            if (q) {
              switch (q.id) {
                case 1:
                  return (ans.choice = q.choice.map((c) => c.id));
                case 2:
                  return (ans.choice = Number(q.choice.map((c) => c.id - 5).join()));
                case 3:
                  return (ans.choice = q.choice.map((c) => c.id - 9));
                case 4:
                  return (ans.choice = Number(q.choice.map((c) => c.id - 19).join()));
                case 5:
                  // console.log(q);
                  return (ans.choice = Number(q.choice.map((c) => c.id - 23).join()));
                default:
                  return;
              }
            }
            return ans;
          });
          // console.log(this.answers);
          // console.log("data question obj arr:", this.questionsArr);
          if (questionArr[0]?.textAnswer[0]?.text) {
            this.purposeInput = true;
            this.purposeEtc = questionArr[0].textAnswer[0].text;
          }
          if (questionArr[2]?.textAnswer[0]?.text) {
            this.diseaseInput = true;
            this.diseaseEtc = questionArr[2].textAnswer[0].text;
          }
          this.noDisease = this.answers[3].choice.includes(1) ? true : false;
        } catch (e) {
          console.log(e);
        }
      }
    },

    noDiseaseHandler() {
      // console.log(this.noDisease);
      // this.answers[3].choice.includes(1)
      // this.healthInfoValid = true;
      this.noDisease
        ? ((this.answers[3].choice = [1]), (this.diseaseEtc = ""), (this.diseaseInput = false))
        : (this.answers[3].choice = this.answers[3].choice.filter((value) => value !== 1));
      // this.$nextTick(() => {
      //   this.healthInfoValid = this.noDisease || this.answers[3].choice.length !== 0 ? true : false;
      // });
      // console.log(this.answers[3].choice.length);
    },

    checkBoxHandler(checkedArr) {
      // console.log(checkedArr, this.currentStateIdx);
      // checkedArr === null || checkedArr.length === 0 ? (this.healthInfoValid = false) : (this.healthInfoValid = true);
      // this.smoking = checkedArr;
      // if (this.survey.choice.isMultiple) {
      //   checkedArr.length !== 0 ? (this.healthInfoValid = true) : (this.healthInfoValid = false);
      // } else {
      //   checkedArr !== null ? (this.healthInfoValid = true) : (this.healthInfoValid = false);
      // }
    },

    // TODO: Refactoring
    // labelsIndex별 나눠진 함수 하나로 통일
    // post method, delete method 하나의 함수로 & 스코프 밖으로 빼기

    async nextPhaseHandler() {
      let value = this.currentStateIdx;
      this.labelsIndex = value += 1;
      const subjectId = Number(this.subjectId);
      const surveyData = [
        {
          questionId: this.labelsIndex,
          choiceAnswers: this.answers[this.labelsIndex].choice,
        },
      ];
      // console.log(surveyData);

      // 다음버튼 클릭 시 console.log(this.answers[1].choice === localStorage 값이랑 비교)

      // 앱 사용목적(multiple & 주관식)
      if (this.labelsIndex === 1) {
        // console.log(this.labelsIndex);
        this.purpose = this.answers;
        this.purposeEtc !== "" &&
        this.questionsArr[this.labelsIndex - 1]?.textAnswer[0]?.text !== this.purposeEtc
          ? (surveyData[0].textAnswer = this.purposeEtc)
          : null;
        surveyData[0].choiceAnswers.length === 0 ? delete surveyData[0].choiceAnswers : null;
        const survey = {
          subjectId: subjectId,
          answers: surveyData,
        };
        // console.log(survey);
        // console.log("-----------", this.answers[this.labelsIndex].choice);
        // console.log(this.questionsArr[this.labelsIndex - 1]?.textAnswer[0]?.text !== this.purposeEtc);

        const choicesAnswer = this.answers[this.labelsIndex].choice;
        const checkedArr = this.questionsArr[this.labelsIndex - 1]?.choice;
        if (checkedArr !== undefined) {
          const addedArr = choicesAnswer.filter(
            (choice) => !checkedArr.some((item) => item.id === choice)
          );
          const deletedArr = checkedArr
            .filter((item) => !choicesAnswer.includes(item.id))
            .map((item) => item.choiceAnswer[0].id);

          // console.log("Added:", addedArr);
          // console.log("Deleted:", deletedArr);
          if (addedArr.length !== 0) {
            surveyData[0].choiceAnswers = addedArr;
            try {
              const survey = {
                subjectId: subjectId,
                answers: surveyData,
              };
              // console.log(survey);
              const { data, status } = await API.postSurveyData(survey);
              // console.log(data, status);
              if (status === 201) {
                if (deletedArr.length !== 0) {
                  // delete
                  try {
                    const deleteData = {
                      subjectId: subjectId,
                      choiceAnswerId: deletedArr,
                    };
                    const { data, status } = await API.deleteSurveyData(deleteData);
                    if (status === 204) {
                      this.$emit("nextPhaseHandler", this.labelsIndex);
                    }
                  } catch (e) {
                    console.log(e);
                  }
                }
                this.$emit("nextPhaseHandler", this.labelsIndex);
              }
            } catch (error) {
              if (error.response.status === 429) {
                this.$store.commit("setoverReqModal", true);
              }
              console.log(error);
            }
          } else if (deletedArr.length !== 0 && addedArr.length === 0) {
            // delete
            try {
              const deleteData = {
                subjectId: subjectId,
                choiceAnswerId: deletedArr,
              };
              const { data, status } = await API.deleteSurveyData(deleteData);
              if (status === 204) {
                this.$emit("nextPhaseHandler", this.labelsIndex);
              }
            } catch (error) {
              if (error.response.status === 429) {
                this.$store.commit("setoverReqModal", true);
              }
              console.log(error);
            }
          }
          // console.log("nothing changed");
          this.$emit("nextPhaseHandler", this.labelsIndex);
        } else {
          // console.log("just first post");
          try {
            const { data, status } = await API.postSurveyData(survey);
            // console.log(data, status);
            if (status === 201) {
              this.$emit("nextPhaseHandler", this.labelsIndex);
            }
          } catch (error) {
            if (error.response.status === 429) {
              this.$store.commit("setoverReqModal", true);
            }
            console.log(error);
          }
        } // this.$store.commit("GET_HEALTH_INFO_PURPOSE", this.answers);
        this.$emit("nextPhaseHandler", this.labelsIndex);
      }

      // 평소 운동량
      if (this.labelsIndex === 2) {
        // console.log(surveyData[0].choiceAnswers);
        surveyData[0].choiceAnswers = [surveyData[0].choiceAnswers + 5];
        const survey = {
          subjectId: subjectId,
          answers: surveyData,
        };
        // console.log(survey);

        // console.log("-----------------------", this.answers[this.labelsIndex].choice);
        // console.log(this.questionsArr[this.labelsIndex - 1]?.choice[0]);

        const choicesAnswer = this.answers[this.labelsIndex].choice + 5;
        const checkedArr = this.questionsArr[this.labelsIndex - 1]?.choice[0];
        if (checkedArr !== undefined) {
          // console.log("survey modify");
          const removedItem =
            checkedArr.id !== choicesAnswer ? [checkedArr.choiceAnswer[0].id] : [];
          const addedItem = checkedArr.id !== choicesAnswer ? [choicesAnswer] : [];
          // console.log(removedItem, addedItem);

          if (addedItem.length !== 0) {
            // 단수선택 항목은 새로운 선택이 있으면 반드시 삭제도 존재
            surveyData[0].choiceAnswers = addedItem;
            try {
              const survey = {
                subjectId: subjectId,
                answers: surveyData,
              };
              const { data, status } = await API.postSurveyData(survey);
              // console.log(data, status);
              if (status === 201) {
                // delete
                try {
                  const deleteData = {
                    subjectId: subjectId,
                    choiceAnswerId: removedItem,
                  };
                  const { data, status } = await API.deleteSurveyData(deleteData);
                  if (status === 204) {
                    this.$emit("nextPhaseHandler", this.labelsIndex);
                  }
                } catch (error) {
                  if (error.response.status === 429) {
                    this.$store.commit("setoverReqModal", true);
                  }
                  console.log(error);
                }
              }
            } catch (e) {
              console.log(e);
            }
          }
          // console.log("nothing changed");
          this.$emit("nextPhaseHandler", this.labelsIndex);
        } else {
          // console.log("first survey");
          try {
            const { data, status } = await API.postSurveyData(survey);
            // console.log(data, status);
            if (status === 201) {
              const choicesData = surveyData[0].choiceAnswers;
              // console.log(choicesData);
              this.$emit("nextPhaseHandler", this.labelsIndex);
            }
          } catch (error) {
            console.log(error);
            if (error.response.status === 429) {
              this.$store.commit("setoverReqModal", true);
            }
          }
        }
        this.$emit("nextPhaseHandler", this.labelsIndex);
      }

      // 만성 질환(multiple & 주관식)
      if (this.labelsIndex === 3) {
        surveyData[0].choiceAnswers = surveyData[0].choiceAnswers.map((choiceId) => choiceId + 9);
        this.diseaseEtc !== "" ? (surveyData[0].textAnswer = this.diseaseEtc) : null;
        surveyData[0].choiceAnswers.length === 0 ? delete surveyData[0].choiceAnswers : null;
        const survey = {
          subjectId: subjectId,
          answers: surveyData,
        };
        // console.log(survey);

        // console.log("-----------------------", this.answers[this.labelsIndex].choice);
        // console.log(this.questionsArr[this.labelsIndex - 1]?.choice);

        // this.answers[this.labelsIndex].choice.map((i) => console.log(i));

        const choicesAnswer = this.answers[this.labelsIndex].choice.map((i) => i + 9);
        const checkedArr = this.questionsArr[this.labelsIndex - 1]?.choice;
        // console.log(checkedArr);
        if (checkedArr !== undefined) {
          const addedArr = choicesAnswer.filter(
            (choice) => !checkedArr.some((item) => item.id === choice)
          );
          const deletedArr = checkedArr
            .filter((item) => !choicesAnswer.includes(item.id))
            .map((item) => item.choiceAnswer[0].id);

          // console.log("Added:", addedArr);
          // console.log("Deleted:", deletedArr);
          if (addedArr.length !== 0) {
            surveyData[0].choiceAnswers = addedArr;
            try {
              const survey = {
                subjectId: subjectId,
                answers: surveyData,
              };
              const { data, status } = await API.postSurveyData(survey);
              // console.log(data, status);
              if (status === 201) {
                if (deletedArr.length !== 0) {
                  // delete
                  try {
                    const deleteData = {
                      subjectId: subjectId,
                      choiceAnswerId: deletedArr,
                    };
                    const { data, status } = await API.deleteSurveyData(deleteData);
                    if (status === 204) {
                      this.$emit("nextPhaseHandler", this.labelsIndex);
                    }
                  } catch (error) {
                    console.log(error);
                    if (error.response.status === 429) {
                      this.$store.commit("setoverReqModal", true);
                    }
                  }
                }
                this.$emit("nextPhaseHandler", this.labelsIndex);
              }
            } catch (e) {
              console.log(e);
            }
          } else if (deletedArr.length !== 0 && addedArr.length === 0) {
            // delete
            try {
              const deleteData = {
                subjectId: subjectId,
                choiceAnswerId: deletedArr,
              };
              const { data, status } = await API.deleteSurveyData(deleteData);
              if (status === 204) {
                this.$emit("nextPhaseHandler", this.labelsIndex);
              }
            } catch (error) {
              if (error.response.status === 429) {
                this.$store.commit("setoverReqModal", true);
              }
              console.log(error);
            }
          }
          // console.log("nothing changed");
          this.$emit("nextPhaseHandler", this.labelsIndex);
        } else {
          // console.log("first survey post");
          try {
            const { data, status } = await API.postSurveyData(survey);
            // console.log(data, status);
            if (status === 201) {
              this.$emit("nextPhaseHandler", this.labelsIndex);
            }
          } catch (error) {
            if (error.response.status === 429) {
              this.$store.commit("setoverReqModal", true);
            }
            console.log(error);
          }
        }
        this.$emit("nextPhaseHandler", this.labelsIndex);
      }

      // 음주량
      if (this.labelsIndex === 4) {
        surveyData[0].choiceAnswers = [surveyData[0].choiceAnswers + 19];
        const survey = {
          subjectId: subjectId,
          answers: surveyData,
        };
        // console.log(survey);

        const choicesAnswer = this.answers[this.labelsIndex].choice + 19;
        const checkedArr = this.questionsArr[this.labelsIndex - 1]?.choice[0];
        if (checkedArr !== undefined) {
          // console.log("survey modify");
          const removedItem =
            checkedArr.id !== choicesAnswer ? [checkedArr.choiceAnswer[0].id] : [];
          const addedItem = checkedArr.id !== choicesAnswer ? [choicesAnswer] : [];
          // console.log(removedItem, addedItem);
          if (addedItem.length !== 0) {
            // 단수선택 항목은 새로운 선택이 있으면 반드시 삭제도 존재
            surveyData[0].choiceAnswers = addedItem;
            try {
              const survey = {
                subjectId: subjectId,
                answers: surveyData,
              };
              const { data, status } = await API.postSurveyData(survey);
              // console.log(data, status);
              if (status === 201) {
                // delete
                try {
                  const deleteData = {
                    subjectId: subjectId,
                    choiceAnswerId: removedItem,
                  };
                  const { data, status } = await API.deleteSurveyData(deleteData);
                  if (status === 204) {
                    this.$emit("nextPhaseHandler", this.labelsIndex);
                  }
                } catch (e) {
                  console.log(e);
                }
              }
            } catch (error) {
              if (error.response.status === 429) {
                this.$store.commit("setoverReqModal", true);
              }
              console.log(error);
            }
          }
          // console.log("nothing changed");
          this.$emit("nextPhaseHandler", this.labelsIndex);
        } else {
          // console.log("first post");
          try {
            const { data, status } = await API.postSurveyData(survey);
            // console.log(data, status);
            if (status === 201) {
              this.$emit("nextPhaseHandler", this.labelsIndex);
            }
          } catch (error) {
            if (error.response.status === 429) {
              this.$store.commit("setoverReqModal", true);
            }
            console.log(error);
          }
        }
      }

      // 흡연량
      if (this.labelsIndex === 5) {
        surveyData[0].choiceAnswers = [surveyData[0].choiceAnswers + 23];
        const survey = {
          subjectId: subjectId,
          answers: surveyData,
        };
        // console.log(survey);

        const choicesAnswer = this.answers[this.labelsIndex].choice + 23;
        const checkedArr = this.questionsArr[this.labelsIndex - 1]?.choice[0];
        if (checkedArr !== undefined) {
          const removedItem =
            checkedArr.id !== choicesAnswer ? [checkedArr.choiceAnswer[0].id] : null;
          const addedItem = checkedArr.id !== choicesAnswer ? [choicesAnswer] : [];
          // console.log(removedItem, addedItem);
          if (addedItem.length !== 0) {
            // 단수선택 항목은 새로운 선택이 있으면 반드시 삭제도 존재
            surveyData[0].choiceAnswers = addedItem;
            try {
              const survey = {
                subjectId: subjectId,
                answers: surveyData,
              };
              const { data, status } = await API.postSurveyData(survey);
              // console.log(data, status);
              if (status === 201) {
                // delete
                try {
                  const deleteData = {
                    subjectId: subjectId,
                    choiceAnswerId: removedItem,
                  };
                  const { data, status } = await API.deleteSurveyData(deleteData);
                  if (status === 204) {
                    this.$emit("nextPhaseHandler", this.labelsIndex);
                  }
                } catch (e) {
                  console.log(e);
                }
              }
            } catch (error) {
              if (error.response.status === 429) {
                this.$store.commit("setoverReqModal", true);
              }
              console.log(error);
            }
          }
          // console.log("nothing changed");
          this.$emit("nextPhaseHandler", this.labelsIndex);
        } else {
          // console.log("first post");
          try {
            const { data, status } = await API.postSurveyData(survey);
            // console.log(data, status);
            if (status === 201) {
              this.$emit("nextPhaseHandler", this.labelsIndex);
            }
          } catch (error) {
            if (error.response.status === 429) {
              this.$store.commit("setoverReqModal", true);
            }
            console.log(error);
          }
        }
      }
      // this.healthInfoValid = false;
      localStorage.setItem("surveyIdx", this.labelsIndex);
    },
  },

  mounted() {
    // console.log("mounted");
    this.subjectId = localStorage.getItem("subjectId") || 0;

    this.getSurveyData();
    this.noDisease = this.answers[3].choice.includes(1) ? true : false;
  },
};
</script>

<style lang="scss" scoped>
.form__wrapper {
  width: 100%;
  padding: 0 30px 130px;
  height: 65vh;
  overflow-y: scroll;
}

.no-disease {
  width: 100%;
  // position: absolute;
  z-index: 9;
}

.disabled-checkbox {
  opacity: 0.5;
}
.v-input--selection-controls {
  margin: 0px !important;
  padding: 0px !important;
}

::v-deep .v-input__control {
  display: block !important;
  flex-wrap: nowrap !important;
}

::v-deep .v-messages {
  display: none !important;
}
.checkbox-label {
  font-weight: 400;
  font-size: 20px !important;
  color: black;
  padding-bottom: 5px;
  letter-spacing: -0.03em;
  line-height: 26px;
}

.diabetes-description {
  color: #646464 !important;
  font-size: 18px !important;
}

.option-comment {
  padding: 0 30px 20px 30px;
  text-align: left;
  letter-spacing: -0.03em;
  color: #646464;
}

.fixed-btn {
  width: 100%;
  position: fixed;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 20.83%, #ffffff 100%);
  bottom: 0;
  height: calc(5vh + 90px);
  max-width: 450px;
  left: 50%;
  transform: translateX(-50%);
  padding: 30px 30px 0px 30px;
}
::v-deep textarea {
  line-height: 18px !important;
  font-size: 12px !important;
}

::v-deep .v-input__slot {
  align-items: flex-start;
}

::v-deep .v-icon.v-icon {
  padding-top: 5px;
}
</style>
