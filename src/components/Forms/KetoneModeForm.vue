<template>
  <div class="input__wrapper">
    <div class="input__title">{{ $t("ketone_mode") }}</div>
    <div class="input__subtitle">
      {{ $t("ketone_mode_desc") }}
    </div>

    <div class="checkbox__items">
      <div class="checkbox__item">
        <v-checkbox color="#41d8e6" off-icon="$check_box" on-icon="$check_box_inactive" v-model="notKetoneMode">
          <!-- @change="notKetoneModeHandler" -->
          <template v-slot:label>
            <div class="text">{{ $t("no_ketosis") }}</div>
          </template>
        </v-checkbox>
        <div class="description">{{ $t("is_diabetes") }}</div>
      </div>
      <div class="checkbox__item">
        <v-checkbox color="#41d8e6" off-icon="$check_box" on-icon="$check_box_inactive" v-model="isKetoneMode">
          <!-- @change="ketoneModeHandler" -->
          <template v-slot:label>
            <div class="text">{{ $t("is_ketosis") }}</div>
          </template>
        </v-checkbox>
        <div class="description">{{ $t("no_diabetes") }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    initialKetoneMode: Boolean,
  },
  data() {
    return {
      isKetoneMode: false,
      notKetoneMode: false,
    };
  },
  watch: {
    isKetoneMode(newVal) {
      // console.log(newVal);
      newVal ? (this.notKetoneMode = false) : null;
      this.$emit("ketoneModeHandler", this.isKetoneMode);
    },
    notKetoneMode(newVal) {
      // console.log(newVal);
      newVal ? (this.isKetoneMode = false) : null;
      this.$emit("ketoneModeHandler", this.isKetoneMode);
    },
  },
  methods: {
    ketoneModeHandler(initialKetoneMode) {
      initialKetoneMode
        ? ((this.isKetoneMode = true), (this.notKetoneMode = false))
        : ((this.isKetoneMode = false), (this.notKetoneMode = true));
    },
  },
  mounted() {
    // console.log(this.initialKetoneMode);
    if (this.initialKetoneMode !== null) {
      this.ketoneModeHandler(this.initialKetoneMode);
    }
  },
};
</script>

<style lang="scss" scoped>
.input__wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  // height: 100px;
}
.checkbox__items {
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.checkbox__item {
  // width: 50%;
}
.input__title {
  width: 100%;
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  color: #646464;
}

.input__subtitle {
  font-size: 16px;
  text-align: left;
  color: #a7a7a7;
  letter-spacing: -0.03em;
  padding: 10px 0;
}

.text {
  color: #646464;
  font-size: 18px;
  padding-left: 5px;
}

.description {
  color: #a7a7a7;
  font-size: 16px;
  text-align: left;
  padding-left: 29px;
}

::v-deep .v-input--selection-controls {
  margin-top: 10px;
}

::v-deep .v-input--selection-controls__input {
  padding-top: 5px;
}

::v-deep .v-input--selection-controls__input {
  margin: 0 !important;
}

::v-deep .theme--light.v-messages {
  display: none;
}

::v-deep .v-text-field .v-label {
  top: -30px !important;
  font-weight: 500;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 12px !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
</style>
