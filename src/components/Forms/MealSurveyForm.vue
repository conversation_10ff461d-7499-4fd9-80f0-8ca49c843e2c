<template>
  <div>
    <CompleteAlert
      class="modal"
      :content="content"
      v-show="showCompleteAlert"
      :btnText="this.$i18n.t('confirm_btn')"
      @isConfirmed="isConfirmed"
    />
    <ErrorModal v-show="showErrModal" :error="error" @isClicked="isClicked" />
    <div class="mealInfo__wrapper">
      <!-- <div class="option-comment">
        <span>{{ $t("duplicate_check") }}</span>
      </div> -->
      <div class="dp-30">
        <div class="meal-categories__wrapper">
          <div class="meal-options" id="first-option">
            <v-checkbox
              v-model="isGeneral"
              off-icon="$check_box"
              on-icon="$check_box_inactive"
              color="#41D8E6"
            >
              <template v-slot:label>
                <div class="checkbox-label">
                  {{ $t("general_diet") }}
                </div>
              </template>
            </v-checkbox>
          </div>
        </div>

        <div class="meal-categories__wrapper">
          <div class="meal-category-title" :class="{ 'disabled-checkbox': isGeneral }">
            {{ $t(calorieSurvey.text) }}
            <span class="question-description"> {{ $t("multi_desc") }}</span>
          </div>
          <div class="meal-options">
            <!-- v-model="answers[idx].choice"
              @click="checkBoxHandler(answers[idx].choice)" -->
            <v-checkbox
              v-for="(value, idx) in calorieSurvey.choice"
              :key="value.id"
              off-icon="$check_box"
              on-icon="$check_box_inactive"
              color="#41D8E6"
              v-model="calorieAnswer"
              v-show="value.status === 'active'"
              :value="idx + 1"
              :multiple="calorieSurvey.isMultipleChoice"
              :disabled="isGeneral"
              :class="{ 'disabled-checkbox': isGeneral }"
              ><template v-slot:label>
                <div class="checkbox-label">
                  {{ $t(value.text) }}
                </div>
              </template></v-checkbox
            >
          </div>
        </div>
        <div class="meal-categories__wrapper">
          <div class="meal-category-title" :class="{ 'disabled-checkbox': isGeneral }">
            {{ $t(veganSurvey.text) }}
            <span class="question-description"> {{ $t("single_desc") }}</span>
          </div>
          <div class="meal-options">
            <!-- v-model="answers[idx].choice"
                @click="checkBoxHandler(answers[idx].choice)" -->
            <v-checkbox
              v-for="(value, idx) in veganSurvey.choice"
              :key="value.id"
              off-icon="$check_box"
              on-icon="$check_box_inactive"
              color="#41D8E6"
              v-model="veganAnswer"
              v-show="value.status === 'active'"
              :value="idx + 1"
              :multiple="veganSurvey.isMultipleChoice"
              :disabled="isGeneral"
              :class="{ 'disabled-checkbox': isGeneral }"
              ><template v-slot:label>
                <div class="checkbox-label">
                  {{ $t(value.text) }}
                </div>
              </template></v-checkbox
            >
          </div>
        </div>
        <div class="meal-categories__wrapper">
          <div class="meal-category-title" :class="{ 'disabled-checkbox': isGeneral }">
            {{ $t(careSurvey.text) }}
            <span class="question-description"> {{ $t("multi_desc") }}</span>
          </div>
          <div class="meal-options">
            <!-- v-model="answers[idx].choice"
                  @click="checkBoxHandler(answers[idx].choice)" -->
            <v-checkbox
              v-for="(value, idx) in careSurvey.choice"
              :key="value.id"
              off-icon="$check_box"
              on-icon="$check_box_inactive"
              color="#41D8E6"
              v-model="careAnswer"
              v-show="value.status === 'active'"
              :value="idx + 1"
              :multiple="true"
              :disabled="isGeneral"
              :class="{ 'disabled-checkbox': isGeneral }"
              ><template v-slot:label>
                <div class="checkbox-label">
                  {{ $t(value.text) }}
                </div>
              </template></v-checkbox
            >
          </div>
        </div>

        <!-- <div class="meal-categories__wrapper" v-for="(item, idx) in labels" :key="idx">
          <div class="meal-category-title">{{ item.title }}</div>
          <div class="meal-options">
            <v-checkbox
              v-model="mealInfo"
              v-for="(item, idx) in item.options"
              :key="idx"
              :value="item.en"
              off-icon="$check_box"
              on-icon="$check_box_inactive"
              color="#41D8E6"
              multiple
            >
              <template v-slot:label>
                <div class="checkbox-label">
                  {{ item.ko }}
                </div>
              </template>
            </v-checkbox>
          </div>
        </div> -->
        <div
          class="meal-category-title"
          @click="showTextAreaHandler"
          :class="{ 'disabled-checkbox': isGeneral }"
        >
          {{ $t("diet_input_etc") }}
          <span class="opinion-comment-text">{{ $t("diet_input_etc_desc") }}</span>
        </div>
        <template v-if="showTextArea">
          <v-textarea
            outlined
            color="#41D8E6"
            :placeholder="this.$i18n.t('diet_input_etc_placeholder')"
            minlength="5"
            maxlength="50"
            v-model="opinion"
            :disabled="isGeneral"
            :class="{ 'disabled-checkbox': isGeneral }"
          ></v-textarea>
        </template>
        <div class="btn__wrapper">
          <v-btn
            color="#41D8E6"
            class="main-large-btn"
            @click="joinBtnHandler"
            elevation="0"
            :disabled="!infoValid"
          >
            {{ $t("start_btn") }}
          </v-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import API from "@/api/survey/index.js";
import CompleteAlert from "@/components/Common/ConfirmModal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";

export default {
  props: {
    questions: Array,
    checkedData: Object,
  },
  components: {
    CompleteAlert,
    ErrorModal,
  },
  data() {
    return {
      showCompleteAlert: false,
      showErrModal: false,
      isGeneral: false,
      survey: {},
      calorieSurvey: {},
      veganSurvey: {},
      careSurvey: {},
      content: this.$i18n.t("survey_modal_message"),
      error: this.$i18n.t("join_error_message"),
      // infoValid: false,
      opinion: "",
      calorieAnswer: [],
      veganAnswer: null,
      careAnswer: [],
      showTextArea: false,
      subjectId: 0,
    };
  },
  computed: {
    infoValid() {
      if (
        this.calorieAnswer.length !== 0 ||
        this.veganAnswer !== null ||
        this.careAnswer.length !== 0
      ) {
        return this.showTextArea ? this.opinion.length >= 5 : true;
      } else return this.isGeneral;
    },
  },
  methods: {
    getSurveyData() {
      // console.log(this.questions.slice(5));
      this.survey = this.questions.slice(5, 8);
      this.calorieSurvey = this.questions[5];
      this.veganSurvey = this.questions[6];
      this.careSurvey = this.questions[7];
      // console.log("calorieSurvey:", this.calorieSurvey);
      // this.survey = this.questions[this.currentStateIdx];
      // this.isMultiple = this.questions[this.currentStateIdx].isMultipleChoice;
      // console.log("survey:", this.survey, this.isMultiple);
      // this.$nextTick(() => this.getSurveyCheckedData());
    },
    // getSurveyCheckedData() {},
    showTextAreaHandler() {
      if (!this.isGeneral) this.showTextArea = !this.showTextArea;
    },
    checkBoxHandler(checkedArr) {
      // console.log(checkedArr);
    },
    async joinBtnHandler() {
      //  ((this.calorieAnswer = [4]), (this.veganAnswer = 4), (this.careAnswer = [5]), (this.opinion = ""))
      const makedCalorieAnswers =
        this.calorieAnswer.length !== 0 && this.calorieAnswer.map((i) => i + 28);
      const calorieAnswer =
        this.calorieAnswer.length === 0 // 안함
          ? { questionId: 6, choiceAnswers: [32] }
          : { questionId: 6, choiceAnswers: makedCalorieAnswers };
      // console.log(calorieAnswer);
      const veganAnswer =
        this.veganAnswer === null // 안함
          ? { questionId: 7, choiceAnswers: [36] }
          : { questionId: 7, choiceAnswers: [this.veganAnswer + 32] };
      // console.log(veganAnswer);
      const makedCareAnswers = this.careAnswer.length !== 0 && this.careAnswer.map((i) => i + 36);
      const careAnswer =
        this.careAnswer.length === 0 // 안함
          ? { questionId: 8, choiceAnswers: [41] }
          : { questionId: 8, choiceAnswers: makedCareAnswers };
      // console.log(careAnswer);
      const surveyData = {
        subjectId: Number(this.subjectId),
        answers: [],
      };
      // post 각 문항에 더한 값 questionId: 6, +28, 7 +32, 8 +36
      calorieAnswer !== null ? surveyData.answers.push(calorieAnswer) : null;
      veganAnswer !== null ? surveyData.answers.push(veganAnswer) : null;
      careAnswer !== null ? surveyData.answers.push(careAnswer) : null;

      // veganAnswer, careAnswer;
      this.opinion.length !== 0
        ? surveyData.answers.push({ questionId: 9, textAnswer: this.opinion })
        : null;

      // console.log("final survey:", surveyData);
      try {
        const { data, status } = await API.postSurveyData(surveyData);
        // console.log(data, status);
        if (status === 201) {
          try {
            const { data, status } = await API.patchSurveyComplete();
            if (status === 200) {
              this.showCompleteAlert = true;
              this.$store.commit("setSurveyStatus", "active");
              localStorage.removeItem("surveyIdx");
            }
          } catch (error) {
            if (error.response.status === 429) {
              this.$store.commit("setoverReqModal", true);
            }
            console.log(error);
          }
        }
      } catch (e) {
        console.log(e);
      }
    },
    isConfirmed() {
      this.showCompleteAlert = false;
      this.$router.push("/home");
    },
    isClicked() {
      this.showErrModal = false;
    },
  },
  watch: {
    questions() {
      this.getSurveyCheckedData();
    },
    isGeneral(newVal) {
      // console.log(newVal);
      newVal
        ? ((this.calorieAnswer = [4]),
          (this.veganAnswer = 4),
          (this.careAnswer = [5]),
          (this.opinion = ""))
        : ((this.calorieAnswer = []), (this.veganAnswer = null), (this.careAnswer = []));
    },
    // 버튼 활성화 조건
  },
  mounted() {
    // console.log(this.questions);
    // console.log(this.checkedData);
    this.subjectId = localStorage.getItem("subjectId") || 0;
    this.getSurveyData();
    // TODO: 설문 앞으로 옮기기
    // console.log("if sns login params:");
    // this.snsId = String(this.$route.params.id);
    // this.email = this.$route.params.email;
    // this.snsType = this.$route.params.snsType;
    // this.profileImg = this.$route.params.profileImg;
  },
};
</script>

<style scoped>
.mealInfo__wrapper {
  width: 100%;
  height: 70vh;
  overflow-y: scroll;
  padding-bottom: 20vh;
}

.v-input--selection-controls {
  margin: 0px !important;
  padding: 0px !important;
}
::v-deep .v-input__control {
  display: block !important;
  flex-wrap: nowrap !important;
}

::v-deep .v-messages {
  display: none !important;
}
.checkbox-label {
  font-style: normal;
  font-weight: 400;
  font-size: 18px !important;
  padding-bottom: 10px;
  color: black;
}

.meal-category-title {
  text-align: left;
  font-weight: 500;
  font-size: 20px;
  line-height: 26px;
  /* identical to box height */
  letter-spacing: -0.03em;
  color: #000000;
  padding: 10px 0px;
}

.question-description {
  font-size: 18px;
  color: #646464;
  font-weight: 400;
}

.meal-options {
  padding-left: 19px;
}

#first-option {
  padding-top: 10px;
}

.option-comment {
  padding: 30px 30px 0 30px;
  text-align: left;
  letter-spacing: -0.03em;
  color: #646464;
}

.opinion-comment-text {
  color: #a7a7a7;
}
.btn__wrapper {
  width: 100%;
  position: fixed;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 20.83%, #ffffff 100%);
  bottom: 0;
  height: calc(5vh + 90px);
  max-width: 450px;
  left: 50%;
  transform: translateX(-50%);
  padding: 30px 30px 0px 30px;
}

.disabled-checkbox {
  opacity: 0.5;
}

::v-deep textarea {
  line-height: 18px !important;
  font-size: 12px !important;
}
</style>
