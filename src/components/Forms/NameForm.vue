<template>
  <div class="userdetail-text-field__items">
    <v-text-field
      v-model="name"
      color="#41d8e6"
      :placeholder="this.$i18n.t('name_placeholder')"
      :error-messages="nameError"
      :persistent-placeholder="true"
      maxlength="20"
    >
      <template v-slot:label>
        <div class="text-field-label">{{ $t("profile_nickname") }}</div>
      </template>
    </v-text-field>
  </div>
</template>

<script>
export default {
  data() {
    return {
      name: "",
      nameError: "",
      valid: false,
    };
  },
  methods: {
    nameValidation(name) {
      return /^[ㄱ-ㅎ|가-힣|a-z|A-Z|0-9|]{2,20}$/.test(name);
    },
    firstValidation() {
      if (this.name.length > 1 || this.nameValidation(this.name)) {
        this.valid = true;
      } else {
        this.valid = false;
      }
      const name = { name: this.name, valid: this.valid };
      this.$emit("nameInputHandler", name);
    },
  },
  watch: {
    name(newVal) {
      if (this.name.length > 1 && this.nameValidation(newVal)) {
        this.nameError = "";
        this.valid = true;
      } else {
        this.nameError = this.$i18n.t("name_placeholder");
        this.valid = false;
      }
      const name = { name: this.name, valid: this.valid };
      this.$emit("nameInputHandler", name);
    },
  },
  mounted() {
    if (this.$route.params.name !== undefined) {
      this.name =
        this.$route.params.name.length > 20
          ? ""
          : this.$route.params.name || this.$store.state.join.userDetail.name || "";
    } else this.name = this.$route.params.name || this.$store.state.join.userDetail.name || "";

    // console.log(this.$route.params.name, this.$store.state.join.userDetail.name, this.name);
    // this.firstValidation();
  },
};
</script>

<style lang="scss" scoped>
.userdetail-text-field__items {
  display: flex;
  width: 100%;
  height: 80px;
}

.text-field-label {
  font-size: 18px;
  font-weight: 500;
}

::v-deep .theme--light.v-input input,
.theme--light.v-input textarea {
  line-height: 20px !important;
  font-size: 20px !important;
  color: #323232;
}
::v-deep .v-text-field input {
  padding: 0;
}
::v-deep v-text-field input {
  font-size: 20px;
}

::v-deep .v-input {
  color: #323232;
  font-size: 20px;
}

::v-deep .v-text-field .v-label {
  top: -30px !important;
  font-weight: 500;
  color: #646464;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 16px !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
</style>
