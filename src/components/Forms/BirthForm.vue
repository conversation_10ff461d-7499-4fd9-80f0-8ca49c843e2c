<template>
  <div class="birth-form__container">
    <div class="age-input__title">{{ $t("profile_birth") }}</div>
    <div class="age-input__subtitle">
      {{ $t("profile_birth_description") }}
    </div>
    <div class="userdetail-text-field__items">
      <div class="name-input__item">
        <v-text-field
          v-model="year"
          type="number"
          inputmode="numeric"
          pattern="[0-9]*"
          color="#41d8e6"
          :placeholder="this.$i18n.t('birth_year_placeholder')"
          :error-messages="yearError"
          @focus="focusInHandler"
          @blur="focusOutHandler"
        >
        </v-text-field>
      </div>
      <div class="name-input__item age">
        <v-text-field
          type="number"
          inputmode="numeric"
          pattern="[0-9]*"
          v-model="month"
          color="#41d8e6"
          :placeholder="this.$i18n.t('birth_month_placeholder')"
          :error-messages="monthError"
          @focus="focusInHandler"
          @blur="focusOutHandler"
        >
        </v-text-field>
      </div>
      <div class="name-input__item">
        <v-text-field
          type="number"
          inputmode="numeric"
          pattern="[0-9]*"
          v-model="day"
          color="#41d8e6"
          :placeholder="this.$i18n.t('birth_day_placeholder')"
          :error-messages="dayError"
          @focus="focusInHandler"
          @blur="focusOutHandler"
        >
        </v-text-field>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    initialBirth: String,
  },
  data() {
    return {
      year: null,
      month: null,
      day: null,
      yearError: "",
      monthError: "",
      dayError: "",
      validBirth: false,
    };
  },
  methods: {
    focusInHandler() {
      const ageInputTitle = document.querySelector(".age-input__title");
      ageInputTitle.style.color = "#41D8E6";
    },
    focusOutHandler() {
      const ageInputTitle = document.querySelector(".age-input__title");
      ageInputTitle.style.color = "rgba(0, 0, 0, 0.6)";
    },
    yearValidation(year) {
      const today = new Date();
      const yearNow = today.getFullYear();
      if (1900 > year || year > yearNow || this.year === null) return false;
      else return true;
    },
    monthValidation(month) {
      if (month < 1 || month > 12 || month.length === 1 || this.month === null) return false;
      else return true;
    },
    dayValidation(day) {
      if (day < 1 || day > 31 || day.length === 1 || this.day === null) return false;
      else if (
        (Number(this.month) === 4 ||
          Number(this.month) === 6 ||
          Number(this.month) === 9 ||
          Number(this.month) === 11) &&
        Number(day) === 31
      )
        return false;
      else if (Number(this.month) === 2) {
        // 2월 29일(윤년) 체크
        const isleap = Number(this.year) % 4 === 0 && (Number(this.year) % 100 !== 0 || Number(this.year) % 400 === 0);
        // console.log(isleap);
        if (Number(day) > 29 || (Number(day) === 29 && !isleap)) {
          return false;
        } else {
          return true;
        }
      } else return true;
    },
    maxLengthCheck(object) {
      if (object.value.length > object.maxLength) {
        object.value = object.value.slice(0, object.maxLength);
      }
    },
  },
  watch: {
    year(newVal) {
      if (this.yearValidation(newVal)) {
        this.yearError = "";
      } else {
        this.yearError = this.$i18n.t("invalid");
      }
      this.yearValidation(this.year) === true &&
      this.monthValidation(this.month) === true &&
      this.dayValidation(this.day) === true
        ? (this.validBirth = true)
        : (this.validBirth = false);
      const birth = { birth: `${this.year}-${this.month}-${this.day}`, valid: this.validBirth };
      this.$emit("birthInputHandler", birth);
    },
    month(newVal) {
      if (this.monthValidation(newVal)) this.monthError = "";
      else this.monthError = this.$i18n.t("invalid");
      this.yearValidation(this.year) === true &&
      this.monthValidation(this.month) === true &&
      this.dayValidation(this.day) === true
        ? (this.validBirth = true)
        : (this.validBirth = false);
      const birth = { birth: `${this.year}-${this.month}-${this.day}`, valid: this.validBirth };
      this.$emit("birthInputHandler", birth);
    },
    day(newVal) {
      if (this.dayValidation(newVal)) this.dayError = "";
      else this.dayError = this.$i18n.t("invalid");
      this.yearValidation(this.year) === true &&
      this.monthValidation(this.month) === true &&
      this.dayValidation(this.day) === true
        ? (this.validBirth = true)
        : (this.validBirth = false);
      const birth = { birth: `${this.year}-${this.month}-${this.day}`, valid: this.validBirth };
      this.$emit("birthInputHandler", birth);
    },
    initialBirth(newVal) {
      if (newVal !== undefined) {
        const [y, m, d] = newVal.split("-");
        this.year = y || 0;
        this.month = m || 0;
        this.day = d || 0;
      }
    },
  },
  mounted() {
    if (this.initialBirth !== undefined) {
      const [y, m, d] = this.initialBirth.split("-");
      // console.log(this.initialBirth);
      this.year = y || 0;
      this.month = m || 0;
      this.day = d || 0;
    }
  },
};
</script>

<style lang="scss" scoped>
.birth-form__container {
  width: 100%;
  height: 135px;
}

.userdetail-text-field__items {
  display: flex;
}
.age-input__subtitle {
  font-size: 14px;
  text-align: left;
  color: #a7a7a7;
  letter-spacing: -0.03em;
  padding-bottom: 10px;
}

.age-input__items {
  display: flex;
  padding-top: 15px;
}

.age {
  padding: 0 12px;
}
.text-field-label {
  padding-bottom: 14px;
}

.age-input__title {
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
  transition: 0.1s ease-in-out;
}

::v-deep .v-text-field input {
  padding: 0;
  font-size: 20px;
}
::v-deep .v-input {
  font-family: GilroyMedium !important;
  margin-bottom: 20px;
  font-size: 20px;
}
::v-deep .v-text-field {
  padding-top: 5px;
}
::v-deep .v-input input {
  max-height: 25px;
}
::v-deep v-text-field input {
  font-size: 18px;
}
::v-deep .v-text-field .v-label {
  top: -20px !important;
  font-weight: 500;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 14px !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
</style>
