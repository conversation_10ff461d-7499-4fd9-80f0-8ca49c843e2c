<template>
  <div class="basic-data__wrapper">
    <div class="img__wrapper"><img src="@/assets/images/cym702_logo/cym702_slogan.png" /></div>
    <div class="form-subtitle__wrapper" v-html="this.$i18n.t('profile_subtitle')"></div>
    <section>
      <GenderForm @genderInputHandler="genderInputHandler" />
    </section>

    <section class="gender-input-section">
      <NameForm @nameInputHandler="nameInputHandler" />
    </section>

    <section>
      <BirthForm @birthInputHandler="birthInputHandler" />
    </section>

    <section>
      <HeightForm @heightInputHandler="heightInputHandler" />
    </section>

    <section>
      <WeightForm @weightInputHandler="weightInputHandler" />
    </section>

    <section class="last">
      <WeightGoalForm @weightGoalInputHandler="weightGoalInputHandler" />
    </section>

    <div class="btn__wrapper">
      <v-btn color="#41D8E6" class="main-large-btn" @click="nextPhaseHandler" :disabled="!inputValid" elevation="0">
        {{ $t("next_btn") }}
      </v-btn>
    </div>
  </div>
</template>

<script>
import GenderForm from "./GenderForm.vue";
import NameForm from "./NameForm.vue";
import BirthForm from "./BirthForm.vue";
import HeightForm from "./HeightForm.vue";
import WeightForm from "./WeightForm.vue";
import WeightGoalForm from "./WeightGoalForm.vue";

export default {
  components: {
    GenderForm,
    NameForm,
    BirthForm,
    HeightForm,
    WeightForm,
    WeightGoalForm,
  },
  data() {
    return {
      name: "",
      nameValid: false,
      gender: null,
      genderValid: false,
      birth: null,
      birthValid: false,
      height: null,
      heightValid: false,
      initialWeight: null,
      initialWeightValid: false,
      targetWeight: null,
      targetWeightValid: false,
      targetWater: 2000,
      inputValid: false,
    };
  },

  methods: {
    nextPhaseHandler() {
      const userInfo = {
        nickname: this.name,
        sex: this.gender,
        birth: this.birth,
        height: Number(this.height),
        initialWeight: Number(this.initialWeight),
        // targetWeight: Number(this.targetWeight),
        // targetWater: this.targetWater,
      };
      this.targetWeight !== null && this.targetWeight !== 0
        ? (userInfo.targetWeight = Number(this.targetWeight))
        : null;
      // if (this.infoValid()) {
      this.$store.commit("GET_USER_DETAIL", userInfo);
      this.$emit("nextPhaseHandler", 4);
      // }
      // console.log(userInfo);
    },
    nameInputHandler(inputName) {
      // console.log(inputName);
      this.name = inputName.name;
      this.nameValid = inputName.valid;
      this.infoValid();
    },
    genderInputHandler(gender) {
      // console.log(gender);
      this.gender = gender;
      this.infoValid();
    },
    birthInputHandler(inputBirth) {
      // console.log(inputBirth);
      this.birth = inputBirth.birth;
      this.birthValid = inputBirth.valid;
      this.infoValid();
    },
    heightInputHandler(inputHeight) {
      // console.log(inputHeight);
      this.height = inputHeight.height;
      this.heightValid = inputHeight.valid;
      this.infoValid();
    },
    weightInputHandler(inputWeight) {
      // console.log(inputWeight);
      this.initialWeight = inputWeight.weight;
      this.initialWeightValid = inputWeight.valid;
      this.infoValid();
    },
    weightGoalInputHandler(inputGoalWeight) {
      // console.log(inputGoalWeight);
      // console.log(inputGoalWeight.goal);
      this.targetWeight = inputGoalWeight.goal;
      this.targetWeightValid = inputGoalWeight.valid;
      this.infoValid();
    },
    // 다음버튼 활성화 유효성 체크
    infoValid() {
      this.gender !== null && this.nameValid && this.birthValid && this.heightValid && this.initialWeightValid
        ? (this.inputValid = true)
        : (this.inputValid = false);
    },
  },
  mounted() {
    // console.log("name valid check this");
    // console.log(this.name);
    // console.log(this.nameValid);
  },
};
</script>

<style lang="scss" scoped>
.basic-data__wrapper {
  width: 100%;
  // padding: 40px 30px 50px;
  padding: 20px 30px 50px;
  // height: 60vh;
  height: calc(100vh - 100px);
  overflow-y: scroll;
}

.img__wrapper {
  img {
    padding-top: 20px;
    width: 100%;
  }
}

.form-subtitle__wrapper {
  width: 100%;
  padding: 25px 0;
  font-weight: 400;
  font-size: 18px;
  text-align: left;
  color: #646464;
  line-height: 23px;
}

section {
  margin: 20px 0 30px;
  // margin-bottom: 25px;
  width: 100%;
}
.gender-input-section {
  // margin-bottom: 30px;
  // margin-top: 25px;
}
.last {
  // margin-bottom: 80px;
}

.gender-checkbox__items {
  display: flex;
  width: 100%;
}

.gender-checkbox__item {
  width: 50%;
}
.gender-input__title {
  width: 100%;
  text-align: left;
  font-size: 16px;
}

.age-input__subtitle {
  font-size: 12px;
  text-align: left;
  color: #a7a7a7;
}

.age-input__items {
  display: flex;
  padding-top: 15px;
}

.age {
  padding: 0 12px;
}
.text-field-label {
  padding-bottom: 14px;
}

.age-input__title {
  text-align: left;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
  transition: 0.1s ease-in-out;
}

.btn__wrapper {
  width: 100%;
  position: fixed;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 20.83%, #ffffff 100%);
  bottom: 0;
  height: calc(5vh + 90px);
  left: 0;
  padding: 30px 30px 0px 30px;
}

.main-large-btn {
  width: 100%;
  max-width: 390px !important;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  color: #c9f4f8 !important;
  opacity: 1;
}
</style>
