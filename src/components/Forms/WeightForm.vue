<template>
  <div class="weight-form">
    <v-text-field
      color="#41d8e6"
      v-model="weight"
      :placeholder="this.$i18n.t('profile_description')"
      :persistent-placeholder="true"
      type="number"
      inputmode="numeric"
      pattern="[0-9]*"
      :error-messages="weightError"
    >
      <template v-slot:label>
        <div class="text-field-label">{{ $t("profile_weight") }}</div>
      </template>
    </v-text-field>
  </div>
</template>

<script>
export default {
  data() {
    return {
      weight: null,
      validWeight: false,
      weightError: "",
    };
  },
  methods: {
    weightValidation(weight) {
      if (weight > 0 && weight < 501) return true;
      return false;
    },
  },
  watch: {
    weight(newVal) {
      if (this.weight === null || this.weightValidation(newVal)) this.weightError = "";
      else this.weightError = this.$i18n.t("invalid");

      this.weightValidation(this.weight) === true ? (this.validWeight = true) : (this.validWeight = false);
      const weight = { weight: this.weight, valid: this.validWeight };
      this.$emit("weightInputHandler", weight);
    },
  },
  mounted() {
    this.weight = this.$store.state.join.userDetail.signup_weight || null;
  },
};
</script>

<style lang="scss" scoped>
.weight-form {
  width: 100%;
  height: 90px;
}

::v-deep .v-text-field input {
  padding: 0;
  font-size: 20px;
}
::v-deep v-text-field input {
  font-size: 18px;
}
::v-deep .v-input {
  font-family: GilroyMedium !important;
}
.text-field-label {
  font-size: 18px;
  font-weight: 500;
}

::v-deep .v-input {
  margin-bottom: 20px;
  font-size: 20px;
  letter-spacing: -0.03em;
}

::v-deep .v-text-field .v-label {
  top: -30px !important;
  font-weight: 500;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 14px !important;
    letter-spacing: -0.03em;
    padding: 0;
    font-weight: 400 !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
</style>
