<template>
  <div class="input__wrapper">
    <div class="input__title">{{ $t("relationships") }}</div>
    <div>
      <v-checkbox
        v-for="(type, idx) in filteredUserTypesArr"
        :key="idx"
        :value="type.id"
        color="#41d8e6"
        off-icon="$check_box"
        on-icon="$check_box_inactive"
        v-model="userType"
      >
        <!-- @click="userTypeHandler" -->
        <template v-slot:label>
          <div class="input-label">{{ type.text }}</div>
        </template>
      </v-checkbox>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    initialType: Number,
  },
  computed: {
    filteredUserTypesArr() {
      if (this.type === "kardio") {
        return this.userTypesArr.filter((item) => item.type === "kardio");
      } else {
        return this.userTypesArr.filter((item) => item.type === "human");
      }
    },
  },
  data() {
    return {
      userType: "",
      type: "",
      userTypesArr: [
        { id: 10, text: this.$i18n.t("patient"), type: "kardio" },
        { id: 11, text: this.$i18n.t("caregiver"), type: "kardio" },
        { id: 12, text: this.$i18n.t("other"), type: "kardio" },
        { id: 2, text: this.$i18n.t("parents"), type: "human" },
        { id: 3, text: this.$i18n.t("spouse"), type: "human" },
        { id: 4, text: this.$i18n.t("child"), type: "human" },
        { id: 5, text: this.$i18n.t("grandparents"), type: "human" },
        { id: 6, text: this.$i18n.t("brothers"), type: "human" },
        { id: 7, text: this.$i18n.t("relative"), type: "human" },
        { id: 8, text: this.$i18n.t("friend"), type: "human" },
        { id: 9, text: this.$i18n.t("acquaintance"), type: "human" },
      ],
    };
  },
  watch: {
    userType(newVal) {
      // console.log(newVal);
      // this.initialType !== this.userType ? this.$emit("userTypeHandler", newVal) : null;
      this.$emit("userTypeHandler", newVal);
    },
  },
  methods: {},
  mounted() {
    this.userType = this.initialType || 0;
    this.type = localStorage.getItem("type") ?? "human";
  },
};
</script>

<style lang="scss" scoped>
.input__wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100px;
}
.checkbox__items {
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.checkbox__item {
  // width: 50%;
}
.input__title {
  width: 100%;
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  color: #646464;
  padding-bottom: 15px;
}

.input__subtitle {
  font-size: 16px;
  text-align: left;
  color: #a7a7a7;
  letter-spacing: -0.03em;
  padding: 10px 0;
}

.text {
  color: #646464;
  font-size: 18px;
}

.description {
  color: #a7a7a7;
  font-size: 16px;
  text-align: left;
  padding-left: 24px;
}

.input-label {
  font-size: 20px;
  padding: 5px;
}

::v-deep .v-input--selection-controls {
  margin: 0;
  padding: 0;
}

::v-deep .v-input__slot {
  margin: 0;
}

::v-deep .v-input--selection-controls__input {
  padding-top: 5px;
}

::v-deep .v-messages {
  min-height: 10px;
}

::v-deep .v-input--selection-controls__input {
  margin: 0;
}
</style>
