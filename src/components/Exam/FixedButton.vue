<template>
  <div>
    <div class="fixed-btn__wrapper dp-30">
      <div
        :class="lang === 'ko' ? 'fixed-btn' : 'fixed-btn_en'"
        :style="{ backgroundColor: color, color: textColor }"
        @click="btn<PERSON><PERSON><PERSON>"
      >
        {{ title }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    color: String,
    title: String,
    path: String,
    textColor: String,
  },
  data() {
    return { lang: this.$i18n.locale === "ko" ? "ko" : "en" };
  },
  methods: {
    btnHandler() {
      this.$router.push({ path: this.path });
      this.$emit("changeCurrentState");
      this.$emit("setLocalStorage");
    },
  },
};
</script>

<style lang="scss" scoped>
.fixed-btn__wrapper {
  width: 100%;
  position: fixed;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 20.83%, #ffffff 100%);
  bottom: 0;
  height: calc(5vh + 90px);
  max-width: 450px;
  left: 50%;
  transform: translateX(-50%);
  padding: 30px 30px 0px 30px;
}

.fixed-btn {
  height: 50px;
  z-index: 999999;
  margin: 0 auto;
  width: 100%;
  max-width: 390px !important;
  border-radius: 5px;
  line-height: 50px;
  font-size: 20px;
  font-weight: bold;
}
.fixed-btn_en {
  height: 50px;
  z-index: 999999;
  width: 100%;
  border-radius: 5px;
  line-height: 50px;
  font-size: 25px;
  font-family: GilroyMedium;
}
</style>
