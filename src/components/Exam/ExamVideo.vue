<template>
  <div>
    <div class="video-section" v-if="isKo">
      <video
        id="exam-video"
        poster="@/assets/images/guide_thumbnail.png"
        src="@/assets/video/guide_video.mp4"
        type="video/mp4"
        controlsList="nodownload"
        controls
        playsinline
        webkit-playsinline="webkit-playsinline"
        width="100%"
      ></video>
    </div>
    <div class="video-section" v-else>
      <video
        id="exam-video"
        poster="@/assets/images/guide_poster_eng.png"
        src="@/assets/video/eng_guide.mp4"
        type="video/mp4"
        controlsList="nodownload"
        controls
        playsinline
        webkit-playsinline="webkit-playsinline"
        width="100%"
      ></video>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isKo: true,
    };
  },

  mounted() {
    this.isKo = this.$i18n.locale === "ko";
  },
};
</script>

<style scoped>
.video-section {
  padding: 15vh 30px 20vh;
  /* height: 100vh; */
  width: 100%;
  display: flex;
  align-items: center;
}

.video-section > poster {
  width: 100%;
  height: 100%;
}
.video-section > img {
  width: 100%;
  height: 100%;
}
</style>
