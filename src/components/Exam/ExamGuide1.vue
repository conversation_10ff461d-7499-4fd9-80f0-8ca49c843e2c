<template>
  <div class="guide1-content-section">
    <div class="guide-contents__wrapper">
      <div class="img__wrapper"><img src="@/assets/images_assets/icons/check-ic.png" /></div>
      <div :class="lang === 'ko' ? 'guide-text' : 'guide-text_en '" v-html="this.$i18n.t('guide_title_first')"></div>
    </div>
    <div class="guide-img__wrapper">
      <img src="@/assets/images/guide_img/strip_stick.png" class="guide_3" alt="stick strip to boat" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return { lang: this.$i18n.locale === "ko" ? "ko" : "en" };
  },
  methods: {
    goToCamera() {
      this.$router.push({ path: "/exam/guide2" });
    },
    goToHome() {
      this.$router.push({ path: "/home" });
    },
  },
};
</script>

<style lang="scss" scoped>
.guide1-content-section {
  width: 100%;
  height: 100vh;
  padding: 100px 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.guide-contents__wrapper {
  width: 100%;
  display: flex;
  align-items: start;
  justify-content: flex-start;
  margin-top: 30px;
  padding-left: 10%;
  letter-spacing: -0.03em;
}
.img__wrapper {
  padding-top: 5px;
  img {
    width: 16px;
  }
}
.guide-text {
  font-size: 24px;
  font-weight: 500;
  margin-left: 10px;
  text-align: left;
}
.guide-text_en {
  font-family: GilroyMedium;
  font-size: 27px;
  margin-left: 10px;
  text-align: left;
}

.guide-img__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80vh;
}
.guide_3 {
  width: 220px;
}
</style>
