<template>
  <!-- <div> -->
  <div class="guide2-content-section">
    <div class="exam-guide-txt">
      <div>
        <div class="guide-txt__row" v-for="(item, i) in guideContent" :key="i">
          <div class="guide-txt__col--numbering">
            <div class="guide-txt__icon">
              <img :src="item.icon" alt="check-icc" />
            </div>
          </div>
          <div :class="lang === 'ko' ? 'guide-txt__col--explaining' : 'guide-txt_en'" v-html="item.text"></div>
        </div>
      </div>
    </div>

    <div class="image-area">
      <img src="@/assets/images/guide_img/pee_guide.gif" class="guide_3" alt="pee guide image" />
    </div>
  </div>
  <!-- </div> -->
</template>

<script>
export default {
  data() {
    return {
      guideContent: [
        {
          icon: require("@/assets/images_assets/icons/check-ic.png"),
          text: this.$i18n.t("guide_title_second"),
        },
        {
          icon: require("@/assets/images_assets/icons/check-ic.png"),
          text: this.$i18n.t("guide_title_third"),
        },
      ],
      lang: this.$i18n.locale === "ko" ? "ko" : "en",
    };
  },
  methods: {
    goToCamera() {
      this.$router.push({ path: "/exam/wait" });
    },
    backToGuide() {
      this.$router.push({ path: "/exam/guide1" });
    },
    goToHome() {
      this.$router.push({ path: "/home" });
    },
  },
};
</script>
<style lang="scss" scoped>
.guide2-content-section {
  width: 100%;
  height: 100vh;
  padding: 100px 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.exam-guide-txt {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: 30px;
  padding-left: 10%;
  letter-spacing: -0.03em;
}

.image-area {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.guide_3 {
  width: 150px;
}

.guide-txt__row {
  font-size: 24px;
  display: flex;
  margin-bottom: 4px;
  width: 100%;
}
.guide-txt__col--numbering {
  color: #41d8e6;
}

.guide-txt__col--explaining {
  font-size: 24px;
  margin-left: 10px;
  text-align: left;
}

.guide-txt_en {
  font-family: GilroyMedium;
  font-size: 27px;
  margin-left: 10px;
  text-align: left;
}

.button-section {
  width: 100%;
  position: fixed;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
  padding: 0 30px;
}
.exam-next-btn {
  width: 100%;
  height: 40px;
  color: #fff;
  background: #41d8e6;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  font-weight: 700;
  font-size: 20px;
}

.back-btn {
  background-color: #ffffff !important;
  padding: 3px 7px !important;
  font-family: Noto Sans KR;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0 !important;
  color: #000000 !important;
  border-radius: 5px;
}

.guide-txt__icon {
  img {
    width: 16px;
  }
}
</style>
