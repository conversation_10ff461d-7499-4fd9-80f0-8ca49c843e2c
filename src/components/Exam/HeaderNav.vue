<template>
  <div>
    <div :class="page === 'wait' ? 'active-background' : 'nav-section'">
      <div class="exam-nav__wrapper">
        <div class="close-btn__box">
          <img
            v-if="page === 'intro'"
            src="@/assets/images_assets/icons/left-arrow-ic.png"
            alt=""
            @click="goToHome"
            class="left-arrow-ic"
          />
          <img
            v-else-if="page === 'guide'"
            src="@/assets/images_assets/icons/x-circle-ic.png"
            alt=""
            @click="goToGuide"
            class="x-circle-ic"
          />
          <img
            v-else
            src="@/assets/images_assets/icons/x-circle-ic.png"
            alt=""
            @click="goToExamIntro"
            class="x-circle-ic"
          />
        </div>
        <div v-if="page !== 'guide'" :class="lang === 'ko' ? 'skip-btn__box' : 'skip-btn_en'" @click="goToCamera">
          <div>📷 {{ $t("take_urine_test_btn") }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { refreshToken, setExp } from "@/api/common/tokenModule";
import { getSubjectId } from "../Common/getSubjectId.js";

export default {
  props: {
    page: String,
  },
  data() {
    return { lang: this.$i18n.locale === "ko" ? "ko" : "en" };
  },

  methods: {
    goToHome() {
      this.$router.push("/home");
    },
    goToExamIntro() {
      this.$router.push("/exam/intro");
    },
    async goToCamera() {
      const video = document.getElementById("exam-video");
      // alert(video);
      if (video !== null) {
        !video.paused ? video.pause() : null;
      }

      /*global Webview*/
      /*eslint no-undef: "error"*/
      const subjectId = getSubjectId();
      // const token = await refreshToken();
      // localStorage.auth = token;
      // this.$store.commit("SET_TOKEN", token);
      // setExp(token);
      const message = {
        action: "turnOnCameraMessage",
        accessToken: localStorage.auth,
        subId: subjectId,
      };
      // console.log(message);
      Webview.openCamera(message);
      this.videoPause();
    },

    goToGuide() {
      this.$router.push("/mypage/guide");
    },
    videoPause() {
      document.getElementById("exam-video").pause();
    },
  },
};
</script>

<style lang="scss" scoped>
.nav-section {
  width: 100%;
  max-width: 450px !important;
  position: fixed;
  z-index: 999;
  top: 0;
  padding-top: 50px;
  padding-bottom: 20px;
}
.active-background {
  background-color: #c9f4f8;
  width: 100%;
  max-width: 450px !important;
  position: fixed;
  z-index: 999;
  top: 0;
  padding-top: 40px;
}
.exam-nav__wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 10px 30px;
  background-color: transparent;
}
.close-btn__box {
  width: 30px;
  display: flex;
}
.left-arrow-ic {
  width: 12px;
}

.x-circle-ic {
  width: 30px;
}
.skip-btn__box {
  background: #ffffff;
  box-shadow: 1px 2px 2px rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  letter-spacing: -0.05em;
  font-size: 14px;
  font-weight: 500;
  padding: 5px 10px;
  img {
    width: 25px;
  }
}
.skip-btn_en {
  background: #ffffff;
  box-shadow: 1px 2px 2px rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  letter-spacing: -0.05em;
  font-size: 14px;
  font-family: GilroyMedium;
  padding: 5px 10px;
  img {
    width: 25px;
  }
}
</style>
