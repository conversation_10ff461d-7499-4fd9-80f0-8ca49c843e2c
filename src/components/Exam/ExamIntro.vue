<template>
  <div>
    <div class="content-section">
      <div class="notice-contents__wrapper">
        <div :class="lang === 'ko' ? 'notice-content-title__wrapper' : 'title_en'">
          <span class="notice-content-title__bold">Cym<sup>702</sup></span>
          <span v-html="this.$i18n.t('before_test')"></span>
        </div>
        <div class="content-items__wrapper">
          <div class="content-items" v-for="(item, i) in guideContent" :key="i">
            <div class="content-item">
              <div class="item--ic">{{ item.ic }}</div>
              <div class="item--txt">
                <div :class="lang === 'ko' ? 'txt--bold' : 'bold-en'">{{ item.textBold }}</div>
                <div :class="lang === 'ko' ? 'txt--normal' : 'normal-en'">{{ item.textNormal }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      lang: this.$i18n.locale === "ko" ? "ko" : "en",
      guideContent: [
        {
          ic: "💊",
          textBold: this.$i18n.t("caution_title1"),
          textNormal: this.$i18n.t("caution_content1"),
        },
        {
          ic: "🎈",
          textBold: this.$i18n.t("caution_title2"),
          textNormal: this.$i18n.t("caution_content2"),
        },
        {
          ic: "🌞",
          textBold: this.$i18n.t("caution_title3"),
          textNormal: this.$i18n.t("caution_content3"),
        },
      ],
    };
  },
  methods: {
    goToCamera() {
      this.$router.push({ path: "/exam/guide" });
    },
    goToHome() {
      this.$router.push({ path: "/home" });
    },
  },

  beforeDestroy() {
    window.removeEventListener("message");
    document.removeEventListener("message");
  },
};
</script>

<style scoped>
button {
  height: 50px;
  z-index: 999999;
  width: 100%;
  border-radius: 5px;
  line-height: 50px;
  font-size: 20px;
  font-weight: bold;
  background-color: #dadada;
}

/* content-section */
.content-section {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 17vh 30px 20vh;
  color: #000000;
}

.title_en {
  font-family: GilroyBold;
  width: 100%;
  text-align: left;
  font-style: normal;
  font-weight: 500;
  font-size: 26px;
  line-height: 34px;
  /* padding-bottom: 20px; */
  color: #000000;
}

.content-section .notice-content-title__wrapper {
  width: 100%;
  text-align: left;
  font-family: Noto Sans KR;
  font-style: normal;
  font-weight: 500;
  font-size: 26px;
  line-height: 34px;
  /* padding-bottom: 20px; */
  color: #000000;
}

.notice-content-title__bold {
  font-family: GilroyBold;
  line-height: 31px;
  font-weight: 500;
  letter-spacing: 0;
}

.content-item {
  margin-top: 5vh;
  display: flex;
}

.content-items__wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  /* align-items: center; */
  justify-content: center;
}

.content-items .content-item .item--txt {
  text-align: left;
  padding-left: 15px;
  font-size: 14px;
  line-height: 25px;
  font-weight: 500;
  letter-spacing: -0.03em;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.content-items .content-item .item--ic {
  font-size: 36px;
}

.content-items .content-item .item--txt .txt--bold {
  font-weight: 500;
  font-size: 22px;
}

.bold-en {
  font-family: GilroyMedium;
  font-size: 22px;
}

.txt--normal {
  font-size: 18px;
  font-weight: 400;
}
.normal-en {
  font-family: GilroyMedium;
  font-size: 18px;
  font-weight: 400;
}
/* button-section */

.button-section {
  width: 100%;
  position: fixed;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
  padding: 0 30px;
}
.exam-next-btn {
  width: 100%;
  max-width: 390px !important;
  height: 40px;
  color: #fff;
  background: #41d8e6;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  font-weight: 700;
  font-size: 20px;
}
</style>
