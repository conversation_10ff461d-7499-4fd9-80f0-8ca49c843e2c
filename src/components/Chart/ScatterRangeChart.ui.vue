<template>
  <div ref="chartContainer" class="chart-container">
    <!-- 상단 정보 표시 영역 -->
    <div 
      class="info-header" 
      :class="{ 'show': selectedData.visible }"
      v-if="selectedData.visible"
    >
      <div class="blood-pressure-info">
        {{ selectedData.pressure }} mmHg
      </div>
      <div class="date-info">
        {{ selectedData.date }}
      </div>
    </div>
    
    <!-- scatter-range chart -->
    <apexchart
      class="chart"
      ref="apexChart"
      height="450"
      type="rangeBar"
      :options="setChartOptions || chartOptions || {}"
      :series="setSeries || []"
      @touchstart.stop="handleTouch"
    />
  </div>
</template>

<script>
"use strict";

export default {
  name: "ScatterRangeChart",

  props: {
    series: {
      type: Array,
      required: true,
    },
    systolicGoal: {
      type: Number,
      required: true,
    },
    diastolicGoal: {
      type: Number,
      required: true,
    },
  },

  computed: {
    setSeries() {
      if (!Array.isArray(this.series) || this.series.length === 0) {
        return [];
      }

      // 혼합 차트를 위한 시리즈 변환
      const transformedSeries = [];
      
      this.series.forEach((series) => {
        if (series.name.includes('Range')) {
          // Range Bar 시리즈
          transformedSeries.push({
            name: series.name,
            type: 'rangeBar',
            data: series.data,
            color: series.color
          });
        } else {
          // Scatter 시리즈 - null 값 처리
          transformedSeries.push({
            name: series.name,
            type: 'scatter',
            data: series.data.map(item => ({
              x: item.x,
              y: item.y === null ? null : item.y
            })),
            color: series.color
          });
        }
      });

      return transformedSeries;
    },
    setChartOptions() {
      const minY = this.series
        ? Math.min(
            ...this.series.flatMap((s) =>
              s.data.map((d) => (Array.isArray(d.y) ? d.y[0] : d.y)).filter(val => val !== null)
            )
          )
        : 60;
      const maxY = this.series
        ? Math.max(
            ...this.series.flatMap((s) =>
              s.data.map((d) => (Array.isArray(d.y) ? d.y[1] : d.y)).filter(val => val !== null)
            )
          )
        : 160;

      const yAxisMin = Math.max(60, minY - 10);
      const yAxisMax = Math.min(160, maxY + 10);

      return {
        chart: {
          height: 450,
          type: 'rangeBar',
          animations: {
            enabled: false
          },
          toolbar: {
            show: false,
          },
          events: {
            dataPointSelection: (event, chartContext, config) => {
              this.handleDataPointClick(event, chartContext, config);
            },
            click: (event, chartContext, config) => {
              // 빈 공간 클릭시 선택 해제
              if (config.dataPointIndex === undefined) {
                this.clearSelection();
              }
            }
          }
        },
        plotOptions: {
          bar: {
            horizontal: false,
            borderRadius: 10,
            columnWidth: '35%',
          },
        },
        markers: {
          size: 8,
          strokeWidth: 0,
          hover: {
            size: 10
          }
        },
        xaxis: {
          type: 'category',
          labels: {
            show: false
          },
          axisBorder: {
            show: false
          },
          axisTicks: {
            show: false
          }
        },
        yaxis: {
          min: yAxisMin,
          max: yAxisMax,
          tickAmount: 5,
          labels: {
            formatter: function (value) {
              const roundedValue = Math.round(value);
              // 특정 값들 숨기기 (60, 100, 140)
              if (roundedValue === 60 || roundedValue === 100 || roundedValue === 140) {
                return '';
              }
              return roundedValue;
            },
            style: {
              fontSize: '14px',
              colors: '#6b7280'
            }
          },
          axisBorder: {
            show: false
          },
          axisTicks: {
            show: false
          }
        },
        colors: ['#E53E3E', '#E53E3E', '#E8924D', '#E8924D'],
        legend: {
          show: false
        },
        dataLabels: {
          enabled: false
        },
        grid: {
          show: true,
          borderColor: '#e2e8f0',
          strokeDashArray: 2,
          xaxis: {
            lines: {
              show: false
            }
          },
          yaxis: {
            lines: {
              show: true
            }
          },
        },
        tooltip: {
          enabled: true,
          shared: false,
          intersect: true,
          y: {
            formatter: function(val, opts) {
              if (val === null) {
                return;
              }
              
              if (!opts || typeof opts.seriesIndex === 'undefined' || !opts.w || !opts.w.config.series[opts.seriesIndex]) {
                return val;
              }

              if (opts.w.config.series[opts.seriesIndex].type === 'rangeBar') {
                const range = opts.w.globals.initialSeries[opts.seriesIndex].data[opts.dataPointIndex].y;
                if (Array.isArray(range)) {
                  return `${range[0]} - ${range[1]} mmHg`;
                }
              }
              return `${val} mmHg`;
            }
          }
        },
        annotations: {
          position: "back",
          xaxis: this.verticalLines, // 수직선 추가
          yaxis: [
            {
              y: this.systolicGoal,
              borderColor: '#E53E3E',
              borderWidth: 1.5,
              strokeDashArray: 6,
            },
            {
              y: this.diastolicGoal,
              borderColor: '#E8924D',
              borderWidth: 1.5,
              strokeDashArray: 6,
            }
          ]
        },
      };
    },
  },

  data() {
    return {
      chartOptions: {},
      selectedData: {
        visible: false,
        pressure: '',
        date: '',
        index: -1
      },
      verticalLines: []
    };
  },

  methods: {
    handleTouch(e) {
      e.stopPropagation();
    },
    
    handleDataPointClick(event, chartContext, config) {
      const { dataPointIndex, seriesIndex, w } = config;
      
      if (dataPointIndex === undefined || seriesIndex === undefined) return;
      
      // 선택된 데이터 정보 가져오기
      const selectedDate = w.globals.labels[dataPointIndex];
      const selectedSeries = w.config.series[seriesIndex];
      const dataPoint = selectedSeries.data[dataPointIndex];
      
      if (!dataPoint || dataPoint.y === null) return;
      
      // 혈압 정보 구성
      let pressureInfo = '';
      let systolicValue = null;
      let diastolicValue = null;
      
      // 시리즈에서 해당 날짜의 모든 데이터 찾기
      this.series.forEach(series => {
        const matchingData = series.data.find(d => d.x === selectedDate);
        if (matchingData && matchingData.y !== null) {
          if (series.name.includes('Systolic')) {
            if (Array.isArray(matchingData.y)) {
              systolicValue = `${matchingData.y[0]}~${matchingData.y[1]}`;
            } else {
              systolicValue = matchingData.y;
            }
          } else if (series.name.includes('Diastolic')) {
            if (Array.isArray(matchingData.y)) {
              diastolicValue = `${matchingData.y[0]}~${matchingData.y[1]}`;
            } else {
              diastolicValue = matchingData.y;
            }
          }
        }
      });
      
      // 혈압 정보 조합
      if (systolicValue && diastolicValue) {
        pressureInfo = `${systolicValue}/${diastolicValue}`;
      } else if (systolicValue) {
        pressureInfo = `${systolicValue}/-`;
      } else if (diastolicValue) {
        pressureInfo = `-/${diastolicValue}`;
      }
      
      // 현재 년도 추가
      const currentYear = new Date().getFullYear().toString().substr(2);
      const formattedDate = `${currentYear}.${selectedDate}`;
      
      // 선택 데이터 업데이트
      this.selectedData = {
        visible: true,
        pressure: pressureInfo,
        date: formattedDate,
        index: dataPointIndex
      };
      
      // 수직선 추가
      this.addVerticalLine(selectedDate);
      
      // 시각적 피드백 (1초 후 색상 원복)
      setTimeout(() => {
        const infoElement = this.$el.querySelector('.blood-pressure-info');
        if (infoElement) {
          infoElement.style.color = '#2d3748';
        }
      }, 1000);
    },
    
    addVerticalLine(selectedDate) {
      this.verticalLines = [
        {
          x: selectedDate,
          borderColor: '#6B7280',
          strokeDashArray: 4,
          opacity: 1,
        }
      ];
      
      // ApexCharts 옵션 업데이트
      this.$refs.apexChart.updateOptions({
        annotations: {
          xaxis: this.verticalLines,
          yaxis: [
            {
              y: this.systolicGoal,
              borderColor: '#E53E3E',
              borderWidth: 1.5,
              strokeDashArray: 6,
            },
            {
              y: this.diastolicGoal,
              borderColor: '#E8924D',
              borderWidth: 1.5,
              strokeDashArray: 6,
            }
          ]
        }
      }, true, true);
    },
    
    clearSelection() {
      this.selectedData = {
        visible: false,
        pressure: '',
        date: '',
        index: -1
      };
      
      this.verticalLines = [];
      
      // 수직선 제거
      this.$refs.apexChart.updateOptions({
        annotations: {
          xaxis: [],
          yaxis: [
            {
              y: this.systolicGoal,
              borderColor: '#E53E3E',
              borderWidth: 1.5,
              strokeDashArray: 6,
            },
            {
              y: this.diastolicGoal,
              borderColor: '#E8924D',
              borderWidth: 1.5,
              strokeDashArray: 6,
            }
          ]
        }
      }, true, true);
    }
  },

  mounted() {},
};
</script>

<style scoped lang="scss">
.chart-container {
  background-color: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  width: 100%;
  max-width: 900px;
  margin: 20px auto;
  position: relative;
}

/* 상단 정보 표시 영역 */
.info-header {
  text-align: center;
  margin-bottom: 15px;
  background-color: #f8f9fa;
  padding: 10px 15px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  
  &.show {
    opacity: 1;
    transform: translateY(0);
  }
}

.blood-pressure-info {
  font-size: 18px;
  font-weight: bold;
  color: #E53E3E;
  margin-bottom: 2px;
  transition: color 0.3s ease;
}

.date-info {
  font-size: 14px;
  color: #718096;
  margin-bottom: 5px;
}

.chart {
  width: 100%;
  cursor: pointer;
}

/* ApexCharts 스타일 오버라이드 */
::v-deep(.apexcharts-gridline:nth-of-type(2)) {
  display: none;
}

::v-deep(.apexcharts-xaxis-annotations) {
  z-index: -1 !important;
  position: relative;
}

/* 혼합 차트 타입을 위한 추가 스타일 */
::v-deep(.apexcharts-series) {
  pointer-events: all;
}

::v-deep(.apexcharts-bar-area) {
  cursor: pointer;
}

::v-deep(.apexcharts-scatter-series) {
  cursor: pointer;
}

/* 데이터 포인트 호버 효과 */
::v-deep(.apexcharts-marker) {
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.2);
  }
}

/* 범위 바 호버 효과 */
::v-deep(.apexcharts-bar-area) {
  transition: opacity 0.2s ease;
  
  &:hover {
    opacity: 0.8;
  }
}
</style>
