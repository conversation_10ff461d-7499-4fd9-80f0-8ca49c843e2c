<template>
  <div>
    <div :class="count === 'recent' ? 'chart' : 'year-chart'">
      <!-- width="100%" -->
      <apexchart
        height="250"
        type="line"
        :options="chartOptions"
        :series="series"
        ref="lineChart"
        @touchstart="mouseDownEventHandler"
        @mouseenter="mouseDownEventHandler"
        @click="mouseDownEventHandler"
      ></apexchart>
      <div v-if="!tabChanged" class="chart__tooltip">
        <div
          class="tooltip__title"
          :class="`${tooltipTitleColor}-txt`"
          @click="showWeeksTrendGraph"
        >
          <span>{{ tooltipTitle }}{{ unit }}</span>
          <img
            src="@/assets/images/guide-card-arrow.png"
            alt=""
            v-if="
              page !== 'cym' &&
              page !== 'weight' &&
              page !== 'step' &&
              count === 'm'
            "
          />
        </div>
        <div class="tooltip__date">{{ tooltipDate }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  chartOptions,
  strokeOptions,
  colorsOptions,
  markerOptions,
  gridOptions,
  xaxisOptions,
  weightCareChartYaxisOptions,
  waterCareChartYaxisOptions,
  peeCareChartYaxisOptions,
  cymHistoryChartYaxisOptions,
  stepHistoryChartYAxisOptions,
} from "./chartOptions.js";

export default {
  props: {
    historyData: Array,
    page: String,
    count: String,
    min: Number,
    max: Number,
    target: Number,
    noData: Boolean,
    targetWater: Number,
  },
  data() {
    return {
      type: "",
      tooltipTitle: "",
      tooltipDate: "",
      tooltipTitleColor: "",
      chartData: [],
      chartLabels: [],
      series: [
        {
          name: "Series 1",
          data: [],
        },
      ],
      month: 0,
      chartOptions: {},
      showTooltip: false,
      isKo: true,
    };
  },
  watch: {
    count(newVal, oldVal) {
      if (newVal) {
        // this.makeLineChart();
        const tooltip = document.querySelectorAll(".chart__tooltip");

        tooltip.forEach((item) => {
          item.style.display = "none";
        });

        const xcrosshairs = document.querySelectorAll(
          ".apexcharts-xcrosshairs"
        );

        xcrosshairs.forEach((line) => {
          line.style.opacity = 0;
        });
      }
    },
    historyData(newVal) {
      // console.log(newVal);
      this.makeLineChart();
    },
    tooltipTitleColor(newVal) {
      // console.log(newVal);
    },
    tooltipTitle(newVal) {
      this.$store.commit("GET_CYM_AVG", newVal);
    },
    tabChanged(newVal) {
      // console.log(newVal);
    },
  },
  computed: {
    unit() {
      if (this.page === "cym") return this.$i18n.t("avg_score");
      if (this.page === "pee") return this.$i18n.t("times");
      if (this.page === "weight") return "kg";
      if (this.page === "water") return "ml";
      if (this.page === "step") return this.$i18n.t("step_title");

      return "";
    },
    tabChanged() {
      return this.$store.state.tabChange;
    },
  },
  methods: {
    careLineChartYaxisOptions(careType) {
      if (careType === "weight")
        return weightCareChartYaxisOptions(this.min, this.max);
      if (careType === "water")
        return waterCareChartYaxisOptions(this.targetWater);
      if (careType === "pee") return peeCareChartYaxisOptions();
      if (careType === "cym") return cymHistoryChartYaxisOptions();
      if (careType === "step") {
        return stepHistoryChartYAxisOptions();
      }
    },

    setInitTooltipPosition() {
      return {
        xcrosshairsPosition() {
          const xcrosshairs = document.querySelectorAll(
            ".apexcharts-xcrosshairs"
          );
          xcrosshairs.forEach((line) => {
            line.setAttribute("y1", "-13");
          });
        },
        setTooltipInitPosition(getLastPointXPosition) {
          const tooltip = document.querySelectorAll(".chart__tooltip");

          tooltip.forEach((item) => {
            item.style.left = `${getLastPointXPosition - 3}px`;
          });
        },
      };
    },

    setTooltipActivate(careType) {
      return {
        setTooltipPosition(seriesXvalue, dataPointIndex) {
          const tooltip = document.querySelectorAll(".chart__tooltip");
          if (
            careType === "weight" ||
            careType === "water" ||
            careType === "pee"
          ) {
            tooltip.forEach((item) => {
              item.style.display = "block";
              if (seriesXvalue[dataPointIndex] > 250) {
                careType === "water"
                  ? (item.style.left = `${seriesXvalue[dataPointIndex] - 30}px`)
                  : (item.style.left = `${
                      seriesXvalue[dataPointIndex] + 20
                    }px`);
              } else if (seriesXvalue[dataPointIndex] < 5) {
                item.style.left = `${seriesXvalue[dataPointIndex] + 45}px`;
              } else {
                item.style.left = `${seriesXvalue[dataPointIndex] + 30}px`;
              }
            });
          } else {
            tooltip.forEach((item) => {
              item.style.display = "block";
              if (seriesXvalue[dataPointIndex] > 250) {
                item.style.left = `${seriesXvalue[dataPointIndex] - 30}px`;
              } else if (seriesXvalue[dataPointIndex] < 5) {
                item.style.left = `${seriesXvalue[dataPointIndex] + 40}px`;
              } else {
                item.style.left = `${seriesXvalue[dataPointIndex] + 10}px`;
              }
            });
          }
        },
      };
    },

    makeTooltipDate(createdAt) {
      const [date, time] = createdAt.split("T");
      let [y, m, d] = date.split("-");
      let [hh, mm, ss] = time.split(":");
      const timeset = Number(hh) >= 12 ? "PM" : "AM";
      hh = Number(hh) % 12 || 12;
      m = Number(m) < 10 ? `0${Number(m)}` : Number(m);
      d = Number(d) < 10 ? `0${Number(d)}` : d;
      hh = Number(hh) < 10 ? `0${Number(hh)}` : Number(hh);
      mm = mm < 10 ? `0${Number(mm)}` : mm;

      return `${String(y).slice(2)}.${m}.${d} ${hh}:${mm} ${timeset}`;
    },

    getTooltipContents() {
      return {
        cymChartTooltipTitle: (idx) => {
          this.tooltipTitle = this.historyData[idx].value;
          if (
            this.historyData[idx].createdAt.length > 8 &&
            this.page === "cym"
          ) {
            this.tooltipDate = this.makeTooltipDate(
              this.historyData[idx].createdAt
            );
          } else if (this.historyData[idx].createdAt.length === 5) {
            const [y, m] = this.historyData[idx].createdAt.split(".");
            this.tooltipDate = this.isKo
              ? `${y}${this.$i18n.t("avg_year")} ${m}${this.$i18n.t(
                  "avg_month"
                )} ${this.$i18n.t("average")}`
              : `${this.$i18n.t("average")} ${y}${this.$i18n.t(
                  "avg_year"
                )}${m}`;
          } else {
            console.log(this.historyData[idx].createdAt);
            this.tooltipDate = this.historyData[idx].createdAt.slice(2);
          }
        },
      };
    },
    mouseDownEventHandler(e) {
      // console.log(e);
      this.$emit("clickEventHandler");
    },
    getMonthHistoryData() {
      return {
        getMonth: (month) => {
          this.$emit("getMonth", month + 1);
        },
        getDate: (date) => {
          // console.log(this.historyData);
          this.$emit("getDate", this.historyData[date]);
        },
      };
    },

    setChartOptions(count) {
      //📈 chart options
      const colors = colorsOptions(this.page);
      const markers = markerOptions(this.page);
      const chart = chartOptions();
      const yaxis = this.careLineChartYaxisOptions(this.page);
      const stroke = strokeOptions();
      const xaxis = xaxisOptions(this.chartLabels);
      const grid = gridOptions();
      const setTooltipPosition = this.setTooltipActivate(this.page);
      const getTooltipContent = this.getTooltipContents();
      const getMonthHistoryData = this.getMonthHistoryData();
      const store = this.$store;

      return {
        zoom: {
          enabled: false,
        },
        annotations: {
          position: "back",
          yaxis:
            this.page === "weight"
              ? [
                  {
                    y: this.target,
                    y2: null,
                    strokeDashArray: 4,
                    borderColor: "#41D8E6",
                  },
                ]
              : [
                  {
                    y: 0,
                    y2: null,
                    strokeDashArray: 0,
                    borderColor: "#D0D0D0",
                  },
                ],
        },
        chart: {
          ...chart.careLineChartOption(),
        },
        dataLabels: {
          enabled: false,
        },

        colors: colors.careLineChartOptions(),
        markers: { ...markers.careLineChartOptions() },
        yaxis: { ...yaxis },
        stroke,
        xaxis,
        grid,
        tooltip: {
          trigger: "click",
          enabled: true,
          custom({ series, seriesIndex, dataPointIndex, w }) {
            store.commit("SET_TOOLTIP_INDEX", dataPointIndex);
            const seriesXvalue = w.globals.seriesXvalues[0];
            setTooltipPosition.setTooltipPosition(seriesXvalue, dataPointIndex);

            const xcrosshairs = document.querySelectorAll(
              ".apexcharts-xcrosshairs"
            );

            xcrosshairs.forEach((line) => {
              line.style.opacity = 1;
              line.setAttribute("y1", `-13`);
            });

            getTooltipContent.cymChartTooltipTitle(dataPointIndex);

            if (count === "m") {
              getMonthHistoryData.getDate(dataPointIndex);
            }
            if (count === "y") {
              getMonthHistoryData.getMonth(dataPointIndex);
            }

            return "";
          },
        },
      };
    },
    updateSeriesLine(chartData) {
      this.$refs.lineChart.updateSeries(
        [
          {
            name: "History",
            data: chartData,
          },
        ],
        false,
        true
      );
    },
    makeLineChart() {
      // count에 따라 날짜 양식 바꾸기
      if (!this.noData) {
        const chartData = this.historyData.map((item) => item.value);
        const chartLabels = this.historyData.map((item) => {
          if (item.createdAt && item.createdAt.length === 5) {
            const [Y, M] = item.createdAt.split(".");
            return `${M}`;
          } else if (item.createdAt.length === 10) {
            const [Y, M, D] = item.createdAt.split(".");
            return `${M}.${D}`;
          } else if (item.createdAt.length > 8) {
            const [date, time] = item.createdAt.split("T");
            let [Y, M, D] = date.split("-");
            return `${M}.${D}`;
          } else {
            return item.createdAt.slice(5);
          }
        });
        // console.log(chartLabels);
        this.chartLabels = chartLabels;
        this.chartOptions = this.setChartOptions(this.count);
        this.updateSeriesLine(chartData);
      }
    },
    showWeeksTrendGraph() {
      this.count === "m" ? this.$emit("showWeeksTrendGraph") : null;
    },
  },

  mounted() {
    // console.log(this.historyData);
    this.makeLineChart();
    this.isKo = this.$i18n.locale === "ko";
  },
};
</script>

<style lang="scss" scoped>
.chart {
  padding: 0px 2px 0 5px;
  position: relative;
  height: 255px;
}
.year-chart {
  padding: 0px 31px 0 10px;
  position: relative;
}
.chart__tooltip {
  display: none;
  min-width: 60px;
  position: absolute;
  top: -30px;
  background: #ededed;
  border-radius: 5px;
  padding: 0 5px;
}

.tooltip__title {
  font-size: 22px;
  font-weight: bold;
  line-height: 20px;
  margin-top: 5px;
  img {
    width: 8px;
    margin-left: 4px;
  }
}

.tooltip__date {
  font-family: GilroyMedium;
  font-size: 14px;
  color: #646464;
  margin-top: 2px;
}

.tooltip__title--ph {
  font-size: 12px;
}

::v-deep .normal-txt {
  color: #00bb00;
}

::v-deep .warning-txt {
  color: #ffcc00 !important;
}

::v-deep .caution-txt {
  color: #ff6600;
}

::v-deep .danger-txt {
  color: #646464;
}

::v-deep .good-txt {
  color: #41d8e6;
}

::v-deep text {
  font-family: GilroyMedium !important;
  color: #646464;
}

::v-deep .apexcharts-yaxis-label {
  // font-size: 14px;
}
::v-deep .apexcharts-xaxis-label {
  // font-size: 13px;
  // font-size: 16px;
  // transform: rotate(0);
  letter-spacing: -0.08em;
  font-family: GilroyMedium !important;
}

::v-deep .apexcharts-xcrosshairs {
  stroke-dasharray: 0 !important;
  stroke: #ededed !important;
}

::v-deep path {
  filter: none !important;
}
</style>
