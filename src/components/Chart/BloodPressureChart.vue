<template>
  <div class="w-full max-w-4xl p-6 mx-auto bg-white shadow-lg rounded-2xl">
    <div class="tooltip-wrapper">
      <div
        id="info-header"
        class="info-header-btn"
        :style="infoHeaderLeft !== null ? { left: infoHeaderLeft + 'px' } : {}"
      >
        <div
          id="pressureInfo"
          class="mb-1 text-2xl font-bold md:text-3xl text-slate-700 pressure-info"
        >
          &nbsp;
        </div>
        <div
          id="dateInfo"
          class="h-6 text-sm md:text-base text-slate-500"
        ></div>
      </div>
    </div>

    <div class="chart-container relative w-full h-[400px] md:h-[450px]">
      <canvas id="chartCanvas" class="cursor-crosshair"></canvas>
    </div>
  </div>
</template>

<script>
export default {
  name: "BloodPressureC<PERSON>",

  props: {
    bloodPressureData: {
      // Map 객체를 허용하도록 타입 체크 수정
      type: [Object, Map], // Object와 Map 둘 다 허용
      default: () => new Map(),
      validator(value) {
        // Map 객체이거나 일반 객체인 경우 허용
        return (
          value instanceof Map || (typeof value === "object" && value !== null)
        );
      },
    },
    period: {
      type: String,
      default: "w",
    },
  },

  data() {
    return {
      canvas: null,
      ctx: null,
      chartData: {
        labels: [],
        systolicData: [],
        diastolicData: [],
        ranges: [],
      },
      hoveredIndex: -1,
      selectedIndex: -1,
      chartLayout: {
        padding: { top: 40, right: 40, bottom: 50, left: 50 },
        yMin: 60,
        yMax: 160,
      },
      // 이벤트 핸들러 바인딩 참조
      boundResizeHandler: null,
      boundMouseMoveHandler: null,
      boundMouseLeaveHandler: null,
      boundClickHandler: null,
      // 터치 이벤트 관련
      touchStartTime: null,
      infoHeaderLeft: null, // info-header-btn의 left 위치(px)
    };
  },

  watch: {
    // bloodPressureData가 Map 객체일 때 deep: true는 효과가 없을 수 있습니다.
    // Map 객체 자체가 교체될 때만 watch가 동작합니다.
    // Map 내부의 변경을 감지하려면 computed 속성이나 별도의 관찰 로직이 필요할 수 있습니다.
    bloodPressureData: {
      handler() {
        this.prepareChartData();
        this.drawChart();
      },
      // Map 객체 내부 변경 감지를 위해서는 이 `deep: true`가 충분하지 않을 수 있습니다.
      // 하지만 현재는 Map 객체 자체가 변경(예: 새로운 Map 할당)될 때 반응하면 되므로 그대로 둡니다.
      deep: true,
    },
    period() {
      this.prepareChartData();
      this.drawChart();
      this.resetInfoHeader(); // period 변경 시 info header 숨김
    },
  },

  mounted() {
    this.$nextTick(() => {
      this.init();
    });
  },

  beforeDestroy() {
    this.cleanup();
  },

  beforeUnmount() {
    // Vue 3 호환성을 위한 추가
    this.cleanup();
  },

  methods: {
    init() {
      this.canvas = document.getElementById("chartCanvas");
      if (!this.canvas) {
        console.error("Canvas element not found");
        return;
      }

      this.ctx = this.canvas.getContext("2d");
      if (!this.ctx) {
        console.error("Canvas context not available");
        return;
      }

      this.prepareChartData();
      this.resizeCanvas();
      this.setupEventListeners();
    },

    setupEventListeners() {
      // 이벤트 리스너 등록 전에 기존 리스너 정리
      this.cleanup();

      // 이벤트 핸들러를 바인딩하여 this 컨텍스트 보장
      this.boundResizeHandler = this.resizeCanvas.bind(this);
      this.boundMouseMoveHandler = this.handleMouseMove.bind(this);
      this.boundMouseLeaveHandler = this.handleMouseLeave.bind(this);
      this.boundClickHandler = this.handleChartClick.bind(this);

      // 이벤트 리스너 등록
      window.addEventListener("resize", this.boundResizeHandler, {
        passive: true,
      });

      if (this.canvas) {
        this.canvas.addEventListener("mousemove", this.boundMouseMoveHandler, {
          passive: true,
        });
        this.canvas.addEventListener(
          "mouseleave",
          this.boundMouseLeaveHandler,
          { passive: true }
        );
        this.canvas.addEventListener("click", this.boundClickHandler);

        // 터치 이벤트도 지원
        this.canvas.addEventListener(
          "touchstart",
          this.handleTouchStart.bind(this),
          { passive: false }
        );
        this.canvas.addEventListener(
          "touchmove",
          this.handleTouchMove.bind(this),
          { passive: false }
        );
        this.canvas.addEventListener(
          "touchend",
          this.handleTouchEnd.bind(this),
          { passive: false }
        );
      }
    },

    cleanup() {
      // 기존 이벤트 리스너 정리
      if (this.boundResizeHandler) {
        window.removeEventListener("resize", this.boundResizeHandler);
      }

      if (this.canvas) {
        if (this.boundMouseMoveHandler) {
          this.canvas.removeEventListener(
            "mousemove",
            this.boundMouseMoveHandler
          );
        }
        if (this.boundMouseLeaveHandler) {
          this.canvas.removeEventListener(
            "mouseleave",
            this.boundMouseLeaveHandler
          );
        }
        if (this.boundClickHandler) {
          this.canvas.removeEventListener("click", this.boundClickHandler);
        }

        // 터치 이벤트 정리
        this.canvas.removeEventListener("touchstart", this.handleTouchStart);
        this.canvas.removeEventListener("touchmove", this.handleTouchMove);
        this.canvas.removeEventListener("touchend", this.handleTouchEnd);
      }

      // 바인딩된 핸들러 참조 정리
      this.boundResizeHandler = null;
      this.boundMouseMoveHandler = null;
      this.boundMouseLeaveHandler = null;
      this.boundClickHandler = null;
    },

    getWeekDates() {
      // bloodPressureData가 Map 객체인지 확인
      if (
        !this.bloodPressureData ||
        typeof this.bloodPressureData.keys !== "function"
      ) {
        console.warn(
          "bloodPressureData is not a Map object:",
          this.bloodPressureData
        );
        return [];
      }

      // Map의 키를 배열로 변환합니다. Map은 삽입 순서를 유지합니다.
      const allKeys = Array.from(this.bloodPressureData.keys());

      if (this.period !== "m") {
        // 주간/연간: 모든 key 반환
        return allKeys;
      }

      // 월간: 데이터가 있는 날짜 key만 반환
      return allKeys.filter((key) => {
        const d = this.bloodPressureData.get(key);

        if (Object.keys(d).length < 1) {
          return false;
        }

        return d && (d.systolic !== undefined || d.diastolic !== undefined);
      });
    },

    prepareChartData() {
      this.chartData.labels = this.getWeekDates();

      this.chartData.systolicData = this.chartData.labels.map((date) => {
        const data = this.bloodPressureData.get
          ? this.bloodPressureData.get(date)
          : this.bloodPressureData[date];
        return data?.systolic ?? null;
      });
      this.chartData.diastolicData = this.chartData.labels.map((date) => {
        const data = this.bloodPressureData.get
          ? this.bloodPressureData.get(date)
          : this.bloodPressureData[date];
        return data?.diastolic ?? null;
      });
      this.chartData.ranges = this.chartData.labels.map((date) => {
        const data = this.bloodPressureData.get
          ? this.bloodPressureData.get(date)
          : this.bloodPressureData[date];
        return data?.range ?? null;
      });

      // 동적으로 Y축 범위 계산
      this.calculateDynamicYRange();
    },

    calculateDynamicYRange() {
      // 모든 혈압 데이터 수집
      const allValues = [];

      // systolic 데이터 추가
      this.chartData.systolicData.forEach((value) => {
        if (value !== null && value !== undefined) {
          allValues.push(value);
        }
      });

      // diastolic 데이터 추가
      this.chartData.diastolicData.forEach((value) => {
        if (value !== null && value !== undefined) {
          allValues.push(value);
        }
      });

      // range 데이터 추가 (range는 [min, max] 배열)
      this.chartData.ranges.forEach((range) => {
        if (range && Array.isArray(range)) {
          allValues.push(range[0], range[1]);
        }
      });

      // 데이터가 없는 경우 기본값 사용
      if (allValues.length === 0) {
        this.chartLayout.yMin = 60;
        this.chartLayout.yMax = 160;
        return;
      }

      const minValue = Math.min(...allValues);
      const maxValue = Math.max(...allValues);

      // min 계산: 데이터 최솟값에서 10 빼고, 20 단위로 내림
      let calculatedMin = Math.floor((minValue - 10) / 20) * 20;
      // min이 너무 작아지지 않도록 최소 40으로 제한
      calculatedMin = Math.max(calculatedMin, 40);

      // max 계산: 데이터 최댓값에서 10 더하고, 20 단위로 올림
      let calculatedMax = Math.ceil((maxValue + 10) / 20) * 20;

      // max 범위 제한: 120 < max <= 160
      if (calculatedMax <= 120) {
        calculatedMax = 140; // 120보다 작거나 같으면 140으로 설정
      } else if (calculatedMax > 250) {
        calculatedMax = 250; // 250보다 크면 250으로 제한
      }

      // 최소 범위 보장 (최소 60 차이)
      if (calculatedMax - calculatedMin < 60) {
        const center = (calculatedMax + calculatedMin) / 2;
        calculatedMin = Math.floor((center - 30) / 20) * 20;
        calculatedMax = Math.ceil((center + 30) / 20) * 20;

        // 다시 범위 제한 적용
        calculatedMin = Math.max(calculatedMin, 40);
        if (calculatedMax <= 120) {
          calculatedMax = 140;
        } else if (calculatedMax > 160) {
          calculatedMax = 160;
        }
      }

      this.chartLayout.yMin = calculatedMin;
      this.chartLayout.yMax = calculatedMax;
    },

    resizeCanvas() {
      const dpr = window.devicePixelRatio || 1;
      const rect = this.canvas.getBoundingClientRect();

      this.canvas.width = rect.width * dpr;
      this.canvas.height = rect.height * dpr;

      this.ctx.scale(dpr, dpr);

      this.drawChart();
    },

    drawChart() {
      if (!this.canvas || !this.ctx) {
        console.error("Canvas or context not available");
        return;
      }

      const { width, height } = this.canvas.getBoundingClientRect();

      this.ctx.clearRect(0, 0, width, height);

      const chartArea = {
        x: this.chartLayout.padding.left,
        y: this.chartLayout.padding.top,
        width:
          width -
          this.chartLayout.padding.left -
          this.chartLayout.padding.right,
        height:
          height -
          this.chartLayout.padding.top -
          this.chartLayout.padding.bottom,
      };

      this.drawGrid(chartArea);
      this.drawAxisLabels(chartArea);
      this.drawReferenceLines(chartArea);
      this.drawRangeBars(chartArea);

      this.drawDataPoints(
        chartArea,
        this.chartData.systolicData,
        "#ef4444",
        true
      );

      this.drawDataPoints(
        chartArea,
        this.chartData.diastolicData,
        "#f97316",
        false
      );

      if (this.hoveredIndex !== -1) {
        this.drawHoverIndicator(chartArea, this.hoveredIndex);
      }
    },

    drawGrid(area) {
      this.ctx.strokeStyle = "#e2e8f0";
      this.ctx.lineWidth = 1;
      this.ctx.font = "12px GilroyBold";
      this.ctx.fillStyle = "#64748b";
      this.ctx.textAlign = "right";

      const yRange = this.chartLayout.yMax - this.chartLayout.yMin;

      const yValues = [
        this.chartLayout.yMin, // 최소값
        80, // 이완기 참조선
        120, // 수축기 참조선
        this.chartLayout.yMax, // 최대값
      ];

      // 중복 제거 및 범위 내 값만 필터링
      const filteredYValues = [...new Set(yValues)]
        .filter(
          (value) =>
            value >= this.chartLayout.yMin && value <= this.chartLayout.yMax
        )
        .sort((a, b) => a - b);

      this.ctx.setLineDash([3, 3]);

      // 필터링된 값들에만 그리드 선과 라벨 표시
      filteredYValues.forEach((value) => {
        const y =
          area.y +
          area.height -
          ((value - this.chartLayout.yMin) / yRange) * area.height;

        // 그리드 선 그리기
        this.ctx.beginPath();
        this.ctx.moveTo(area.x, y);
        this.ctx.lineTo(area.x + area.width, y);
        this.ctx.stroke();

        // Y축 라벨 표시
        this.ctx.fillText(value.toString(), area.x - 8, y + 4);
      });

      this.ctx.setLineDash([]);
    },

    drawAxisLabels(area) {
      this.ctx.fillStyle = "#64748b";
      this.ctx.font = "12px Inter";
      this.ctx.textAlign = "center";
      const xStep = area.width / (this.chartData.labels.length - 1);

      this.chartData.labels.forEach((label, index) => {
        const x = area.x + index * xStep;
        this.ctx.fillText(label, x, area.y + area.height + 25);
      });
    },

    drawReferenceLines(area) {
      const values = [
        { value: 120, color: "#ef4444" },
        { value: 80, color: "#f97316" },
      ];

      this.ctx.lineWidth = 1;
      this.ctx.setLineDash([5, 5]);

      values.forEach(({ value, color }) => {
        const y =
          area.y +
          area.height -
          ((value - this.chartLayout.yMin) /
            (this.chartLayout.yMax - this.chartLayout.yMin)) *
            area.height;
        this.ctx.strokeStyle = color;

        this.ctx.beginPath();
        this.ctx.moveTo(area.x, y);
        this.ctx.lineTo(area.x + area.width, y);
        this.ctx.stroke();
      });

      this.ctx.setLineDash([]);
    },

    drawRoundedRect(x, y, width, height, radius) {
      this.ctx.beginPath();
      if (height < 2 * radius) radius = height / 2;
      if (width < 2 * radius) radius = width / 2;
      this.ctx.moveTo(x + radius, y);
      this.ctx.arcTo(x + width, y, x + width, y + height, radius);
      this.ctx.arcTo(x + width, y + height, x, y + height, radius);
      this.ctx.arcTo(x, y + height, x, y, radius);
      this.ctx.arcTo(x, y, x + width, y, radius);
      this.ctx.closePath();
      this.ctx.fill();
    },

    drawRangeBars(area) {
      const xStep = area.width / (this.chartData.labels.length - 1);
      const barWidth = 16;
      const borderRadius = 8;
      this.ctx.fillStyle = "#C85C5C";

      this.chartData.ranges.forEach((range, index) => {
        if (range) {
          const x = area.x + index * xStep;
          const yTop =
            area.y +
            area.height -
            ((range[1] - this.chartLayout.yMin) /
              (this.chartLayout.yMax - this.chartLayout.yMin)) *
              area.height;
          const yBottom =
            area.y +
            area.height -
            ((range[0] - this.chartLayout.yMin) /
              (this.chartLayout.yMax - this.chartLayout.yMin)) *
              area.height;
          const barHeight = yBottom - yTop;
          this.drawRoundedRect(
            x - barWidth / 2,
            yTop,
            barWidth,
            barHeight,
            borderRadius
          );
        }
      });
    },

    drawDataPoints(area, data, color, isSystolic = false) {
      if (!data || data.length === 0) {
        console.warn("No data to draw");
        return;
      }

      data.forEach((value, index) => {
        // 주석: range가 있어도 systolic 포인트를 표시하도록 변경
        // if (isSystolic && this.chartData.ranges[index] !== null && this.chartData.ranges[index] !== undefined) {
        //   return;
        // }

        if (value !== null && value !== undefined) {
          let x;

          // 데이터가 하나만 있으면 중앙에 표시
          if (data.length === 1) {
            x = area.x + area.width / 2;
          } else {
            // 여러 데이터가 있으면 균등하게 분배
            const xStep = area.width / (data.length - 1);
            x = area.x + index * xStep;
          }

          const y =
            area.y +
            area.height -
            ((value - this.chartLayout.yMin) /
              (this.chartLayout.yMax - this.chartLayout.yMin)) *
              area.height;

          let radius =
            this.hoveredIndex === index || this.selectedIndex === index ? 8 : 5;

          this.ctx.beginPath();
          this.ctx.fillStyle = color;
          this.ctx.arc(x, y, radius, 0, Math.PI * 2);
          this.ctx.fill();

          this.ctx.beginPath();
          this.ctx.strokeStyle = "white";
          this.ctx.lineWidth = 2;
          this.ctx.arc(x, y, radius, 0, Math.PI * 2);
          this.ctx.stroke();
        } else {
          return;
        }
      });
    },

    drawHoverIndicator(area, index) {
      const xStep = area.width / (this.chartData.labels.length - 1);
      const x = area.x + index * xStep;
      this.ctx.strokeStyle = "#94a3b8";
      this.ctx.lineWidth = 1;
      this.ctx.setLineDash([3, 3]);
      this.ctx.beginPath();
      this.ctx.moveTo(x, area.y);
      this.ctx.lineTo(x, area.y + area.height);
      this.ctx.stroke();
      this.ctx.setLineDash([]);
    },

    findNearestPoint(mouseX, mouseY) {
      const { width, height } = this.canvas.getBoundingClientRect();
      const chartArea = {
        x: this.chartLayout.padding.left,
        y: this.chartLayout.padding.top,
        width:
          width -
          this.chartLayout.padding.left -
          this.chartLayout.padding.right,
        height:
          height -
          this.chartLayout.padding.top -
          this.chartLayout.padding.bottom,
      };
      // 데이터가 없는 경우를 대비하여 length가 0일 때 xStep을 0으로 설정
      const xStep =
        this.chartData.labels.length > 1
          ? chartArea.width / (this.chartData.labels.length - 1)
          : 0; // 데이터 포인트가 1개 이하일 경우 예외 처리
      const rawIndex = xStep === 0 ? 0 : (mouseX - chartArea.x) / xStep;
      let index = Math.round(rawIndex);

      if (index < 0 || index >= this.chartData.labels.length) {
        return -1;
      }

      // Map에서 데이터 가져올 때 .get() 사용
      const dataEntry = this.bloodPressureData.get(
        this.chartData.labels[index]
      );

      if (
        !dataEntry ||
        (dataEntry.systolic === undefined && dataEntry.diastolic === undefined)
      ) {
        return -1;
      }
      return index;
    },

    handleMouseMove(event) {
      const rect = this.canvas.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      const index = this.findNearestPoint(mouseX, mouseY);
      if (index !== this.hoveredIndex) {
        this.hoveredIndex = index;
        this.drawChart();
      }
    },

    handleMouseLeave() {
      this.hoveredIndex = -1;
      this.drawChart();
    },

    handleChartClick(event) {
      event.preventDefault();
      const rect = this.canvas.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      const index = this.findNearestPoint(mouseX, mouseY);

      if (index !== -1) {
        this.selectedIndex = index;
        this.updateInfoHeader(this.selectedIndex);
        this.updateInfoHeaderPosition(index); // 추가: 위치 갱신
      } else {
        this.selectedIndex = -1;
        this.resetInfoHeader();
        this.infoHeaderLeft = null; // 위치 초기화
      }
      this.drawChart();

      // value 객체 전달 (Map에서 데이터 가져올 때 .get() 사용)
      let value = null;
      if (index !== -1) {
        const label = this.chartData.labels[index];
        value = { label, data: this.bloodPressureData.get(label) };
      }
      this.$emit("clickEventHandler", value);
    },

    // 터치 이벤트 핸들러 추가
    handleTouchStart(event) {
      event.preventDefault();
      this.touchStartTime = Date.now();
      const touch = event.touches[0];
      const rect = this.canvas.getBoundingClientRect();
      const touchX = touch.clientX - rect.left;
      const touchY = touch.clientY - rect.top;

      const index = this.findNearestPoint(touchX, touchY);
      if (index !== this.hoveredIndex) {
        this.hoveredIndex = index;
        this.drawChart();
      }
    },

    handleTouchMove(event) {
      event.preventDefault();
      const touch = event.touches[0];
      const rect = this.canvas.getBoundingClientRect();
      const touchX = touch.clientX - rect.left;
      const touchY = touch.clientY - rect.top;

      const index = this.findNearestPoint(touchX, touchY);
      if (index !== this.hoveredIndex) {
        this.hoveredIndex = index;
        this.drawChart();
      }
    },

    handleTouchEnd(event) {
      event.preventDefault();
      const touchEndTime = Date.now();
      const touchDuration = touchEndTime - (this.touchStartTime || 0);

      // 짧은 터치는 클릭으로 처리 (300ms 이하)
      if (touchDuration < 300 && this.hoveredIndex !== -1) {
        this.selectedIndex = this.hoveredIndex;
        this.updateInfoHeader(this.selectedIndex);
        this.updateInfoHeaderPosition(this.selectedIndex); // 추가: 위치 갱신
        // value 객체 전달 (Map에서 데이터 가져올 때 .get() 사용)
        let value = null;
        if (this.selectedIndex !== -1) {
          const label = this.chartData.labels[this.selectedIndex];
          value = { label, data: this.bloodPressureData.get(label) };
        }
        this.$emit("clickEventHandler", value);
      }

      // 터치 종료 후 hover 상태 초기화
      setTimeout(() => {
        this.hoveredIndex = -1;
        this.drawChart();
      }, 100);
    },

    updateInfoHeader(index) {
      if (index === -1) {
        this.resetInfoHeader();
        return;
      }

      const date = this.chartData.labels[index];
      // Map에서 데이터 가져올 때 .get() 사용
      const data = this.bloodPressureData.get(date);
      const infoHeaderEl = document.getElementById("info-header");
      const pressureInfoEl = document.getElementById("pressureInfo");
      const dateInfoEl = document.getElementById("dateInfo");

      if (data) {
        infoHeaderEl.classList.add("active");
        pressureInfoEl.textContent = `${data.systolic} / ${data.diastolic} mmHg`;
        const currentYear = new Date().getFullYear();
        dateInfoEl.textContent = `${currentYear}.${date}`;
      } else {
        this.resetInfoHeader();
      }
    },

    updateInfoHeaderPosition(index) {
      // index에 해당하는 x좌표 계산 후 infoHeaderLeft에 저장
      this.$nextTick(() => {
        const rect = this.canvas.getBoundingClientRect();
        const chartArea = {
          x: this.chartLayout.padding.left,
          y: this.chartLayout.padding.top,
          width:
            rect.width -
            this.chartLayout.padding.left -
            this.chartLayout.padding.right,
          height:
            rect.height -
            this.chartLayout.padding.top -
            this.chartLayout.padding.bottom,
        };
        // 데이터가 없는 경우를 대비하여 length가 0일 때 xStep을 0으로 설정
        const xStep =
          this.chartData.labels.length > 1
            ? chartArea.width / (this.chartData.labels.length - 1)
            : 0; // 데이터 포인트가 1개 이하일 경우 예외 처리
        const x = chartArea.x + index * xStep;
        const infoHeaderEl = document.getElementById("info-header");
        let offset = 0;
        let infoHeaderWidth = 0;
        if (infoHeaderEl) {
          infoHeaderWidth = infoHeaderEl.offsetWidth;
          offset = infoHeaderWidth / 2;
        }
        const wallPadding = 0;
        let left;
        if (index === 0) {
          // 왼쪽 끝: chartArea.x에 완전히 붙게
          left = chartArea.x + wallPadding;
        } else if (index === this.chartData.labels.length - 1) {
          // 오른쪽 끝: chartArea의 오른쪽에 완전히 붙게
          left = chartArea.x + chartArea.width - infoHeaderWidth - wallPadding;
        } else {
          // 중앙 정렬, 단 chartArea를 벗어나지 않게 보정
          left = x - offset;
          left = Math.max(
            chartArea.x,
            Math.min(left, chartArea.x + chartArea.width - infoHeaderWidth)
          );
        }
        this.infoHeaderLeft = left;
      });
    },

    resetInfoHeader() {
      const infoHeaderEl = document.getElementById("info-header");
      const pressureInfoEl = document.getElementById("pressureInfo");
      const dateInfoEl = document.getElementById("dateInfo");

      infoHeaderEl.classList.remove("active");
      pressureInfoEl.innerHTML = "&nbsp;";
      dateInfoEl.textContent = "";
      this.infoHeaderLeft = null; // 위치 초기화
    },

    viewBloodPressureReport() {
      try {
        this.$router.push("/home/<USER>");
      } catch (error) {
        console.error("Navigation error:", error);
        // 대체 네비게이션 방법
        window.location.href = "/home/<USER>";
      }
    },
  },
};
</script>

<style scoped lang="scss">
.chart-container canvas {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}

.info-header-btn {
  width: fit-content;
  max-height: 45px;
  min-height: 35px;
  padding: 5px 10px;
  border-radius: 5px;
  color: #a7a7a7;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 14px;
  letter-spacing: -0.03em;
  line-height: 20px;
  position: relative;
  top: 0%;
  left: 0;
  transition: left 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;
}

.info-header-btn.active {
  border-radius: 7px;
  background: #ededed;
  color: #000;
  font-weight: 500;
  line-height: 20px;
}

.tooltip-wrapper {
  width: 100%;
  height: 0;
  padding-top: 15px;
  background: transparent;
  left: 0;
  top: 0;
  pointer-events: none;
  z-index: 2;
  letter-spacing: -5%;
}

.pressure-info {
  font-family: "GilroyBold" !important;
  padding: 0 5px;
  font-size: 18px;
  letter-spacing: -0.03em;
  line-height: 20px;
}
</style>
