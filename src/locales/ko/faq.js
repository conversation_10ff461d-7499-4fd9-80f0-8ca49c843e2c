export const faq = {
  about_cym_title: "<div>Cym<sup>702</sup>는 무엇인가요?</div>",
  about_cym:
    "Cym<sup>702</sup>는 옐로시스에서 연구 개발한 소변검사 시스템 및 맞춤형 솔루션들의 Brand Identity입니다.<br /> 옐로시스의 미션은 우리의 측정/진단 기술을 바탕으로 인류를 건강하고 행복하게 하는 것입니다.<br /> 그래서 우리는 일상속에서 누구나 건강을 쉽게 관리할 수 있도록 소변 기반 AI 건강관리 솔루션 ‘Cym<sup>702</sup>’를 만들고 있습니다.<br /> ‘Cym’는 사용자를 생각하는 옐로시스의 마음을 담은 슬로건 ‘We Care Your Moment’의 이니셜을 뜻합니다. ‘7’은 소변검사를 위한 가장 좋은 시간대 7am, ‘0’은 정기적인 소변검사를 통한 건강관리로 일상을 지키는 사이클의 반복과 선순환을, ‘2’는 우리가 추구하는 2가지 가치인 인류와 환경의 건강을 뜻합니다.",
  what_is_cym_title: "<div>Cym<sup>702</sup> Boat (Basic 5)는 무엇인가요?</div>",
  what_is_cym:
    "Cym<sup>702</sup> Boat (Basic 5)는 옐로시스 주식회사의 첫 번째 소변검사 제품으로 소변 컵이 필요 없는 종이배 형태의 편리한 검사 용기를 개발하였습니다. 기존 소변검사의 불편함을 개선한 혁신적인 개인용 소변검사 키트 입니다. <br /> 검사항목은 총 5가지로 소변 내 Blood(잠혈), Protein(단백질), Glucose(포도당), pH(산도), Ketone(케톤)을 측정하여 소변 내 혈액 존재여부와 신장 건강, 소변 내 당 함유도, 체수분 균형, 체지방 등을 확인할 수 있습니다. <br /> Cym<sup>702</sup> Boat의 결과는 모바일 앱 Cym<sup>702</sup>를 이용하여 분석하고 바로 확인이 가능합니다. 사용자는 분석 된 소변 검사 결과를 통하여 건강 항목의 결과에 대한 추이를 모니터링 할 수 있습니다.",
  where_buy_title: "<div>Cym<sup>702</sup> Boat (Basic 5)는 어디서 살 수 있나요?</div>",
  where_buy:
    "앱 하단의 구매 버튼을 클릭 하시거나 네이버 스마트 스토어(https://smartstore.naver.com/cym702)에서 구매하실 수 있습니다.",
  about_cym_results_title:
    "<div>Cym<sup>702</sup> 어플에서 제공하는 검사결과는 의학적 소견인가요?</div>",
  about_cym_results:
    "Cym<sup>702</sup> 어플은 소변 내 특정 요소 분석을 통해서, 개인 확인용으로 각 측정 항목의 단계별 결과를 즉각 판독하여, 건강을 효율적으로 관리할 수 있도록 도와줍니다. 신체의 이상 징후를 진단하는 확진검사법이 아닌 개인 확인용의 간이 검사법입니다. 주기적으로 Cym<sup>702</sup> Boat 의 소변검사를 측정 및 결과 관리등을 통해 효과적으로 건강을 유지 관리할 수 있습니다.",
  how_to_use_title: "사용방법이 궁금해요",
  how_to_use:
    "&bull;  Cym<sup>702</sup> Boat를 구입 후, Cym<sup>702</sup> 앱을 설치하세요. <br /> &bull; 앱 하단 &quot;검사&quot; 버튼 누른 후 소변검사 안내영상을 참고하시면 누구나 쉽게 검사를 진행하실 수 있어요. <br/> &bull; 앱으로 검사 결과를 쉽고 간단하게 확인할 수 있습니다.",
  do_not_use_case_title:
    "<div>Cym<sup>702</sup> Boat (Basic 5)의 검사지를 미리 뜯고 실수로 훼손시켰는데 그대로 사용해도 되나요?</div>",
  do_not_use_case:
    "정확한 검사 결과를 위해 훼손되지 않은 검사지를 사용해주세요. 훼손된 검사지를 사용하시면 검사결과가 정확하지 않을 수 있습니다. 유의 부탁드립니다.<br/> <strong>검사를 진행할 수 없는 소변검사지의 상태</strong><br /> &bull; 검사지가 구부러지거나 접혀 있는 경우 <br />&bull; 검사지의 색상이 변질되거나 물을 포함한 이물질이 묻은 경우",
  until_use_title: "<div>Cym<sup>702</sup> Boat (Basic 5)의 사용기한은 어떻게 되나요?</div>",
  until_use:
    "Cym<sup>702</sup> Boat (Basic 5) 제품의 사용기한은 제조일로 부터 24개월 이며, 제품의 하단에 표기되어 있습니다.",
  how_many_test_title: "적절한 소변검사주기는 어떻게 되나요?",
  how_many_test:
    "지병이 없는 일반적인 분들에게는 월 1회를 권장합니다. 하지만 나의 건강 상태와 몸의 변화를 지속적으로 모니터링 하시려면 일주일에 한번 주기로 사용하시는 것을 추천드립니다. 케톤과 체수분 균형 등 추가적으로 더 자세히 보시고 싶은 항목이 있다면, 원하는 만큼 더 사용하는 것을 추천드립니다.",
  how_to_store_title: "<div>Cym<sup>702</sup> Boat 보관방법을 알려주세요</div>",
  how_to_store:
    "제품 패키지를 뜯지 않은 상태에서 습기, 직사광선, 열을 피해서 2~30°C 의 건조한 곳에 보관하세요.",
  when_medication_title: "약물복용 중인데 검사가 가능한가요?",
  when_medication:
    "약물 성분이 검사에 영향을 끼칠 수 있기 때문에 정확한 검사 결과를 위하여 약물을 복용하지 않는 시기에 검사를 진행하시길 권합니다.",
  when_period_title: "생리기간에도 검사가 가능한가요?",
  when_period:
    "여성의 경우 생리 기간에는 혈액이 섞여 예기치 않은 검사 결과가 나올 수 있습니다. 정확한 검사 결과를 위해 생리 기간이 아닐 때 검사를 진행하시길 권합니다.",
  what_is_solution_title: "<div>Cym<sup>702</sup> 앱 안의 솔루션은 무엇인가요?</div>",
  what_is_solution:
    "Cym<sup>702</sup>가 개인별로 추천해드리는 식단 입니다. 제공해주신 문진 기반 건강 정보, 식생활 정보 및 소변 건강 지표 등를 바탕으로 식단 솔루션을 제공 및 고도화된 솔루션 서비스들을 제공해 드릴 예정입니다.",
  is_safe_title: "나의 데이터는 안전한가요?",
  is_safe:
    "네, 안전합니다. 옐로시스는 사용자 정보의 데이터 보안을 매우 중요하게 생각하며, Cym<sup>702</sup>앱은 사용자의 식별 를 모두 암호화하고 탈퇴 회원의 식별 데이터는 절대 남기지 않습니다. 제품 사용 중 사이버 보안 사고 발생 시 제조사 A/S로 연락을 취하십시오.",

  what_time_test_title: "첫 소변으로 검사해야하나요?",
  what_time_test:
    "첫 소변에는 여러가지 체내 주요 성분들이 농축되어 있기 때문에 가능하면 첫 소변으로 검사하시는 것을 추천합니다. 하지만 꼭 첫 소변이 아니어도 검사 가능합니다.",
  can_reuse_title: "<div>Cym<sup>702</sup> Boat(Basic 5)를 재사용 해도 되나요?</div>",
  can_reuse:
    "<div>Cym<sup>702</sup> Boat(Basic 5)의 모든 구성품은 일회용이며 재사용을 금합니다. 재사용을 하실 경우 검사 결과를 보장하지 못합니다.</div>",

  how_does_analyze_title: "<div>Cym<sup>702</sup> 앱이 어떻게 소변검사지를 분석하나요?</div>",
  how_does_analyze:
    "Cym<sup>702</sup> 앱은 높은 정확도의 머신러닝 알고리즘을 통해 사용자의 소변 검사 데이터를 분석합니다. 수많은 테스트를 통해서 확보된 데이터로 보다 정확한 결과를 제공합니다.",
  every_camera_perform_title: "휴대폰 마다 카메라의 성능과 설정이 다른데요, 결과에 상관이 없나요?",
  every_camera_perform:
    "Cym<sup>702</sup> 앱은 휴대폰 환경마다 다른 조건을 통한 많은 테스트를 진행하였습니다. 때문에 휴대폰 성능과 사양에 따른 편차까지 계산하여 안정화된 프로세싱을 통한 분석 알고리즘을 이용하기 때문에 데이터의 신뢰성을 보장합니다.",
  how_to_take_photo_title: "촬영은 어떻게 하면 되나요?",
  how_to_take_photo:
    "Cym<sup>702</sup> 앱에서 제공하는 카메라 화면의 가이드라인을 맞추어 촬영한 후 안내 사항에 따라주시면 됩니다. 검사 결과는 1초 내외로 앱에서 바로 확인하실 수 있습니다.",
  why_retake_title: "계속 재촬영 하라고 뜹니다. 어떻게 해야하나요?",
  why_retake:
    "지속적인 재촬영 메시지는 현재 장소가 너무 어둡거나 밝기 때문에 나타날 수 있습니다. 장소를 조금 이동하여 재촬영 시도를 해보거나 그럼에도 불구하고 지속될 경우 고객센터(카카오톡 채널 Cym702)에 문의해 주시면 빠르게 확인해드리겠습니다.",

  is_reliable_title:
    "검사 결과 '위험'으로 나와서 병원 진료를 다녀왔는데, 건강에 이상이 없음을 확인했습니다. 정확한 제품이 맞나요?",
  is_reliable:
    "Cym<sup>702</sup> Boat는 체외 진단 검사 시약으로 확진검사법이 아닌 간이검사법입니다. 검사 과정에서 이물질이 묻거나 검사지에 소변을 흘려보내고 1분 이전이나 1분이 지나서 촬영 한 경우, 결과에 영향을 미칠 수 있는 점 참고 부탁드립니다.",
  results_are_abnormal_title: "검사 결과가 정상이 아닌데 바로 병원에 가야 하나요?",
  results_are_abnormal:
    "검사 결과가 정상이 아니라면 검사 당일의 몸 컨디션에 따라 다를 수 있으므로 정확도를 위해 2~3일 후 새로운 제품으로 한두 번 더 검사하시기를 권합니다. 만약 다시 검사한 결과가 기존 검사와 동일하게 정상 범위 밖이면 의사의 상담을 받아 보실 것을 권합니다.",
  how_to_view_results_title: "나의 지난 결과들은 어떻게 볼 수 있나요?",
  how_to_view_results:
    "Cym<sup>702</sup> 앱 홈 화면에서 결과를 클릭 시 연결되는 History 화면에서 확인 가능합니다.",
  why_not_displayed_title:
    "검사를 완료했는데 홈 화면에 검사한 결과가 나오지 않습니다. 어떻게 해야하나요?",
  why_not_displayed:
    "앱을 완전히 종료한 뒤, 재실행 해주세요. 그럼에도 불구하고 결과가 나오지 않는다면 고객센터(카카오톡 채널 Cym702)에 문의해 주세요.",
  why_results_not_shown_title:
    "정상적으로 가이드에 맞춰 촬영을 진행했으나, 결과가 나오지 않습니다.",
  why_results_not_shown:
    "Cym<sup>702</sup> 앱은 휴대폰이 인터넷에 연결 된 상태에서 사용이 가능합니다. 해당 문제는 네트워크 불안정으로 인한 일시적인 장애일 수 있습니다. 휴대폰의 인터넷 상태 다시 점검 후 촬영을 진행해 주세요. 이후에도 지속적으로 문제 발생 시 고객센터에 문의해 주세요.",
  is_available_other_categories_title: "현재 5종 말고 다른 건강 상태도 확인하고 싶어요.",
  is_available_other_categories:
    "현재의 5종 검사 이외에 다른 검사 항목도 개발이 진행되고 있습니다. 불편하시겠지만 조금만 기다려 주세요!",
};
