{"permission_notice": "We inform you of the app’s access rights.", "permission_notice2": "Cym<sup>702</sup> only requests access to functions necessary to provide services and to comply with the Information and Communication Network Act.", "perm_record": "Device and App History (Required)", "perm_record_description": "To optimize services and check for errors while using the app.", "perm_location": "Location (Required)", "perm_location_description": "Use location while using the app", "perm_healthKit": "HealthKit (Optional)", "perm_healthKit_permission": "Providing customized solutions using health data", "perm_camera": "Camera (Optional)", "perm_camera_description": "To take photos of urine test strips", "perm_media": "Photo/media/file (Optional)", "perm_media_description": "To save image on device, and send image to server to calculate results", "perm_noti": "Notification (Optional)", "perm_noti_description": "To register for and receive app notifications", "perm_ble": "Bluetooth (Optional)", "perm_ble_description": "App sends to and collects data from urine testing device", "permission_description": "We only obtain consent if access is required to provide services. You may still use our services without providing consent to some fields. You can change the allowed status at any time from your mobile phone settings.", "next_btn": "NEXT", "image_success_modal": "Your profile image has been <br/> successfully changed!", "image_error_modal": "Image upload failed. <br /> Please try again.", "auth_error_modal": "Authentication failed.<br/> Please check the entered information again.", "valid_error_modal": "Please fill in the correct form.", "delete_sub_modal": "Your urine test data and health records will be deleted.<br/>Are you sure you want to delete?", "success_delete_sub_modal": "User deletion is complete.", "error_delete_sub_modal": "User deletion failed. <br/> Please try again.", "edit_birth": "Edit Birth", "change_birth_success": "✏️ Your date of birth has been <br/> successfully changed!", "change_birth_error": "Change failed. <br/> Please check what you entered.", "change_relationship_success": "✏️  Your date of relationship has been <br/> successfully changed!", "intro_title_1": "AI Urine Test", "intro_mid_1": "We analyze urine tests immediately and provide easily comprehensible results", "intro_title_2": "My health trend <br/> at a glance", "intro_mid_2": "We convert the test results to numerical scores and show you your health trend.", "intro_title_3": "Daily Health Records", "intro_mid_3": "You can record your weight, hydration, urination frequency everyday", "confirm": "Get started", "input_id": "ID", "input_id_placeholder": "5 ~ 10 characters contain Number and English letters", "no_spaces": "Please enter without spaces.", "input_pwd": "Password", "login": "Log in", "login_error_msg": "🥺 <PERSON><PERSON> failed.<br/>Please check your ID and password.", "session_error_msg": "Your session has expired.<br/>Please log in again.", "join_btn": "Sign Up", "find_account_btn": "Find Account", "err_check_id": "Please check your ID.", "err_check_pwd": "Please check your password.", "kakao_login": "<PERSON><PERSON><PERSON>", "google_login": "Google Login", "recent_login": "recent", "kakao": "kakao", "google": "google", "apple": "apple", "general_account": "General Account", "cur_login": "(Account currently logged in)", "alreay_exist": "The account has already<br/>been registered or linked.", "success_content": "Urination frequency added", "comment_phone_cert_title": "Please verify yourself", "comment_phone_cert_one": "Start your AI Helathcare with <PERSON><PERSON><sup>702</sup>! <br/> Your personal information <br/> will be kept safely🔒", "comment_phone_cert_two": "Your personal information will be kept safely", "country_code": "Country", "input_phone": "Phone number", "cur_phone": "Current phone", "cur_pwd": "Current password", "send_btn": "Send verification number", "verification_number": "Verification number", "request_btn": "Request again", "invalid_error": "Invalid verification number.", "error_modal_msg": "🥺 Incorrect verification number. <br/> Please try again.", "invalid_number": "Please check the information you entered again.", "error_send_msg": "Message sending failed.", "send_text_modal_msg": "📱 We've sent the verification number. <br/> If you do not receive a text message, please check if the information you entered is correct.", "error_timeout": "After the authentication code input time expires, it moves to the main screen.", "error_dismatch": "No matching account found.<br/> Please double check the information you entered.", "success_authentication": "✅ Verified!<br/>Please reset your password.", "success_change_password": "✅ Changed successfully!<br/>Please log in with the changed information.", "join_id_title": "Enter your ID", "join_id_subtitle": "Welcome! 🙌🏻 <br/> Please select a ID.", "join_id_error": "ID should be between 5 to 20 characters in English or numbers.", "join_id_placeholder": "between 5 to 20 characters in English or numbers.", "join_id_invalid": "The ID is already in use.", "join_pwd_title": "Enter your password.", "join_pwd_subtitle": "Passwords must be a combination of English, numbers, and special characters, between 8 to 16 characters.", "join_pwd_error": "Please enter at least 8 characters in English, numbers, and special characters.", "join_pwd_placeholder": "Please enter between 8 to 16 characters in Korean or English.", "join_pwd_check": "Confirm password (must match above)", "join_pwd_invalid": "Password does not match. ", "join_terms_title": "Please accept the terms and conditions.", "join_terms_subtitle": "We're almost there! <br/> Start your AI health care with <PERSON>m<sup>702</sup>", "terms_agreement": "Terms and Conditions", "terms_all_agree": "Agree to all", "terms_service": "[Required] Terms and conditions of service", "terms_service_contents": "<h2>Terms and conditions of use</h2><br/> <b>Article 1 (Purpose)</b><br/>These terms and conditions are referred to as 'Yellosis, Inc.' (hereinafter referred to as 'Company').The  purpose is to stipulate the rights,  obligations and  responsibilities of the  company and  service users for  the use of  services  provided by ) through  websites and mobile  devices, and  other necessary matters. <br/><br/> <b>Article 2 (Definition of Terminology)</b><br/> 1. The terms used in this agreement are as follows.<br/>① The term 'company' means a business operator that provides services through a website or mobile device.<br/>② The term 'member' (or 'user') means a person who enters into a contract of use in accordance with these terms and conditions and uses the services provided by the company.<br/> ③ The term 'membership account' refers to a combination of unique characters and numbers given to each member for identification of members and use of members' services. <br/> ④ The term 'password'  refers to a combination of  letters and numbers selected by  members to confirm that they are members who are consistent with their member accounts and protect the rights and interests of members. <br/> ⑤ The term 'use contract' is a generic term for contracts concluded between the company and its members in relation to the use of services. <br/> ⑥ The term 'cancellation' refers to  the termination of the contract of  use between the company and its  members. <br/> ⑦ 'Temporary member' means a person who provides only some information and uses only a part of the service provided by the company. <br/> ⑧ Mobile devices are devices that can be used by downloading or installing content, and refer to mobile phones, smartphones, portable information terminals (PDAs), tablets, etc. <br/> ⑨ The term 'personal information' (or 'account information')  refers to  information  provided by  members to the  company, such as member's membership number, external account information, device information, nicknames, profile photos, friend list, and usage fee payment  information. <br/> ⑩ 'Application' means any program downloaded or installed through a mobile device to use the service provided by the company. <br/> The definition of terms used in these Terms and Conditions shall be as prescribed by relevant laws and policies for each service, except as prescribed in paragraph 1 of this Article, and it shall be in accordance with general commercial practice.<br/><br/> <b>Article 3 (Effect and modification of terms and conditions)</b><br/> 1. The company  will publish the terms and conditions within the service or in a similar manner so that the members can know them. <br/> 2. The contract of use is concluded by a person who wants to become a member (hereinafter referred to as 'applicant') agreeing to the terms and conditions, applying for service use, and the company agreeing to the application. <br/> 3. In principle, the company accepts the application of the applicant for membership. However, the company may refuse to accept any of the following applications. <br/> ① Where the contents of the application for use are falsely stated or the requirements for application for use are not met.<br/> ② If the service is used in an abnormal or indirect manner in a country where the company has not provided the service. <br/> ③ Where an application is filed for the purpose of hindering the well-being and order of society or customs and customs and customs<br/> ④ In the case of using a game service for illegal purposes, <br/> ⑤ In other cases where consent is deemed inappropriate for reasons equivalent to each subparagraph. <br/> 4. If there is a reasonable reason, the company may revise the terms and conditions to the extent that they do not violate the relevant laws, and if the company revises the terms and conditions, the date of application, details, and reasons for revision shall  be  specified and notified to members. However, if  the change is unfavorable to the  member or is a change in a  serious matter, it shall  be notified in the same  manner as the  text 30 days  before the  application date and notified to the member in the manner of Article 5. <br/> 5. If the company revises the terms and conditions, it will check whether the members agree to the application of the revised terms and conditions after announcing the revised terms and conditions. In the event of notice or notice under paragraph 4 of this Article, the Company shall also notify or notify that the Member has agreed to the amended Terms and Conditions, and if the Member does not express his intention to reject them by the effective date of the Terms and Conditions. If a member disagrees with the revised terms and conditions, the company or member may terminate the contract. <br/> 6. The company will take measures to allow members to inquire and respond to the contents of the terms and conditions with the company. <br/> 7. The Company may amend these Terms and Conditions to the extent that they do not violate relevant laws such as the Electronic Commerce Act, the Terms and Conditions Regulation Act, the Information and Communication Network Utilization and Information Protection Act, and the Content Industry Promotion Act. <br/><br/> <b>Article 4 (Rules outside the Terms and Conditions)</b> <br/>  Matters not specified in this Agreement and interpretation of this Agreement shall be governed by other relevant laws and commercial practices, such as the Commercial Act, theTelecommunications Business Act, the Electronic Commerce Act, and the Copyright Act. <br/><br/> <b>Article 5 (Notification to Members)</b><br/> 1. Companies can notify or  notify members by  using appropriate communication methods such  as e-mail, text messages, and  application notifications. <br/> 2. In the case of notification to an unspecified number of members who are virtually impossible to notify under paragraph 1 of this Article, the company may replace individual notification by posting the notification on the website or mobile bulletin board for 20 days.<br/><br/> <b>Article 6 (Conclusion of a use contract and membership registration)</b> <br/> 1. A person who intends to register as a member and use the service is deemed to have  agreed to this  agreement by  selecting 'Agreed' to this agreement notified by the company through the service screen. <br/> 2. Members can use the company's services from the moment they sign up. <br/> 3. Members can download and install dedicated applications (or programs) from their websites or mobile devices to use the company's services. <br/> 4. Members who wish to register as members and use applications dedicated to sites or mobile devices must provide the necessary information requested by the company. Members are obligated to amend the information in accordance with the procedures set by the company in the event of a change in information. The company is not responsible for any problems caused by members not changing information (including problems caused by the company's inability to notify users). <br/> 5. Members who do not register their true information, such as stealing other people's information or registering false information, may not claim any rights in relation to the use of websites and mobile device applications and may be punished according to relevant laws. <br/> 6. Members are responsible for managing member accounts and passwords. The member is responsible for any damage to the use of the service caused by negligence or improper use by a third party, and the company is not responsible for it. <br/><br/> <b>Article 7 (Personal Information Processing Policy)</b><br/> 1. The company does  not have a  password for  the personal information provided when signing  up for a website or  mobile device application, and the  relevant part  is in accordance with the privacy policy of the website or mobile device application.<br/> 2. Regarding the protection of personal information, the company strives to protect the personal information of members, including member information, as prescribed by relevant laws and regulations.<br/> 3. The company does not disclose or distribute the personal information of the members it has learned about the provision of services to third parties without their consent. However, exceptions are made to cases where a request is received from a related agency for investigative purposes under related laws or by legal procedures under the provisions of the law, such as a request from the competent authority.<br/><br/> <b>Article 8 (Delete personal data)</b><br/> 1. When a member deletes a mobile application, the data stored on the server may not be  deleted. <br/> 2. If a member withdraws from the company service, even the data stored on the server will be deleted. However, in this case, even if a member re-enrolls, the existing data cannot be restored. <br/><br/> <b>Article 9 (Company's Obligations)</b><br/> 1. If complaints from members are justified, the company will deal with them as soon as possible. However, if it is difficult to process quickly, we will notify the members of the reason and schedule.<br/> 2. In order to provide continuous and stable service, the company will repair or repair the equipment without delay in the event of a failure or loss. However, the service may be suspended  or permanently suspended in case of a natural disaster, emergency, or other unavoidable  circumstances.<br/><br/> <b>Article 10 (Member's Obligations)</b><br/> 1. Members shall  comply with  the company's announcements, such as the matters stipulated in these Terms and Conditions, company regulations, announcements,  and operating policies, and shall not interfere with other company's business or  damage the  company's reputation. <br/> 2. A member  cannot transfer or donate the right to use the service or other status under the  contract of  use to  another person without the company's explicit consent , and cannot  provide it as  collateral.<br/> 3. Members must pay considerable attention to managing IDs and passwords and cannot provide IDs to third parties without the  consent of the company.<br/> 4. Members shall not infringe on the intellectual  property rights of the  Company and any  third party.<br/> 5. Members shall not transmit or post information that is prohibited from being transmitted or posted by relevant laws and regulations.<br/> 6. Members shall not collect, store, or disclose other members' personal information.<br/> 7. Members shall not violate other relevant laws and regulations or engage in acts contrary to public order and good customs. <br/> 8. Members are responsible for managing their accounts and mobile devices. As a result, the company is not responsible for any damages.<br/><br/> <b>Article 11 (Company Exemption)</b><br/> 1. The  company is exempt from liability  for damages caused by members not earning the expected profits from the company's service provision or by choosing or using service materials.<br/> The Company shall be exempted from liability for  any damage caused to its members due to service-based and telecommunications service failures provided by other carriers and shall comply with the terms  and conditions of use of the Site or Mobile Application.<br/> 3. The company is not responsible for  any data stored, posted, or  transmitted by  its members.<br/> 4. The company is not responsible for any failure to use the service due to reasons attributable to the members. <br/> 5. The company is not liable for any damages incurred to its members in connection with the use of free services. <br/> 6. The Company shall not be  liable for  any activities  (including  data transfer  and other community activities) between its members or between its members and third parties, or within or outside of this Service. <br/> 7. The company is not responsible for  the  authenticity , reliability, and  accuracy of  materials posted or transmitted by members. <br/> 8. The company is not responsible for any damages arising therefrom in the  event of goods transactions, etc. between members or between members and third parties through services. <br/> 9. The company is not responsible for any disputes between members or between members and a third party without reasons attributable to the company. <br/> 10. The company is not responsible for any system failure that  may occur intentionally or  unintentionally in the  process of  managing actual  costs such as  servers, repairing,  replacing, or operating  software, system failure due to  third-party attacks,  computer viruses that are not developed by renowned  domestic or  security  companies. <br/><br/> <b>supplementary provisions</b><br/>These terms and conditions will take effect on April 1, 2021.", "terms_personal_info": "[Required] Collection and use of personal information", "terms_personal_info_contents": "<h2>Personal information processing policy</h2><br/>'Yellosis, Inc.' (hereinafter referred to as ‘company’) establishes and discloses personal information processing guidelines as follows to protect the personal information of the data subject and to quickly and smoothly handle related grievances in accordance with Article 30 of the Personal Information Protection Act. <br/><br/> <b>Article 1 (Purpose of processing personal information)</b><br/>The company processes personal information for the following purposes: The personal information being processed will not be used for any purpose other than the following purposes, and if the purpose of use is changed, necessary measures will be implemented, such as obtaining separate consent in accordance with Article 18 of the Personal Information Protection Act.<br/> 1. Subscribe and  manage web  pages or  mobile device applications (hereinafter referred to as  'app') <br/> Personal information is processed for the purpose of confirming the  intention to sign up, identifying and authenticating  oneself according to the provision of membership services, maintaining and managing membership qualifications, preventing illegal use of service, and confirming the consent of legal representatives. <br/> 2. provision of goods or services <br/> Personal information  is processed for the purpose of providing content, providing customized services, identification, age authentication, delivery of goods, and service provision.<br/> 3. complaint handling <br/> Personal information is processed for the purpose of identifying members, checking complaints, contacting and notifying facts, and notifying the results of the processing.<br/> 4. application to marketing and advertising<br/> Personal information  is processed for the purpose of developing new services (products) and providing customized services, providing events and advertising information and opportunities to participate, identifying frequency of access, or statistics on members' use of services.<br/><br/> <b> Article 2 (Processing and retention period of personal information)</b><br/> 1. The company processes and holds personal information within the period of retention and use of personal information in accordance with laws and regulations or the period of retention and use of personal information agreed upon by the data subject. <br/> 2. The  processing and  retention period of each  personal information is as  follows. <br/> ① Subscribe and manage web pages or mobile apps: Until you leave the  membership <br/> However, in the case of the following reasons, until the end of the relevant reason,<br/> 1) If an investigation, investigation, etc. is underway due to violation of relevant laws and regulations, until the relevant investigation and investigation are completed.<br/> 2) If the bond/debt relationship remains due to the use of web pages and mobile apps, until the settlement of the bond/debt relationship is completed. <br/> ② Provision of Goods or Services: Until the supply of goods and services is completed and the payment and settlement of fees are completed.<br/> However, if it falls under the following reasons, until the end of the period, <br/> 1) Records of transactions such as labeling, advertising, contract details, and implementation  under the  Act on Consumer Protection in Electronic Commerce, etc. <br/> - Record of display and advertising: 6 months <br/> -Contract or withdrawal of subscription, payment, goods, etc. Supply record: 5  years<br/> - Records of Consumer Complaints or Disputes: 3  years<br/> 1) Storage of communication fact confirmation data pursuant to Article 41 of the Communications Secret Protection Act <br/> - Computer communication, Internet log data, destination tracking data: 3 months<br/><br/><b>Article 3 (Provision of personal information to third parties)</b><br/>The company processes the personal information of the data subject only within the scope specified in Article 1 (Personal Information Processing Purpose), and provides the personal information to a third party only if it falls under Article 17 of the Personal Information Protection Act.<br/><br/> <b>Article 4 (Rights and obligations of the  data subject  and legal representative, and the  method of exercising the  same)</b><br/>The data subject may exercise the following personal information protection rights against the company at any time.<br/> ①request for access to personal information<br/>② Request for correction in case of errors, etc.<br/>③ deletion request<br/>④ request for suspension of processing<br/> 2. The exercise of rights under paragraph (1) can be made to the company in writing, by phone, e-mail, or fax, and the company will take action without delay. <br/> 2. The exercise of rights under paragraph (1) may be made through an agent, such as a legal representative of the information subject or a person delegated. In this case, you must submit a power of attorney in accordance with attached Form 11 of the Enforcement Rules of the Personal Information Protection Act. <br/> 3. The rights of the data subject may be restricted in accordance with Articles 35 (5) and 37 (2)  of the  Personal Information Protection  Act. <br/>4. Request for correction and deletion of personal information cannot be requested if the personal information is specified as the subject of collection in other laws.<br/> 5. The company confirms whether the person who made the request, such as request for access, correction or deletion, or request for suspension of processing, is the person who made the request or a legitimate agent.<br/> 6. The information subject shall not infringe on the personal information and privacy of the person or other person handled by the company in violation of related laws such as the Personal Information Protection Act. <br/><br/> <b>Article 5 (Processing Personal Information Items)</b><br/>The company handles the following personal information items: <br/>1. Subscribe and  manage web pages or mobile  apps<br/> Name, gender, email address, login ID, date of birth, password, address, mobile number, height, weight, service history, access log, cookies, access IP information<br/>2. provision of goods or services<br/> Payment history and past purchase history<br/> 3. The following personal information items may be automatically generated and collected during the Internet service usage process. <br/>IP address, cookies, MAC address, service usage record, visit record, defect usage record, etc.<br/><br/><b>Article 6 (Destruction of Personal Information)</b><br/>When personal information becomes unnecessary, such as the expiration of the period of personal information retention or the achievement of the purpose of processing, the company destroys the personal information without delay. If the period of personal information retention agreed by the data subject has elapsed or the purpose of processing has been achieved, the personal information shall be moved to a separate database (DB) or stored in a different storage location. <br/>The procedure, deadline, and method of destruction are as follows.<br/>1. destruction procedure<br/>The information entered by the user is transferred to a separate DB after achieving the purpose (a separate document in the case of paper) and stored for a certain period of time or immediately destroyed according to internal policies and other related laws. At this  time, the  personal information  transferred to DB will not  be used for any other  purpose except by  law. <br/>2. method of destruction<br/>The personal information printed on the paper document is destroyed  by crushing or  incinerating it with a shredder.<br/><br/><b>Article 7 (Measures to ensure the safety of personal information)</b><br/>The company is taking the following measures to ensure the safety of personal information. <br/>1. Administrative Measures: Establishment and implementation of internal management plans, regular employee training, etc.<br/>2. Technical measures: Management of access rights such as personal information processing systems, installation of access control systems, encryption of unique identification information, and installation of security programs.<br/>3. Physical measures: Access control in computer rooms, data storage rooms, etc.<br/><br/><b>Article 8 (Matters concerning the installation, operation, and rejection of an automatic personal information collection device)</b><br/>The company  uses cookies, which store usage information and frequently recall them, to provide individual customized services to users.<br/>Cookies are a small amount of information that the server (http) used to run the website sends to the user's computer browser and are sometimes stored on the hard disk in the user's PC computer.<br/>1. Purpose of use of cookies: It is used to provide users with optimized information by identifying the type of visits and use of each service and website visited by users, popular search terms, security access, etc.<br/>2. Installation, operation, and rejection of cookies : You can  refuse to  save cookies by  setting the Tools >  Internet Options > Privacy Menu options at  the top of your  browser.<br/>3. Refusing to save cookies can cause difficulties in using customized services.<br/><br/><b>Article 9 (Personal Information Protection Officer)</b>The company is in charge of the processing of personal information, and designates a personal information protection manager as follows for the handling of complaints and damage relief of information subjects related to personal information processing.<br/>&bull; personal information protection officer<br/>Name: Lee Jong-gun<br/>Position: Technical Director<br/>Contact number: <***********>, <<EMAIL>><br/>Information subjects can contact the person in charge and the department in charge of personal information protection for all personal information protection inquiries, complaint handling, damage relief, etc. that occurred while using the company's service (or business). The company will respond and process the information subject's inquiries without delay.<br/><br/><b>Article 10 (How to remedy infringement of rights)</b><br/>Information subjects can contact the following organizations for damage relief and counseling for personal information infringement.<br/><The following organizations are separate from the company, so please contact us if you are not satisfied with the company's own handling of personal information complaints and damage relief results, or if you need further help.><br/>&bull; Personal Information Infringement Reporting Center (Operated by the Korea Internet & Security Agency): Report personal information infringement, apply for counseling<br/> - Home page: privacy.kisa.or.kr<br/> - Telephone : 118 (without country code)<br/>&bull; Personal Information Dispute Mediation Committee: Application for Personal Information Dispute Mediation, Collective Dispute Mediation <br/> - Website: www. kopico.go.kr<br/>&bull; Telephone : (without country code) 1833-6972<br/>&bull; Cyber Investigation Division, Supreme Prosecutors' Office: 1301 (www.spo.go.kr)<br/>&bull; National Police Agency Cyber Security Bureau : 182 (ecrm.cyber.go.kr/minwon/main)<br/><br/><b>Article 11 (Change of Privacy Policy)</b><br/>This personal information processing policy will take effect from April 1, 2021.<br/>", "terms_sensitive": "[Required] Collection and use of sensitive information", "terms_sensitive_contents": "<h2>Sensitive information processing policy</h2><br/>Yellosis, Inc. (hereinafter referred to as ‘company’) intends to process sensitive information of members to provide services such as health information and diet information.<br/>Please fully read the notice on the collection and use of the following sensitive information before agreeing.<br/><br/><b>Collection and use of sensitive information</b><br/>&bull; Providing health care services such as health information and diet information<br/><br/> <b>Item of sensitive information</b><br/>&bull; Height, weight, eating habits, chronic diseases, drinking, smoking, exercise habits, etc.<br/>&bull;Results of items tested with products provided by the company<br/><br/><b>Retention and use period of sensitive information</b><br/>&bull; from the date of consent to the provision of information to the time of withdrawal of membership<br/><br/> ※ You may refuse the above consent. However, if you do not agree, the provision and use of services provided by the company may be restricted.<br/>These terms and conditions will take effect on April 1, 2021.", "terms_marketing": "[Optional] Use information for marketing", "terms_marketing_contents": "<h2>Consent to receiving marketing information</h2><br/>According to Article 22.4 of the Personal Information Protection Act, you can use the service without having to write down the optional information.<br/><br/><b>purpose of personal information processing</b><br/>Use it for marketing and advertising.<br/>&bull; Develop new features and provide customized services<br/>&bull; Send newsletters to inform you of new features (products)<br/>&bull; providing event and advertising information<br/>&bull; Provision of services and advertising based on demographic characteristics<br/>&bull; validation of service<br/>&bull; Statistics on frequency of access or use of services by members, etc.<br/> ※ However, the information content that must be guided is provided regardless of whether you agree to receive it.<br/><br/><b>transmission method</b><br/>In operating the service, various information is provided to members through service screens, mobile app push, mobile text message (SMS), e-mail,  mail, etc.<br/><br/><b>withdrawal guidance</b><br/>Customers may withdraw their consent upon consent, and may not receive marketing information such as discounts, events, and  user-customized product recommendations, although they may use the basic services provided by the company without consent.<br/><br/>These terms and conditions will take effect on April 1, 2021.", "read_more": "Read more >", "profile_subtitle": "To provide personalized AI healthcare solutions, we need to know a bit about you.", "profile_sex": "Sex *", "male": "Male", "female": "Female", "profile_name": "Nickname", "profile_nickname": "Nickname *", "edit": "edit", "name_placeholder": "Please use Korean, English, or numbers within 20 characters", "profile_birth": "Date of birth (8 digits) *", "profile_birth_description": "It will be used for account recovery, so please enter it correctly", "birth_year_placeholder": "Year (4 digits)", "birth_month_placeholder": "Month (2 digits)", "birth_day_placeholder": "Day (2 digits)", "profile_height": "Height (cm) *", "profile_description": "Please fill in the correct information to receive a customized solution.", "profile_weight": "Current Weight (kg) *", "target_weight": "Target Weight (kg)", "target_water": "Target Hydration(ml)", "target_water_description": "It is the recommended water intake based on your height, weight, and sex.", "invalid": "Not Valid", "purpose_answer": "What do you hope to achieve?", "disease_prevention_and_management": "Disease prevention and management", "exercising": "Maintain health through exercise", "eating_healthy": "Eat healthy", "weight_loss": "Lose weight", "ketone_management": "Manage ketone levels", "duplicate_check": "Multiple checks are possible.", "single_check": "Please select one.", "input_etc": "Other (please specify)", "input_nickname": "2 to 10 characters in English or numbers", "input_nickname_placeholder": "At least 2 to 20 characters in Korean or English or numbers", "etc_placeholder": "Please enter between 5 to 50 characters in Korean or English.", "select_modal": "Would you like to switch to", "success_add_user": "➕ User successfully added!", "error_add_user": "Adding user failed. <br/> Please double check what you entered.", "adding_impossible": "You can add up <br/> to 3 users.", "etc": "etc", "exercise_answer": "How much do you exercise?", "upper": "A lot (more than 5 days a week, over an hour on average)", "middle": "Moderately (3 to 4 days a week, 40 mins to an hour each time)", "lower": "Minimally (1 to 2 days of light walking)", "little_excercise": "Never", "chronic_answer": "Do you have a chronic illness?", "none": "none", "no_disease": "No", "kidney_disease": "Kidney disease", "liver_disease": "Liver disease", "diabetes": "Diabetes mellitus", "ketone_mode": "Selection of results display criteria for ketone items", "ketone_mode_desc": "Please choose only one. You can change the criteria within the app.", "is_ketone_mode": "I'm not Diabetes/gestational diabetes.", "is_ketone_mode_desc": "Let's take a look at the general ketone result interpretation.", "non_ketone_mode": "Diabetes/gestational diabetes.", "non_ketone_mode_desc": "Let's look at the interpretation of ketone results for diabetic/gestational diabetics.", "no_ketosis": "Diabetes", "abb_diabetes": "Diabetes", "is_ketosis": "Ketosis", "set_ketone_mode": "Go to Set the Ketone Mode", "is_diabetes": "When selected, the interpretation of the ketone entry in urine test will be displayed for diabetes/gestational diabetes.", "diabetes_guide_desc": "We are currently interpreting the results as for <strong>‘diabetes/gestational diabetes’.</strong> You can change it to for <strong>‘ketosis’</strong>", "diabetes_guide_img": "en_ketone_guide", "ketosis_guide_desc": "We are currently interpreting the results as for <strong>‘ketosis’.</strong> You can change it to for <strong>‘diabetes/gestational diabetes’</strong>", "ketosis_guide_img": "en_ketosis_guide", "gestational_diabetes": "Gestational diabetes", "no_diabetes": "When selected, the interpretation of the ketone entry in urine test will be displayed for ketosis confirmation. ", "gout": "Gout", "high_cholesterol": "High cholestero", "hypertension": "High blood pressure", "hypotension": "Low blood pressure", "urinary_tract_infection": "Urinary tract infection", "drinking_answer": "How often do you drink alcohol?", "drink_four": "4+ times a week", "drink_two": "2 to 3 times a week", "drink_one": "Once a week", "non_drinking": "I don't drink", "smoking_answer": "How often do you smoke?", "a_pack": "More than a pack a day", "smoke_ten": "More than 10 cigarettes a day ", "smoke_five": "5-10 cigarettes a day", "smoke_less": "Less than 5 cigarettes a day", "non_smoking": "I don't smoke", "diet_answer": "What is your usual diet?", "general_diet": "Nothing specific", "single_desc": "(Select 1)", "multi_desc": "(Multiple selection)", "calorie_controlled_diet": "💪🏻 Calorie-controlled diet", "low_carb_high_fat": "Low-carb, High-fat diet(Ketosis)", "low_carb": "Low-carb diet", "low_calorie": "Low-calorie diet", "none_diet": "Not selected", "vegetarianism": "🥗 Vegetarian diet(Select 1)", "pesco": "Pesco vegetarian", "lacto_ovo": "Lacto-ovo vegetarian", "vegan": "Vegan", "none_vegetarian": "Not selected", "care_diet": "🌾 Care diet", "low_sodium": "Low-sodium diet", "low_sugar": "Low-sugar diet", "low_protein": "Low-protein diet", "high_protein": "High-protein diet", "none_care": "Not selected", "diet_input_etc": "👂🏻 Other", "diet_input_etc_desc": "(Please specify)", "diet_input_etc_placeholder": "Please enter between 5 to 50 0 characters in Korean or English.", "start_btn": "Start Healthcare", "join_modal_message": "🎉Congratulations on signing up! <br/><br/> After logging in, a survey is required for personalized health care🙏🏻", "need_survey_message": "We need survey for urinalysis and custom solutions.<br/>Please give me a minute!😊", "go_to_survey": "Go to survey", "go_to_login": "Go to Login", "survey_modal_message": "🎉 Congrats! <br/><br/> Start your AI health care with <PERSON>m<sup>702</sup>", "join_error_message": "🥺 Member registration failed. <br/> Please double check the information you entered.", "over_request": "Too many requests.😵‍💫<br>Please try again later.", "over_ten": "You can select up to 10.", "comment_findaccount_title": "Find Account", "comment_findaccount_tab_1": "Find ID", "comment_findaccount_tab_2": "Find Password", "cym_account": "ID", "comment_subtitle_1": "You can find <PERSON><PERSON><sup>702</sup> account with the 'mobile phone number' you used when signing up.", "comment_subtitle_2": "You can reset your password with the ‘mobile phone number’ and ‘date of birth’ you used when signing up.", "birth": "birth", "phone_error": "Only numbers can be entered.", "account_searched": "This is an account registered with the corresponding phone number.", "account_searched_no_result": "There is no information registered with that mobile phone number.", "login_with_id": "Log in with ID", "go_to_join": "Sign Up", "is_changed_phonenumber": "Have you ever changed your phone number?", "first_tutorial_title": "Cym702 Score", "first_tutorial_content": "Health scores are created based on the overall result of your urine test", "second_tutorial_title": "Urine test results", "second_tutorial_content": "Your results are summarized and visualized. Click on the icons for a detailed report.", "third_tutorial_title": "Weight, Hydration, Urination", "third_tutorial_content": "Scroll down to record today's weight, water consumption, and urinary frequency.", "cym_702": "Cym702", "blood": "B   L   D", "glucose": "G  L   U", "protein": "P  R  O", "ph": "   p H", "ketone": "K   E   T", "name": "", "try_test": "Try test!", "main_negative_txt": "NEG", "main_positive_txt": "POS", "main_normal_level": "Good", "main_warning_level": "Caution", "main_exertion_level": "Try", "main_enter_level": "Enter", "main_caution_level": "Warning", "main_danger_level": "Danger", "main_good_level": "Great", "write": "Record below", "nav_home": "HOME", "nav_solution": "SOLUTION", "nav_test": "TEST", "nav_shop": "SHOP", "nav_my": "MY", "need_update": "Please update to the new version", "success_analysis": "🔬 Analyzed successfully!", "health_score": "Cym<sup>702</sup> Health Score", "portable_modal_message": "Gutter is<br/>automatically cleaned.", "go_to_result": "Go to results", "cym_no_data": "No data available", "cym_detail_link": "'s <PERSON><PERSON>", "exam_result_title": "Test Results", "do_not_reuse_boat": "Reusing Cym<sup>702</sup> boat<br/>gives inaccurate results.", "only_main": "for Main Account", "current_no_service": "Coming Soon", "go_result_popup": "Click for Results", "times": "times", "time": "time", "tab_cym": "Cym", "tab_blood": "BLD", "tab_ph": "pH", "tab_protein": "PRO", "tab_glucose": "GLU", "tab_ketone": "KET", "ketone_status": "ketone", "test_count": "Avg of", "score": "tests:", "avg_score": "", "avg_year": ".", "avg_month": "", "average": "Avg of", "year": "year", "month": ".", "day": "", "cym702_score_info_title": "What is <PERSON><PERSON><sup>702</sup> health score?", "cym702_score_info_content": "We numerically evaluate your health status based on your urine test results.", "good": "Great", "normal": "Good", "ketone_normal": "Good", "caution": "Warning", "danger": "Danger", "warning": "Caution", "negative": "Negative", "negative_minus": "Negative(-)", "negative_plus_minus": "Negative(+/-)", "positive": "Positive", "positive_plus_minus": "Positive(+/-)", "positive_plus": "Positive(+)", "positive_double_plus": "Positive(++)", "positive_triple_plus": "Positive(+++)", "positive_quadruple_plus": "Positive(++++)", "cym_score": "Cym Score", "users": "'s state of", "status": "", "no_data": "No data available. Please begin testing.", "status_description": "The result of this analysis is for your reference only and can't be used for any clinical diagnosis of any conditions.", "font_size": "A", "what_is_hematuria": "What is blood in urine?", "hematuria": "blood", "blood_info": "Blood means that there are red blood cells in urine. The blood inside urine can come from anywhere in the urinary system, from the kidney to urethra. <br/><br/> The most commonly tested blood is 'gross' or 'visible' blood. However, not all red urine indicates blood. Some medications cause urine to turn red. Therefore, if your urine turns red, you should first think about the medicine or food you have eaten. <br/><br/> Since even healthy individuals may experience blood from intense exercise, stress, high fever, and menstruation, it needs to be tested multiple times. For females, we advise avoiding testing before and after menstruation to ensure the test's accuracy.", "blood_good_level": "Your test result for occult blood is negative.😄 No blood was detected in your urine. You seem to be maintaining a healthy lifestyle! Remember to regularly test for blood in urine, and check out the health guide below for more information.", "blood_warning_level": "Your test result for occult blood is positive(+), so be alert. We detected 10 RBC/μL or more blood within your urine.🤔 This can be caused temporarily by intense exercise, stress, high fever, and menstruation, which is not an issue. <br/><br/> However, the presence of blood in urine may also be due to kidney and urethral issues. So, we recommend testing regularly to check for trends. Click on the health guide below for more information.", "blood_caution_level": "Your test result for occult blood is positive(++), so regular care is needed. We detected 50 RBC/μL or more blood in your urine.🤔 This can be caused temporarily by intense exercise, stress, high fever, and menstruation, which is not an issue. <br/><br/> However, the presence of blood in urine may also be due to kidney and urethral issues. We recommend you to test 2-3 more times to check if the symptom persists, and to see a healthcare professional if you continue to receive ‘Positive(++)’. Click on the health guide below for more information.", "blood_danger_level": "Your test result for occult blood is positive(+++), so regular care is needed. We detected 250 RBC/μL or more blood in your urine.🤔 This can be caused temporarily by intense exercise, stress, high fever, and menstruation, which is not an issue. <br/><br/> However, the presence of blood in urine may also be due to kidney and urethral issues. Test 2-3 more times to check if the symptom persists, and see a healthcare professional if you continue to receive ‘Positive(+++)’. Click on the health guide below for more information.", "blood_negative_level_modal": "No blood is detected in your urine. You seem to be maintaining a healthy lifestyle! <br/><br/> Blood in urine is an important indicator of your kidney and urethra health. <br/><br/> Manage your health trends using regular tests and hydration/urination recording in Cym<sup>702</sup> app.", "blood_positive_plus_level_modal": "We detected a relatively low amount of blood in your urine, 10 RBC/μL or more, but don’t worry. It can be caused temporarily by intense exercise, stress, high fever, and menstruation, even for healthy individuals. <br/><br/> Blood caused by intense exercise can occur due to temporary impact on the kidneys and bladder, or a compensatory increase in glomerular pressure from decreased renal flow. Exercise-induced blood usually disappears within a few hours after exercise. For female users, blood in urine can be a result of testing too close to menstruation. <br/><br/> However, since the presence of blood in urine may also be due to kidney and urethral issues, we recommend testing regularly to check for occult blood trends.", "blood_positive_double_level_modal": "We detected 50 RBC/μL or above of blood in your urine, but don’t worry. It can be caused temporarily by intense exercise, stress, high fever, and menstruation, even for healthy individuals. <br/><br/> However, the presence of blood in urine may also be due to kidney and urethral issues. <br/><br/> Moverover, occult blood cannot be determined by a single test, so we recommend you to test 2-3 more times to check if the symptom persists, and to see a healthcare professional if you continue to receive 'Positive(++)'.", "blood_positive_triple_level_modal": "We detected 250 RBC/μL or above of blood in your urine. Do not worry. It can be caused temporarily by intense exercise, stress, high fever, and menstruation, even for healthy individuals. <br/><br/> However, the presence of blood in urine may also be due to issues in the urinary system or related diseases. We recommend testing 2-3 more times to check if the symptom persists, and to see a healthcare professional if you continue to receive ‘Positive(+++)’.", "what_is_glycosuria": "What is glucose in urine?", "glycosuria": "glucose", "glucose_info": "Our blood contains a certain amount of glucose, which is called blood sugar. Blood sugar is broken down by insulin hormones secreted by the pancreas, turned into energy, and the rest is excreted outside the body. <br/><br/> When we are healthy, sugar is reabsorbed from the renal tubules and returned to the bloodstream, but when there is a problem with kidney function or the blood sugar level is too high, sugar is excreted through urine. Blood sugar level must exceed 180 mg/dL for glucose to be present in urine. In other words, it can be estimated that the blood sugar level is above 180 mg/dL if glucose is detected in urine. <br/><br/> However, for the same level of blood glucose, the test results may vary after drinking a lot of water or sweating a lot as urine becomes diluted or concentrated. When collecting urine, one should check whether they are collecting after a meal or on an empty stomach. Vitamin C can also affect the results, so it is recommended to avoid excessive intake before testing. <br/><br/> Measuring glucose in urine may be less accurate than measuring glucose levels in blood. This test alone cannot diagnose glucose because it can be caused by environmental factors such as stress or other diseases.", "glucose_negative_level": "No glucose was detected in your urine. You seem to be maintaining a healthy lifestyle!😄 Remember to regularly test for glucose in urine, and check out the health guide below for more information.", "glucose_positive_plus_level": "Your result for glucose is positive(+), so be alert. Glucose level of  250 mg/dL or above was detected in your urine, but don't worry. It can be caused temporarily by stress or decreased kidney function during late pregnancy. <br/><br/> However, since the presence of glucose in urine may also indicate high blood sugar levels or kidney issues, we recommend testing regularly to check for trends. Click on the health guide below for more information.", "glucose_positive_double_level": "Your result for glucose is positive(++), so regular care is needed. Glucose level of 500 mg/dL or above was detected in your urine, but don't worry. It can be caused temporarily by stress or decreased kidney function during late pregnancy. <br/><br/> However, since the presence of glucose in urine may also indicate high blood sugar levels or kidney issues, we recommend testing regularly to check for trends. Click on the health guide below for more information.", "glucose_positive_triple_level": "Your result for glucose is positive(+++), so regular care is needed. Glucose level of 1,000 mg/dL or above was detected in your urine.🤔 It can be caused temporarily by stress or decreased kidney function during late pregnancy, so check out the health guide for more information. <br/><br/> However, the presence of glucose in urine may also be caused by underlying health issues. So, we recommend testing 2-3 more times and, if you continue to receive ‘Positive(+++)’, seeing a healthcare professional.", "glucose_positive_quadruple_level": "Your result for glucose is positive(++++), so regular care is needed. Glucose level of 2,000 mg/dL or above was detected in your urine.🤔. It can be caused temporarily by stress or decreased kidney function during late pregnancy, so check out the health guide for more information. <br/><br/> However, the presence of glucose in urine may also be caused by underlying health issues. So, we recommend testing 2-3 more times and, if you continue to receive ‘Positive(++++)’, seeing a healthcare professional.", "glucose_negative_level_modal": "No glucose was measured in your urine, and your health is at an appropriate level. Since the presence of glucose indicates an already high blood sugar level, it is important to regularly check for glucose trends in urine. <br/><br/> Also, be aware that glucose level in urine can easily change from water intake or sweating, which dilutes or concentrates your urine.", "glucose_positive_plus_level_modal": "Glucose level of 250 mg/dL or above was detected in your urine, but don't worry. It can be caused temporarily by environmental factors such as stress or decreased kidney function during late pregnancy. <br/><br/> However, since the presence of glucose in urine may also indicate high blood sugar levels or kidney issues, check your glucose trend in urine regularly.  We recommend tracking your weight and setting a balanced diet through Cym<sup>702</sup> application.", "glucose_positive_double_level_modal": "Glucose level of 500 mg/dL or above was detected in your urine, but don't worry. It can be caused temporarily by environmental factors such as stress or decreased kidney function during late pregnancy. <br/><br/> However, since the presence of glucose in urine may also indicate high blood sugar levels or kidney issues, check your glucose trends in urine regularly. <br/><br/> A diet on a constant routine and adequate amount is important, and be careful of consuming highly-condensed energy sources such as sugar or honey, as they can cause a spike in your blood glucose level.", "glucose_positive_triple_level_modal": "Glucose level of 1,000 mg/dL or above was detected in your urine.🤔 It can be caused temporarily by environmental factors such as stress or decreased kidney function during late pregnancy, so don't worry. <br/><br/> However, the presence of glucose in urine may also be caused by underlying health issues. So, we recommend testing 2-3 more times and, if you continue to receive ‘Positive(+++)’, seeing a healthcare professional.", "glucose_positive_quadruple_level_modal": "Glucose level of 2,000 mg/dL or above was detected in your urine.🤔 It can be caused temporarily by environmental factors such as stress or pregnancy, so don't worry.  <br/><br/> However, the presence of glucose in urine may also be caused by underlying health issues. So, we recommend testing 2-3 more times and, if you continue to receive ‘Positive(++++)’, seeing a healthcare professional.", "what_is_proteinuria": "What is protein in urine?", "protein_precautions": "Precautions on Protein", "protein_precautions_description": "Urine with Riboflavin (0.6 mg/dL or more) can result in a false positive.", "proteinuria": "protein", "protein_info": "Presence of protein in urine is called protein. It is normal for the kidneys to excrete 150mg or less of protein per day. <br/><br/> In adults, over 150 mg of protein excreted per day is considered abnormal and is clinically defined as protein. If protein excretion exceeds 3.5 g/m<sup>2</sup>, it is called nephrotic syndrome. <br/><br/> Protein is one of the important indicators of kidney disease and can be associated with various clinical problems, so it must be diagnosed accurately and managed properly. <br/><br/> Many people think that seeing foam in urine indicates protein, but that may not necessarily be the case. Therefore, it is important to check for the presence of protein through urine tests.", "protein_good_level": "Your test result for protein is negative, which is good. No protein was detected in your urine. You seem to be maintaining a healthy lifestyle!😄 Remember to regularly test for your protein trend in urine, and check out the health guide below for more information.", "protein_positive_plus_level": "Your test result for protein is positive(+), so be alert.🤔 A protein level of 30 mg/dL or more was detected in your urine, but don’t worry. It can be caused temporarily by concentrated urine after intense exercise or fatigue. In adults, protein of less than 150 mg can be excreted daily from the kidney. <br/><br/> However, since the presence of protein in urine may also indicate kidney issues, we recommend testing regularly to check for trends in proteinuria. Click on the health guide below for more information.", "protein_positive_double_level": "Your test result for protein is positive(++), so regular care is needed.🤔 A protein level of 100 mg/dL or more was detected in your urine, but don’t worry. It can be caused temporarily by concentrated urine after intense exercise or fatigue. In adults, protein of less than 150 mg can be excreted daily from the kidney. <br/><br/> However, since the presence of protein in urine may also indicate kidney issues, we recommend testing regularly to check for trends in proteinuria. Click on the health guide below for more information.", "protein_positive_triple_level": "Your test result for protein is positive(+++), so regular care is needed.🤔A protein level of 300 mg/dL or more was detected in your urine, but don’t worry. It can be caused temporarily by excessive exercise, fatigue, extended duration of standing, etc. You can check for further details on the health guide below. <br/><br/> However, since the presence of protein in urine may also be a symptom of kidney-related diseases, we recommend testing 2-3 more times to check and, if you continue to receive ‘Positive(+++)’, seeing a healthcare professional.", "protein_positive_quadruple_level": "Your test result for protein is positive(++++), so regular care is needed.🤔 A protein level of 1000 mg/dL or more was detected in your urine.🤔 It can be caused temporarily by various factors such as excessive exercise, fatigue, extended duration of standing, etc., so don’t worry. You can check for further details on the health guide below. <br/><br/> However, since the presence of protein in urine may also be a symptom of kidney-related diseases, we recommend testing 2-3 more times to check and, if you continue to receive ‘Positive(++++)’, seeing a healthcare professional.", "protein_negative_level_modal": "No protein was detected in your urine. You seem to be maintaining a healthy lifestyle! <br/><br/> Manage your protein trend in urine, as it is an important indicator of your kidney health. Regularly test and record hydration/urination through Cym<sup>702</sup> app.", "protein_positive_plus_level_modal": "A protein level of 30 mg/dL or more was detected in your urine, but don’t worry. Even in healthy individuals, protein can be detected temporarily in urine by highly alkaline urine, intense exercise, standing up for an extended duration (orthostatic albuminuria), stress, fatigue, and concentrated urine from dehydration. <br/><br/> However, since the presence of protein in urine can indicate an issue in kidney functions, we recommend testing regularly to track your proteinuria trends. <br/><br/> Cym<sup>702</sup>'s urination record feature can help you manage your kidney health more effectively. For reference, it is ideal for adults to urinate 4-6 times a day. If you urinate more than 8 times, you may be experiencing frequent urination or have excessive fluid intake.", "protein_positive_double_level_modal": "A protein level of 100 mg/dL or more was detected in your urine, but don’t worry. Even in healthy individuals, protein can be detected temporarily in urine by intense exercise, standing up for an extended duration (orthostatic albuminuria), stress, fatigue, and concentrated urine from dehydration. <br/><br/> However, since the presence of protein in urine can indicate an issue in kidney functions, we recommend testing regularly to track your proteinuria trends. <br/><br/> Cym<sup>702</sup>'s urination record feature can help you manage your kidney health more effectively. For reference, it is ideal for adults to urinate 4-6 times a day. If you urinate more than 8 times, you may be experiencing frequent urination or have excessive fluid intake.", "protein_positive_triple_level_modal": "A protein level of 300 mg/dL or more was detected in your urine, but don’t worry. Even in healthy individuals, protein can be detected temporarily in urine by highly alkaline urine, intense exercise, standing up for an extended duration (orthostatic albuminuria), stress, fatigue, and concentrated urine from dehydration. <br/><br/> However, since the presence of protein in urine can indicate an issue in kidney functions, we recommend testing regularly to track your proteinuria trends. <br/><br/> Cym<sup>702</sup>'s urination record feature can help you manage your kidney health more effectively. For reference, it is ideal for adults to urinate 4-6 times a day. If you urinate more than 8 times, you may be experiencing frequent urination or have excessive fluid intake.", "protein_positive_quadruple_level_modal": "A protein level of 1000 mg/dL or more was detected in your urine, but don’t worry. Even in healthy individuals, protein can be detected temporarily in urine by highly alkaline urine, intense exercise, standing up for an extended duration (orthostatic albuminuria), stress, fatigue, and concentrated urine from dehydration. <br/><br/> However, since the presence of protein in urine can indicate an issue in kidney functions, we recommend testing regularly to track your proteinuria trends. <br/><br/> Cym<sup>702</sup>'s urination record feature can help you manage your kidney health more effectively. For reference, it is ideal for adults to urinate 4-6 times a day. If you urinate more than 8 times, you may be experiencing frequent urination or have excessive fluid intake.", "what_is_ph": "What is urine pH?", "ph_info": "Urine pH is a value that determines whether urine is acidic or alkaline and refers to the concentration of hydrogen ions in urine. <br/><br/> Our lungs and kidneys work to keep a constant pH level within our body through breathing and excreting urine. Therefore, we can predict the health of our respiratory and renal system based on the result of urine pH levels. The lungs release carbon dioxide through breathing, which gets rid of the acid in the body, and the kidneys control the concentration of bicarbonate in the blood and release acid into urine. <br/><br/> Urine pH is also closely tied to our diet. A diet heavy on protein and animal products can result in acidic urine, and plant based diets in alkaline urine. As results can fluctuate greatly depending on food, fluid intake, exercise, and medication, the test should be repeated multiple times.", "ph_normal_level": "Your test result for urine acidity is negative, meaning that it is at an appropriate level.😄 The pH of urine in healthy individuals is between 4.6 and 8.0. <br/><br/> Urine pH level changes according to diet, fluid intake, exercise, and medication, so we recommend testing regularly. Also, check out the health guide below for more information.", "ph_warning_level": "Your test result for urine acidity is positive, meaning that it is at a pH level of 9 or higher. Though that pH level is too high, it can be temporary, as even healthy individuals may experience high urine pH due to exercise, hyperventilation, high fever, etc. <br/><br/> We recommend testing 2-3 more times, and if you continue to receive “Positive(pH9)”, seeing a healthcare professional.", "ph_negative_level_modal": "The pH of urine in healthy individuals is between 4.6 and 8.0, and the acidity of your urine is at an appropriate level. <br/><br /> The average urine pH level ranges from 5.5 to 6.5. A pH of 5.5 or below is considered acidic, 5.5 to 7 neutral, and 7 or above alkaline. <br/><br/> ❗️️️ Even within the appropriate range, urine of pH 5, which indicates slight acidity, can be caused by a diet high in animal protein, an empty stomach, dehydration, etc. <br/><br/> Urine pH levels are heavily affected by what we eat and drink. Therefore, if the test results are consistently around pH 5, we recommend reducing animal foods such as meat and dairy while consuming more plant-based foods such as fruits and vegetables, along with sufficient water intake.", "ph_positive_level_modal": "Your urine pH was measured to be slightly alkaline, but don't worry. Even healthy individuals can temporarily experience high urine pH due to exercise, hyperventilation, high fever, etc., which causes increased carbon dioxide or bicarbonate production inside the body. In addition, excessive intake of vegetables may result in alkaline urine. <br/><br/> However, highly alkaline urine may also be an indicator of certain diseases. We recommend testing 2-3 more times to check, and if you continue to receive “Positive(pH9)”, seeing a healthcare professional.", "exertion": "Try", "enter": "Enter", "what_is_ketone": "What is ketone in urine?", "ketone_precautions": "Precautions on Ketone", "ketone_precautions_description": "Urine with Ascorbic acid (5.25 mg/dL or more) or Riboflavin (0.6 mg/dL or more) can result in a false negative.", "ketone_info": "Ketones are a type of acid that can be produced in the liver when fat is used as fuel due to high or low blood sugar levels, and can accumulate in the bloodstream and be excreted in urine through the kidneys.<br/><br/>Glucose is the first fuel source used by the body and the presence of ketones detected in urine suggests a lack of insulin in the body, which prevents glucose from being used as an energy source.<br/><br/>In general, ketone production in the body can be a dangerous situation for people with diabetes, so urine ketone testing is as important as blood sugar testing for all people with diabetes. If the results are marked as caution, we suggest repeating the test 2-3 times and consulting with a professional if the same results persist.", "ketone_info_ketosis": "Ketones are compounds that are formed when our bodies break down fat for energy. When our bodies don't have enough energy sources such as carbohydrates, they burn fat to produce energy.<br/><br/>he presence of ketones in urine indicates that the body is using fat as an energy source, a state also known as ketosis. Ketosis levels can help you predict the amount of fat your body is burning.<br/><br/>Generally, for those without diabetes, ketone production in the body is a normal response to fasting and regimented diets. However, if you have diabetes and receive 'Caution', you may need to speak to a healthcare professional.", "ketone_good_level_minus": "Good", "ketone_negative_txt": "Your result for ketone is negative, which is good.😄 No ketone was detected in your urine. You are in a healthy shape.👍🏻 Continue your progress and maintain your health through Cym<sup>702</sup> application’s ketone care.😉", "ketone_negative_level_modal": "No ketone was detected in your urine. You are using glucose as a source of energy through insulin in your body. <br/><br/> Continue your progress and maintain your health through Cym<sup>702</sup> application’s ketone care.😉", "ketone_good_level": "No ketone was detected in your urine.😅 There is nothing to be worried about! For those who are not on a ketogenic diet, your body is in a healthy state. Check out the health guide below. <br/><br/> If you want to dissolve your body fat, go on a healthy diet and exercise, while using the application’s weight care system. It will surely help as you can set a target weight, record your daily weight, and monitor your changes on a weekly/monthly/yearly basis.😉", "ketone_warning_level_plus_minus": "Caution(+/-)", "ketone_warning_level_plus_minus_txt": "Your result for ketone is positive(+/-), so be alert. A ketone level of 5 mg/dL or lower was detected in your urine.😐 Ketone in urine can go above the normal level depending on your day’s condition and diet.", "ketone_positive_plus_minus_level_modal": "A ketone level of 5 mg/dL or lower was detected in your urine. <br/><br/> We recommend using Cym<sup>702</sup> Hydration Care to drink one cup of water per hour and testing 2-3 more times to check for results. <br/><br/> Test results can go above the normal level depending on your day’s condition and diet.", "ketone_enter_level_modal": "A ketone level of 5 mg/dL or lower was detected in your urine. This indicates that you have entered ketosis. <br/><br/> Your body is using fat as a source of energy. Maintain your diet and exercise pattern to reach the optimized ketosis level.", "ketone_normal_level_modal": "No ketone was detected in your urine.👍🏻 Your body is in a healthy state. <br/><br/> Continue your progress and maintain your health through Cym<sup>702</sup> application’s ketone care.😉", "ketoneMode_normal_level_modal": "A ketone level of about 10 mg/dL was detected in your urine. This indicates that you have reached an ideal ketosis state. <br/><br/> For those with no diabetic diseases, your body has reached its maximum fat-dissolving stage. If you are on a diet, we hope you maintain your current status.", "ketone_warning_level_plus": "Caution(+)", "ketone_great_level": "Great", "ketone_warning_level_plus_txt": "Your result for ketone is positive(+), so be alert. A ketone level of 10 mg/dL was detected in your urine.🤔 Drink one cup of water per hour, and test 2-3 more times for a better result. If you have any diabetic diseases, we recommend visiting a healthcare professional.", "ketone_positive_plus_level_modal": "A ketone level of 10 mg/dL was detected in your urine. The test result can go above the normal level depending on your day’s condition and diet. <br/><br/> However, if you have any diabetic diseases (type 1 diabetes, gestational diabetes, juvenile diabetic patients with type 2 diabetes, type 2 diabetic patients with fasting glucose level beyond 240 g/dL), look at the guide below.<br/><br/> Drink a cup of water every 3-4 hours and record in the Cym<sup>702</sup> Hydration Care. <br/><br/> If your test results for glucose or ketone are positive two or more times, or if the symptoms below are seen, we recommend visiting a healthcare individual. <br/><br/> <strong>Associated Symptoms</strong> <br/>&nbsp; &bull; &nbsp; Abdominal pain, nausea, vomit, tachypnea <br/>&nbsp; &bull; &nbsp; Fruity or nail polish scent in breath <br/><br/> <strong> Preparation Item </strong> <br/>&nbsp; &bull; &nbsp; Ketone and glucose test time and results <br/>&nbsp; &bull; &nbsp; List and amount of food and beverage taken <br/>&nbsp; &bull; &nbsp; List of medication taken <br/>&nbsp; &bull; &nbsp; Body temperature", "ketone_normal_level": "You have entered a state of ketosis!😄 For those with no diabetic diseases, you are in good condition, and have entered a ketosis state where your body is dissolving fat. <br/><br/> Your body is using fat as a source of energy, and if you continue to maintain this diet and exercise routine, you will reach the optimized ketosis state, ‘Great’! Check on the health guide below.", "ketone_caution_level_txt": "Your result for ketone is positive(++), and regular care is needed. A ketone level of 50 mg/dL was detected.😧 That amount of ketone in your urine is quite a lot. If you have any diabetic diseases, drink one cup of water per hour, test your urine every 2 hours and check for results. If you continue to get ‘Positive(++)’, we recommend visiting a healthcare individual.", "ketone_positive_double_level_modal": "A ketone level of 50 mg/dL or above was detected in your urine. This amount of ketone in your urine is very high. <br/><br/> If you have any diabetic diseases (type 1 diabetes, gestational diabetes, juvenile diabetic patients with type 2 diabetes, type 2 diabetic patients with fasting glucose level beyond 240 g/dL), look at the guide below. <br/><br/> Drink a cup of water every 3-4 hours and record in the Cym<sup>702</sup> Hydration Care. <br/><br/> If your test results for glucose or ketone are positive two or more times, or if the symptoms below are seen, we recommend visiting a healthcare individual. <br/><br/> <strong> Associated Symptoms </strong> <br/>&nbsp; &bull; &nbsp; Abdominal pain, nausea, vomit, tachypnea <br/>&nbsp; &bull; &nbsp; Fruity or nail polish scent in breath <br/><br/> <strong> Preparation Item </strong> <br/>&nbsp; &bull; &nbsp; Ketone and glucose test time and results <br/>&nbsp; &bull; &nbsp; List and amount of food and beverage taken <br/>&nbsp; &bull; &nbsp; List of medication taken <br/>&nbsp; &bull; &nbsp; Body temperature", "ketone_danger_level_txt": "Your result for ketone is positive(+++), and regular care is needed. A ketone level of 100 mg/dL was detected.😥 That amount of ketone in your urine is very high. If you have any diabetic diseases, drink one cup of water per hour, test your urine every 2 hours and check for results. If you continue to get ‘Positive(+++)’, we recommend visiting a healthcare individual.", "ketone_positive_triple_level_modal": "A ketone level of 100 mg/dL was detected in your urine. <br/><br/> If you have any diabetic diseases (type 1 diabetes, gestational diabetes, juvenile diabetic patients with type 2 diabetes, type 2 diabetic patients with fasting glucose level beyond 240 g/dL), follow the guide below. <br/><br/> Drink one cup of water per hour, test your urine every 2 hours and check for results.<br/><br/> If you continue to get the same results for ketone and glucose, we recommend visiting a healthcare individual.", "ketone_normal_explain": "You have entered a state of ketosis!😄 For those with no diabetic diseases, you are in good condition, and have entered a ketosis state where your body is dissolving fat. <br/><br/> Your body is using fat as a source of energy, and if you continue to maintain this diet and exercise routine, you will reach the optimized ketosis state, ‘Great’! Check on the health guide below.", "ketone_caution_level": "Wow! You are at an optimal ketosis level.😍 For those with no diabetic disorder, your body is actively dissolving fat. <br/><br/> If you are on a diet, maintain this ‘Great’ state and check on the health guide below.", "ketone_warning_level": "Your ketosis level is too high.😟 Pause any excessive exercise or diet and check on the health guide below. <br/><br/> High state of ketosis can be caused by extended periods of fasting or intense diet and exercise, so test 2-3 more times. If you continue to receive this ‘Warning’ state, we recommend you see a healthcare individual.", "ketone_exertion_level_modal": "No ketone was detected in your urine. For healthy individuals, this is not an issue. Ketone in a healthy individual’s urine can be an indicator of fat dissolution. <br/><br/> If you wish to check your fat dissolution status through a urine test, compose a diet based on low carbohydrates and high protein, and actively work out. <br/><br/> Try using the Cym<sup>702</sup> Weight Care to set your target weight, monitor your trends and efficiently manage your health.", "ketone_great_level_modal": "A ketone level of 50 mg/dL or above was detected in your urine. <br/><br/> For those with no diabetic diseases, you have reached an active state of fat dissolution! If you are on a diet, we hope you maintain your current status.", "ketone_good_level_modal": "A ketone level of 50 mg/dL was detected in your urine. <br/><br/> For those with no diabetic diseases, you have reached an active state of fat dissolution! If you are on a diet, we hope you maintain your current status.", "ketone_warning_level_modal": "A ketone level of 100 mg/dL was detected in your urine. This high state of ketosis can be caused by extended periods of fasting or intense diet and exercise.<br/><br/> If you are extending yourself over the limit, pause for a while and go back to a balanced diet and exercise. Try ketosis after you have returned back to a normal level. We recommend testing 2-3 more times and, if you continue to receive a 'Caution' state, seeing a healthcare individual.", "level_table_title": "Cym702's Health Guide", "nodata": "No data", "avg_week_header": "Weekly Avg", "avg_month_header": "Monthly Avg", "avg_year_header": "Yearly Avg", "target_water_value": "Water intake goal", "today_pee_count": "Urination today", "save": "Save", "goal": "Target", "directly": "Manually", "save_success": "Save Success", "save_fail": "Save Failed", "delete_success": "Delete Success", "delete_fail": "Delete Failed", "no_date_selected": "No Date Selected", "connection_success": "Connection Success", "connection_fail": "Connection Failed", "pee_error": "Record one at a time", "pee_error_msg": "Please record one at a time", "notice_marketing_agree_modal": "You have agreed to receive notices and marketing information.", "notice_marketing_disagree_modal": "You have opted out of receiving notices and marketing information.", "notice_agree_modal": "You have agreed to receive notices.", "marketing_agree_modal": "You have agreed to receive marketing information.", "confirm_disagree_modal": "Do you want to unsubscribe from", "notice_disagree_modal": "You have canceled the subscription agreement for", "topic_marketing": "Marketing Information", "topic_notice": "Notice", "cym702_no_data": "No data available", "weight_title": "Weight", "pee_title": "Urination", "water_title": "Water", "bloodPressure_title": "Blood Pressure", "step_title": "Steps", "woman_title": "Woman", "period": "Period", "period_start": "Period Starts", "in_period": "During Period", "period_end": "End of Period", "not_possible_enter": "Pre-entering is difficult!", "fertile_window": "Fertile Window", "ovulation_day": "Ovulation Day", "today": "Today", "days_ago": "days ago", "in_day": "Day", "chart_btn_d": "Day", "chart_btn_w": "Week", "chart_btn_m": "Month", "chart_btn_y": "Year", "average_score": "Avg Score", "set_target_weight": "Set target weight", "set_target_water": "Set target water intake", "set_target_calorie": "Set daily calorie intake", "ketone_mode_setting": "Set Ketone Analysis", "recommended_water": "Recommended water intake", "health_info_settings": "Set health info", "until_now": "So far you have lost", "decrease": ", and have", "untile_target": "You need", "need_decrese": "lose until your target weight.", "need_over": "gain until your target weight.", "record": "Record", "until": "left until", "remain_water": "", "hydration_trend": "Check my hydration trend", "urination_trend": "Check my urination trend", "nodata_water": "No data available. Please record your water intake.", "nodata_pee": "No data available. Please record your urinary frequency.", "what_is_target_weight": "My Target Weight?", "target_weight_description": "Your target weight compares<br/> your actual and target weights.<br/><br/>Press the button on the top<br/> right to change target weight.", "what_is_target_water": "Water intake goal?", "target_water_desc": "Your water intake goal is based <br/>on height, weight, and gender.<br/><br/> Press the button below to<br/> change health info.", "food_search_placeHolder": "menu name, ingredients", "food_search": "Food Search", "search": "Search", "recent_food": "Recent Food", "bookmark": "Favorites", "add_record": "<PERSON><PERSON>", "view_meals": "Apply Settings", "add_favorites": "Add to Favorites", "add_meals": "Add to My Meals", "complete_success": "Added to Favorites", "delete_favorites": "Delete To Favorites", "complete_add_record": "Added to My Meals", "complete_delete": "Removed from Favorites", "add": "Add", "foods_count": " <PERSON><PERSON>", "edit_time": "Time Fix", "solution_tab1": "Recommended", "solution_tab2": "Favorites", "solution_tab3": "My Meals", "tag_high_fat": "High-fat 🥑", "tag_low_carb": "Low-carb 🥥", "tag_low_carl": "Low-calorie 🥒", "tag_high_protein": "High-protein 🍗", "tag_low_sodium": "Low-sodium 🧂", "tag_low_sugar": "Low-sugar 🍅", "tag_vegan": "Vegan 🥕", "tag_lacto": "Lacto-ovo 🥛", "tag_pesco": "Pescatarian 🐟", "tag_korean": "Korean 🍚", "tag_chinese": "Chinese 🥟", "tag_japanese": "Japanese 🍣", "tag_western": "Western 🍝", "tag_snack": "Snack 🧁", "tag_salad": "Salad 🥗", "tag_other": "Other 🥢", "btn_select": "Select", "btn_filter": "Filter", "reset": "Reset", "type_category": "Type", "calories_filter": "Calorie (per serving) ", "calorie": "Calorie", "per_serving": "per serving", "edit_per_serving": "Edit single serving", "nutrition_info": "Nutrition Facts", "carbohydrate": "Carbohydrates", "protein_ingredient": "<PERSON><PERSON>", "fat": "Fat", "sodium": "Sodium", "sugar": "Sugar", "main_ingredient": "Main Ingredient", "no_favorite": "There are no favorites yet.", "no_result_food": "There is no corresponding food.😭", "try_filter_reset": "Try resetting the filter.", "find_favorite": "Try adding meals from our recommendations!", "record_description": "Plan your meals for the day <br/> and view the total calories", "all_calories": "Total calories of my meals", "general_calories": "This is the general recommended calorie<br/> intake for an average male/female <br/><br/>If you wish to change your total calorie,<br/> press the settings button <br/>or the button below", "set_calories": "Set calories", "breakfast": "Breakfast", "lunch": "Lunch", "dinner": "Dinner", "snack": "Snack", "before_test": " Before we start the urine test, please check the precautions 📢", "caution_title1": "Be mindful of Vitamin C intake.", "caution_content1": "Too much Vitamin C can darken your urine and affect the results.", "caution_title2": "Avoid testing near/during menstruation.", "caution_content2": "There may be blood present before and after menstruation, which can affect the results.", "caution_title3": "Try to test first thing in the morning.", "caution_content3": "Urine that has concentrated overnight can increase result accuracy.", "note_confirm_btn": "I understand", "guide_title_first": "Attach test strip to<br/> the boat.", "btn_content_first": "Attached test strip", "guide_title_second": "Pee on the boat", "guide_title_third": "Remove the boat<br/> as soon as the strip<br/> becomes wet.", "btn_content_second": "Finished peeing", "exam_video_title": "Cym<sup>702</sup> Urine test Guide Video", "exam_prev_btn": "Replay the Guide Video", "exam_guide_txt_one": "We need to wait", "exam_guide_txt_two": "60 seconds", "exam_guide_txt_three": "for the test strip to react.", "exam_notice_txt_one": "After the timer ends, you will automatically be redirected to take a photo.", "exam_notice_txt_two": "Gently shake off excess urine", "exam_notice_txt_three": "Mount boat on package slit", "exam_notice_txt_four": "Wipe off dirt on camera lens", "exam_notice_txt_five": "Align the boat with the guide box and take a photo", "exam_notice_txt_six": "Avoid dark lighting.", "exam_notice_btn": "Waiting for 60 sec...", "take_urine_test_btn": "Take photo now", "tutorial_one_title": "View Cym Score Details", "tutorial_one_text": "Cym<sup>702</sup> Health Score<br/> We numerically evaluate your <br/>health status based on your<br/> urine test results.", "tutorial_two_title": "Urine Test Results", "tutorial_two_text": "We summarize the results for <br/>an easy viewing experience.<br/><PERSON>lick to find out more.", "tutorial_two_description": "Click on a result <br/> to see the interpretation of the results <br/> for that item.", "tutorial_three_title": "Weight, Hydration, Urination", "tutorial_three_text": "Swipe the screen downwards to<br/> record your daily weight,<br/> hydration, and urination!", "confirm_btn": "OK", "delete_btn": "Delete", "cancle_btn": "Cancel", "category_meal": "My Meals", "category_guide": "User Guide", "category_alert": "Notification Settings", "alert_success_modal": "⏰ Your notification is<br />changed successfully!", "category_notice": "Notice", "category_customer_service": "Customer Service", "faq": "Frequently Asked Questions", "before_use": "Usage", "peeing": "Peeing", "photo": "Shooting", "result": "Result", "kakao_inquiry": "Kakao Channel 1:1 Inquiry", "terms_info": "View Terms and Conditions", "service_terms": "Terms of Service", "privacy_terms": "Personal information policy", "marketing_terms": "Marketing Consent", "sensitive_terms": "Sensitive information policy", "category_about_company": "About Yellosis", "guide_category_app": "App Guide", "test_alert_settings": "Notification Settings", "am": "AM", "pm": "PM", "cycle": "Repeat", "weekly_cycle": "Repeat weekly", "monthly_cycle": "Repeat every 15 days", "repeat": "", "mon": "MON", "tue": "TUE", "wed": "WED", "thu": "THU", "fri": "FRI", "sat": "SAT", "sun": "SUN", "guide_category_exam": "Urine Testing Guide", "setting_version": "Version information", "account_info": "Account", "sns_login_settings": "Manage social login accounts", "setting_logout": "Log out", "setting_logout_confirm_txt": "Do you want to <br/> log out of Cym<sup>702</sup>?", "setting_data_share": "Share data", "setting_samsung": "Sync with Samsung Health App", "setting_apple": "Sync with Apple Health App", "setting_notification": "App notification settings", "setting_version_txt": "Version information", "open_source": "Open source license", "foods_source": "Compilation of Nutritional Information for Dining Out_Ministry of Food and Drug Safety", "setting_delete_account": "Delete account", "delete_text": "Are you sure you want to delete your account?", "delete_description1": "Your health records will be deleted upon account deletion.", "delete_description2": "Deleted records cannot be recovered.", "delete_description3": "You cannot create a new account with the same phone number for 30 days.", "delete_reason": "Please let us know why you decided to leave", "plz_select": "Choose option", "yes": "Yes", "no": "No", "delete_account_confirm_txt": "Your urine test data and health records will be deleted.<br/>Are you sure you want to delete your account?", "not_used": "I don't use it often", "difficult_use": "The app is difficult to use", "test_anymore": "I don't need urine tests anymore", "delete_records": "I want to delete my records", "delete_input": "👂🏻Other (please specify)", "delete_confirm": "😭 Your account has been deleted.<br/> We hope to see you again in the future. Stay healthy!🍀", "deleted_account": "You cannot re-join with a canceled account within 30 days.", "setting_langauge": "Language settings", "setting_header_title": "Settings", "setting_push": "Push notification setting", "push_description": "Set a reminder for tests so you don't forget", "setting_noti": "Notice notifications", "setting_marketing": "Marketing notifications", "alert_description": "You can receive a variety of offers and services from Cym<sup>702</sup> <br /><br />Purpose of use: Push notification service<br />Collected info: Device ID, member ID<br />Retention period: Until deletion of account or preservation period according to laws and regulations<br /><br />*If you don't receive a notification, please check your mobile phone settings", "new_version": "Newer version available", "cur_version": "Current Version", "version": "Update to Version", "update": "", "supported": "Supported in", "above": "or above", "delete_account_question_txt": "Are you sure you want to delete your account?", "delete_account_explain_txt": "Your data will be permanently deleted. You cannot make a new account with the same phone number for the next 30 days.", "delete_account_plz_select": "Select an option.", "delete_account_option_one": "I don’t use it often.", "delete_account_options_two": "The app is difficult to use.", "delete_account_options_three": "I don't use urine tests..", "delete_account_options_four": " I want to delete my data.", "delete_account_options_five": "Other", "delete_account_reason_txt": "Tell us why you’re deleting your account", "delete_account_confirm_btn": "Yes, delete my account.", "delete_account_cancle_btn": "No, keep my account.", "delete_account_popup_txt": "Your feedback is greatly appreciated.", "delete_account__btn": "Delete account", "delete_account__cancle": "Cancel", "profile_title": "Edit Profile", "add_user": "Add User", "add_user_description": "Please create a profile for the new user.", "relationships": "Relationship with myself", "patient": "Patient", "caregiver": "Caregiver", "other": "Other", "parents": "Parents", "spouse": "Spouse", "child": "Child", "grandparents": "Grandparents", "brothers": "Brothers and sisters", "relative": "Relative", "acquaintance": "Acquaintance", "friend": "Friend", "delete_sub_user": "Delete Profile", "disease_group": "Disease Group", "profile_email": "Email", "profile_phone": "Phone Number", "profile_pwd": "Password", "profile_survey": "Health Information", "profile_info": "Basic Information", "basic_info_edit": "Basic Information Edit", "profile_name_title": "Enter Nickname", "change": "change", "profile_name_txt": "Your username can be up to 10 characters in Korean, English or numbers.", "profile_phone_title": "Change phone number", "profile_phone_txt": "To change your phone number, click the ‘Change’ button below.", "profile_authorization_txt": "Enter your new phone number and verify.", "profile_pwd_title": "Change Password", "profile_pwd_txt": "To protect your account, please enter your current password.", "profile_newpwd_txt": "Enter your new phone number. The new number will be applied after verification.", "profile_survey_title": "Edit Health Information", "basic_profile_edit": "Basic Info", "lifestyle_profile_edit": "Lifestyle", "diet_profile_edit": "Diet", "success_message_phone": "📱 Your phone number has been changed successfully!", "success_message_pwd": "🔒 Your password has been changed successfully!", "success_message_name": "✏️ Your information is changed successfully!", "name_error_modal": "Change failed. Please check your name.", "success_message_survey": "📋 Are you sure you want to cancel the health information change?", "pwd_error_modal": "🥺 Password change failed. <br /> Please try again.", "pwd_error": "🥺 Password does not match. <br /> Please try again.", "pwd_match_error": "Password does not match.", "useage_guide": "User Guide", "about_cym_title": "<div>What is Cym<sup>702</sup></div>?", "about_cym": "Cym<sup>702</sup> is a brand identity of an urinalysis system and customized solutions, researched and developed by Yellosis. Inc. Our mission is to nourish and prosper mankind based on our analytic/diagnostic technologies. <br/>Cym<sup>702</sup> is a brand identity of a smart urinalysis system and customized solutions, researched and developed by Yellosis. Inc. Our mission is to nourish and prosper mankind based on our analytic/diagnostic technologies. <br/>In order to achieve our goal, we are making ‘Cym<sup>702</sup>’, a urine-based AI healthcare solution, so that ordinary people can simply manage their health in their everyday lives. <br/>‘Cym’ is an acronym of Yellosis’s wholehearted slogan, ‘We Care Your Moment’. <br/>‘7’ represents 7 am, the best hour in a day for an urinalysis. ‘0’ symbolizes the virtuous cycle of healthcare and preservation through a periodic urine test. ‘2’ stands for the two values that we hold, the wellbeing of humanity and its surrounding environment.", "what_is_cym_title": "<div>What is Cym<sup>702</sup> Boat (Basic 5)</div>", "what_is_cym": "Cym<sup>702</sup> Boat is the first urine testing device presented by Yellosis Inc.. With Cym Boat, you can test your urine without preparing a separate cup or getting your hands wet. <br /> Cym<sup>702</sup> Boat (Basic 5) tests for protein, blood, glucose, pH, and ketone levels within your urine, and offers insight into your kidney health, presence of blood and sugar in your urine, hydration and fat burning levels. <br /> You can analyze and view your results immediately through the Cym<sup>702</sup> mobile application. You can also track your progress as you continue to use Cym<sup>702</sup> Boat (Basic 5).", "where_buy_title": "<div>Where can I buy <PERSON><PERSON><sup>702</sup> Boat (Basic 5)?</div>", "where_buy": "You can purchase it by clicking the 'Shop' button at the bottom of our app, or entering our Naver Smart Store(https://smartstore.naver.com/cym702).", "about_cym_results_title": "<div>Are the results provided by the Cym<sup>702</sup> application considered medical diagnosis?</div>", "about_cym_results": "Cym<sup>702</sup> Boat (Basic 5) analyzes certain features in the urine, so that individuals can instantly check the severity of each category, thus helping users efficiently manage their health. It is not a diagnostic tool that determines signs of abnormality in a patient’s body, but an assisting tool for a patient to monitor his or her health trend. Through a periodic urine test using Cym<sup>702</sup> Boat (Basic 5), users can effectively check and maintain their health.", "how_to_use_title": "<div>How do I use Cym<sup>702</sup> Boat (Basic 5)?</div>", "how_to_use": "&bull; After purchasing the Cym<sup>702</sup> Boat, install the Cym<sup>702</sup> app. <br /> &bull; &quot;Test&quot; at the bottom of the app. Anyone can easily perform the test by pressing the button and referring to the guide video for urine test. <br/> &bull; You can easily and simply check the test results with the app.", "do_not_use_case_title": "<div>I accidentally opened the sealed pouch and contaminated the test strip. Can I still use it?</div>", "do_not_use_case": "Do not use the test strip if it is opened before testing or if it becomes contaminated, as it may yield inaccurate results.<br/> <b>You cannot use the test strip if:</b><br /> &bull; the test strip is bent or folded. <br />&bull; the test strip is stained with water or other liquids, or has changed color.", "until_use_title": "<div>Does Cym<sup>702</sup> Boat (Basic 5) have an expiration date?</div>", "until_use": "The shelf life of Cym<sup>702</sup> Boat (Basic 5) is 24 months from the date of manufacture, and the date of manufacture is specified on the bottom of the back of the product package.", "how_many_test_title": "How often should I be testing?", "how_many_test": "For normal, healthy individuals, we recommend testing once a month. However, if you wish to monitor your health continuously, we recommend testing once a week. You can use it more frequently if you want to track specific categories such as ketone and hydration levels.", "how_to_store_title": "<div>How do I store <PERSON><PERSON><sup>702</sup>(Basic 5) Boat properly?</div>", "how_to_store": "Store in a cool, dry place, away from direct sunlight at 20-30°C.", "when_medication_title": "I'm on medication. Can I still test my urine?", "when_medication": "Your medication may influence the test results. For accurate results, we recommend testing your urine when you are not taking medicine.", "when_period_title": "I'm on my period. Can I still test my urine?", "when_period": "Your period may influence the test results. For accurate results, we recommend testing your urine when you are not on your period.", "what_is_solution_title": "<div>What does the solution in the Cym<sup>702</sup> application do?</div>", "what_is_solution": "The Solution recommends a unique diet plan to the users. Based on the medical information, diet survey and urine test results, we are planning to provide a customized and sophisticated diet solution.", "is_safe_title": "Is my data safe?", "is_safe": "Yes, your data is completely safe with us. <PERSON><PERSON><PERSON> values the cyber security of the user’s personal data by encrypting every identifiable information on the app while removing all information of deleted users. In case of a cyber security incident during product use, contact the manufacturer’s A/S.", "what_time_test_title": "What time of day should I do my testing?", "what_time_test": "We recommend testing the first thing in the morning, as there are many major biological markers concentrated in first urine. However, you can still test at other times of the day.", "can_reuse_title": "<div>Can I reuse Cym<sup>702</sup> Boat(Basic 5)?</div>", "can_reuse": "<div>Cym<sup>702</sup> Boat(Basic 5) is a single-use product and therefore should not be reused.</div>", "how_does_analyze_title": "<div>How does the Cym<sup>702</sup> app analyze urine test strips?</div>", "how_does_analyze": "The Cym<sup>702</sup> app uses an accurate machine-learning algorithm that analyzes the test strip results. Data gathered through numerous tests provides highly-accurate results.", "every_camera_perform_title": "Every camera performs differently. Does this affect my results?", "every_camera_perform": "Cym<sup>702</sup> app's image processing algorithm has been developed with test images from many different settings and cameras. We have accounted for the difference in camera performance across mobile devices and assure reliability of results.", "how_to_take_photo_title": "How do I take a photo of my test strip?", "how_to_take_photo": "After the 60 second timer ends, your camera will be turned on. Fit your test strip inside the guided box within your phone screen and take a photo. You can check the results immediately after taking the photo..", "why_retake_title": "The app keeps asking me to retake the photo. What should I do?", "why_retake": "The message might continue to show up if your surroundings are too bright or too dark. Try changing your location before taking another photo, and if the problem continues, please contact our customer service center (Kakao Talk Cym702 Channel).", "is_reliable_title": "The test results showed that I was in critical condition, so I went to the hospital but found nothing wrong. Is your product reliable?", "is_reliable": "Cym<sup>702</sup> Boat is not a medical device and is only intended to provide information on your urine test results. Please note that contamination of the strip, or taking the photo before or after the 1 minute mark can influence the accuracy of your results.", "results_are_abnormal_title": "The results are abnormal. Should I go to the hospital right away?", "results_are_abnormal": "We recommend testing one more time if your test results are abnormal. If the results are still abnormal, we suggest seeing a medical professional.", "how_to_view_results_title": "How can I view my past test results?", "how_to_view_results": "On the home screen , click one of the category names to view your history.", "why_not_displayed_title": "I completed my test, but the results are not displayed on the home screen. What should I do?", "why_not_displayed": "Completely turn off the app and reopen. If the results still do not show, please contact our customer service center (Kakao Talk Cym702 Channel).", "why_results_not_shown_title": "I took the photo according to the guide, but the results will not show.", "why_results_not_shown": "Cym<sup>702</sup> app must be used with internet connection. Please check your internet connection before testing and taking the photo. If you continue to face problems, please contact our customer service center.", "is_available_other_categories_title": "Are there other categories for testing?", "is_available_other_categories": "We are currently developing other categories for testing. Stay tuned!", "home_notice_content": "<strong>Now at GS25 CVS!</strong><br>self-medicate with <strong>Cym<sup>702</sup></strong>", "hide_btn": "close", "explore_btn": "Explore", "hide_today": "Don't show this again today."}