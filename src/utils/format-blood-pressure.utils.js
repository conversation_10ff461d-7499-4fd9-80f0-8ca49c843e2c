/**
 * @param {Array<object>} bpData
 * @param {string} [period]
 * @returns {Map<string, object>}
 */
export default function formatBloodPressure(bpData = [], period) {
  // result를 일반 객체 {} 대신 Map 객체로 초기화합니다.
  const result = new Map();

  if (!bpData || bpData.length === 0) {
    return result; // 빈 Map을 반환하도록 수정
  }

  bpData.forEach((record) => {
    const dateObj = new Date(record.date);
    const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
    const day = dateObj.getDate().toString().padStart(2, "0");
    // period가 'y'면 key는 'MM', 아니면 'MM.DD'
    const key = period === "y" ? `${month}` : `${month}.${day}`;

    // 하루에 여러 개면 range, 하나면 값만
    const systolicArr = record.bloodPressures.map((bp) => bp.systolic);
    const diastolicArr = record.bloodPressures.map((bp) => bp.diastolic);

    const systolic =
      systolicArr.length > 0 ? systolicArr[systolicArr.length - 1] : null;
    const diastolic =
      diastolicArr.length > 0 ? diastolicArr[diastolicArr.length - 1] : null;

    const entry = {};
    if (systolic !== null) entry.systolic = systolic;
    if (diastolic !== null) entry.diastolic = diastolic;

    if (systolicArr.length > 1) {
      entry.range = [Math.min(...systolicArr), Math.max(...systolicArr)];
    }

    // Map 객체의 set 메서드를 사용하여 키-값 쌍을 저장합니다.
    result.set(key, entry);
  });

  // Map 객체 자체를 반환합니다.
  return result;
}
/**
 * {
  "date": "2025-06-24",
  "bloodPressures": [
    {
      "id": 18,
      "subjectId": 1,
      "systolic": 120,
      "diastolic": 111,
      "mean": 40074,
      "pulse": 121,
      "comment": null,
      "rawData": null,
      "resourceType": "manual",
      "date": "2025-06-24T17:05:53.000Z",
      "createdAt": "2025-06-24T17:05:53.199Z",
      "updatedAt": "2025-06-24T17:05:53.199Z"
    },
    {
      "id": 17,
      "subjectId": 1,
      "systolic": 121,
      "diastolic": 110,
      "mean": 40407,
      "pulse": 100,
      "comment": null,
      "rawData": null,
      "resourceType": "manual",
      "date": "2025-06-24T11:13:10.000Z",
      "createdAt": "2025-06-24T11:13:10.657Z",
      "updatedAt": "2025-06-24T11:13:10.657Z"
    }
  ]
}
 * 
 */

/**
 *
 * @param {Array<object>} bpData
 * @param {string} [period]
 * @returns
 */
export function formatBloodPressureHistory(bpData = [], period) {
  const result = [];

  function formatDateWithTimezone(dateString, period) {
    // 브라우저(사용자) 로컬 타임존 기준으로 변환
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    let hour = date.getHours();
    const minute = String(date.getMinutes()).padStart(2, "0");
    const mid = hour >= 12 ? "PM" : "AM";
    hour = hour % 12 === 0 ? 12 : hour % 12;
    hour = String(hour).padStart(2, "0");

    return `${year}.${month}${
      period !== "y" ? `.${day} ${hour}:${minute} ${mid}` : ""
    }`;
  }

  for (const bpDayData of bpData) {
    // 날짜만 필요하면 아래 라인 유지, 아니면 제거
    bpDayData.date = bpDayData.date.split("T")[0];

    bpDayData?.bloodPressures.forEach((bp) => {
      let obj = {};
      obj.createdAt = formatDateWithTimezone(bpDayData.date, period);
      obj.value = `${bp.systolic}/${bp.diastolic} mmHg | ${bp.pulse} bpm`;

      if (period !== "y") {
        obj.createdType = bp.resourceType;
      }

      result.push(obj);
    });
  }

  return result;
}

export function calculateBloodPressureAverage(data) {
  const allBP = data.flatMap((dayData) => dayData.bloodPressures);

  if (allBP.length === 0) {
    return { systolic: 0, diastolic: 0 };
  }

  const systolicSum = allBP.reduce((sum, bp) => sum + bp.systolic, 0);
  const diastolicSum = allBP.reduce((sum, bp) => sum + bp.diastolic, 0);

  return {
    systolic: Math.round((systolicSum / allBP.length) * 100) / 100,
    diastolic: Math.round((diastolicSum / allBP.length) * 100) / 100,
  };
}
