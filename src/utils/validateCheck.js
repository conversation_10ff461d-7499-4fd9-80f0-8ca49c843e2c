
/*

mobileNumberRule: [
  (v) => /^[0-9]/g.test(v) || "숫자만 기입 가능합니다.",
  (v) => /^[0-9]{11}/g.test(v) || "",
],
emailrules: [
  (v) => !!v || "이메일을 입력해 주세요",
  (v) => /.+@.+\..+/.test(v) || "이메일 주소를 확인해 주세요.",
],
passwordRules: [
  (v) => !!v || "비밀번호를 입력해 주세요",
  (v) =>
    /^(?=.*[A-Za-z])(?=.*\d)(?=.*[$@$!%*#?&])[A-Za-z\d$@$!%*#?&]{8,}$/.test(
      v
    ) || "영문, 숫자, 특수문자 조합으로 8글자 이상으로 입력하세요.",
],
nicknameRules: [
  (v) => !!v || "닉네임을 입력해 주세요",
  (v) =>
    /^[ㄱ-ㅎ|가-힣|a-z|A-Z|0-9|]{2,10}$/.test(v) ||
    "10글자 이내의 한글, 영문, 숫자만 사용 가능합니다.",
],*/

function validatePhone(v) {
  return /^[0-9]/g.test(v)
}

function validateEmail(v) {
  return /.+@.+\..+/.test(v)
}

function validatePassword(v) {
  return /^[ㄱ-ㅎ|가-힣|a-z|A-Z|0-9|]{2,10}$/.test(v)
}

function validateNickname(v) {
  return /^[ㄱ-ㅎ|가-힣|a-z|A-Z|0-9|]{2,10}$/.test(v)
}

export {
  validatePhone,
  validateEmail,
  validatePassword,
  validateNickname,
}
