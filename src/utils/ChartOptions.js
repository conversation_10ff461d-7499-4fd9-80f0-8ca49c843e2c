function chartStrokeOptions(clickedValue) {
  return clickedValue === "recent" ? "straight" : "smooth";
}

function yaxisTitleText(urineTestItemType) {
  if (urineTestItemType === "blood") return "RBC/µL";
  if (urineTestItemType === "ph") return "";
  return "mg/dL";
}

/** y축 개수 */
function yaxisMaxValue(urineTestItemType) {
  // console.log(urineTestItemType);
  if (urineTestItemType === "protein" || urineTestItemType === "glucose") return 6;
  else if (urineTestItemType === "blood") return 4;
  else return 5;
}

/** y축 내용 */
function yaxisLabelsValue(type, value) {
  // console.log(type, value);
  switch (type) {
    case "blood":
      if (value === 1) return "0";
      if (value === 2) return "10";
      if (value === 3) return "50";
      if (value === 4) return "250";
      else return "";
    case "protein":
      if (value === 2) return "10";
      else if (value === 3) return "30";
      else if (value === 4) return "100";
      else if (value === 5) return "300";
      else if (value === 6) return "1000";
      else return "";
    case "glucose":
      if (value === 2) return "100";
      else if (value === 3) return "250";
      else if (value === 4) return "500";
      else if (value === 5) return "1000";
      else if (value === 6) return "2000";
      else return "";
    case "ph":
      if (value === 1) return "pH 5";
      else if (value === 2) return "pH 6";
      else if (value === 3) return "pH 7";
      else if (value === 4) return "pH 8";
      else if (value === 5) return "pH 9";
      else return "";
    case "ketone":
      if (value === 3) return "10";
      else if (value === 4) return "50";
      else if (value === 5) return "100";
      else return "";
  }
}

export default {
  apexchartOptions() {
    const chartOptions = {
      offsetX: 0,
      animations: {
        enabled: false,
      },
      zoom: {
        enabled: false,
      },
      dropShadow: {
        enabled: true,
        color: "#A7A7A7",
        top: 5,
        left: 0,
        blur: 6,
        opacity: 0.15,
      },
      toolbar: {
        show: false,
      },
      states: {
        active: {
          allowMultipleDataPointsSelection: true,
        },
      },
    };
    return chartOptions;
  },

  strokeOptions: (clickedValue) => {
    const strokeOptions = {
      width: 2.5,
      lineCap: "butt",
      curve: chartStrokeOptions(clickedValue),
    };
    return strokeOptions;
  },

  xaxisOptions(chartLables) {
    const len = chartLables.length;
    const xaxisOptions = {
      tooltip: {
        enabled: false,
      },
      axisBorder: {
        show: false,
      },
      tickAmount: len > 9 ? 6 : 10,
      categories: chartLables,
      // overwriteCategories: chartLables.slice(0, 6),
      labels: {
        rotate: 0,
        rotateAlways: false,
        hideOverlappingLabels: true,
        style: {
          colors: ["#646464"],
          fontSize: "14px",
          cssClass: "apexcharts-yaxis-label",
        },
        // formatter: function(value) {
        //   if (chartLabels.length > 5) {
        //     if (value % 50 === 0) {
        //       return chartLabels[value - 1];
        //     } else {
        //       return "";
        //     }
        //   }
        //   return chartLabels[value - 1];
        // }
      },
    };
    return xaxisOptions;
  },

  yaxisOptions(urineTestItemType) {
    const yaxisOptions = {
      logBase: 1,
      title: {
        text: yaxisTitleText(urineTestItemType),
        offsetY: 80,
        offsetX: 20,
        style: {
          color: "#a7a7a7",
          fontSize: "10px",
        },
      },
      min: 1,
      max: yaxisMaxValue(urineTestItemType),
      tickAmount: yaxisMaxValue(urineTestItemType) - 1,
      labels: {
        style: {
          colors: ["#646464"],
          fontSize: "14px",
          letterSpacing: "-0.08em",
          cssClass: "apexcharts-yaxis-label",
        },
        // offsetX: 3,
        // offsetY: 0,

        formatter: (value) => {
          if (value === 1) return yaxisLabelsValue(urineTestItemType, value);
          if (value === 2) return yaxisLabelsValue(urineTestItemType, value);
          if (value === 3) return yaxisLabelsValue(urineTestItemType, value);
          if (value === 4) return yaxisLabelsValue(urineTestItemType, value);
          if (value === 5) return yaxisLabelsValue(urineTestItemType, value);
          if (value === 6) return yaxisLabelsValue(urineTestItemType, value);
        },
      },
    };

    const cym702YaxisOptions = {
      min: 0,
      max: 100,
      tickAmount: 4,
    };

    if (urineTestItemType === "cym702") {
      return cym702YaxisOptions;
    } else {
      return yaxisOptions;
    }
  },

  markersOptions(count) {
    const markersOptions = {
      strokeColor: ["#A7A7A7"],
      colors: ["#fff"],
      // size: count === "recent" ? 5 : 0,
      hover: {
        size: 5.5,
      },
    };
    return markersOptions;
  },

  gridOptions() {
    const gridOptions = {
      yaxis: {
        lines: {
          show: true,
        },
      },
      strokeDashArray: 2,
    };
    return gridOptions;
  },
};
