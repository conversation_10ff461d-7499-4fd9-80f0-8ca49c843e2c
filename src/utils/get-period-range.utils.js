export default function getPeriodRange(period) {
  const now = new Date();
  let startDate, endDate;

  if (period === "d") {
    // 오늘
    const yyyy = now.getFullYear();
    const mm = String(now.getMonth() + 1).padStart(2, "0");
    const dd = String(now.getDate()).padStart(2, "0");
    startDate = endDate = `${yyyy}-${mm}-${dd}`;
  } else if (period === "w") {
    // 이번 주 월요일 ~ 일요일
    const day = now.getDay() === 0 ? 6 : now.getDay() - 1;
    const monday = new Date(now);
    monday.setDate(now.getDate() - day);
    const sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6);

    const yyyy1 = monday.getFullYear();
    const mm1 = String(monday.getMonth() + 1).padStart(2, "0");
    const dd1 = String(monday.getDate()).padStart(2, "0");
    startDate = `${yyyy1}-${mm1}-${dd1}`;

    const yyyy2 = sunday.getFullYear();
    const mm2 = String(sunday.getMonth() + 1).padStart(2, "0");
    const dd2 = String(sunday.getDate()).padStart(2, "0");
    endDate = `${yyyy2}-${mm2}-${dd2}`;
  } else if (period === "m") {
    // 이번 달 1일 ~ 마지막일
    const yyyy = now.getFullYear();
    const mm = String(now.getMonth() + 1).padStart(2, "0");
    startDate = `${yyyy}-${mm}-01`;
    // 마지막 일 구하기
    const lastDay = new Date(yyyy, now.getMonth() + 1, 0).getDate();
    endDate = `${yyyy}-${mm}-${String(lastDay).padStart(2, "0")}`;
  } else if (period === "y") {
    // 1년 전 ~ 현재
    const yyyy = now.getFullYear();
    const m = now.getMonth() + 1;
    const lastDayOfCurrentMonth = new Date(yyyy, m + 1, 0).getDate();

    // 시작일: 현재 년도-현재 월-마지막날
    startDate = `${yyyy - 1}-${m.toString().padStart(2, "0")}-01`;

    // 종료일: 다음 년도-현재 월-01 (즉, 정확히 1년 후)
    endDate = `${yyyy}-${(m - 1).toString().padStart(2, "0")}-${String(
      lastDayOfCurrentMonth
    ).padStart(2, "0")}`;
  } else {
    // 기본: 오늘
    const yyyy = now.getFullYear();
    const mm = String(now.getMonth() + 1).padStart(2, "0");
    const dd = String(now.getDate()).padStart(2, "0");
    startDate = endDate = `${yyyy}-${mm}-${dd}`;
  }

  return { startDate, endDate };
}
