import { cym702 } from "../index";

export default {
  GetUserFilter(subjectId) {
    return cym702.get(`/solution/foods/filter/${subjectId}`);
  },
  GetFilteredFood(
    subjectId,
    page,
    diet,
    vegetarian,
    type,
    calorie,
    suggestion = false
  ) {
    return cym702.get(
      `/solution/foods/search/${subjectId}${page}${diet}${vegetarian}${type}${calorie}`,
      {
        params: {
          suggestion,
        },
      }
    );
  },
  GetSearchFood(subjectId, keyword) {
    return cym702.get(`/solution/foods/search/${subjectId}${keyword}`);
  },
  PostBookMark(subjectId, selectedFood) {
    return cym702.post(`/solution/bookmarks/${subjectId}`, selectedFood);
  },
  DeleteBookMark(subjectId, selectedFood) {
    return cym702.delete(`/solution/bookmarks/${subjectId}`, {
      data: selectedFood,
    });
  },
  GetBookMark(subjectId, page) {
    return cym702.get(`/solution/bookmarks/${subjectId}?page=${page}`);
  },
  PostFoodRecord(subjectId, type, food) {
    return cym702.post(`/solution/records/${type}/${subjectId}`, food);
  },
  GetFoodRecord(subjectId, term) {
    return cym702.get(`/solution/records/${subjectId}?${term}`);
  },
  /** params: type, subjectId, body: { records: [] } */
  DeleteFoodRecord(type, subjectId, records) {
    return cym702.delete(`/solution/records/${type}/${subjectId}`, {
      data: records,
    });
  },
  /** body: records: [ { recordId: 1, gram: 100, createdAt: 2023-02-08T09:59 } ] */
  PatchFoodRecord(type, subjectId, records) {
    return cym702.patch(`/solution/records/${type}/${subjectId}`, records);
  },
};
