import { instance } from "../index";

export default {
  fetchLoginRequest(type, userData) {
    return instance.post(`/accounts/login/general?type=${type}`, userData);
  },
  fetchSnsLoginRequest(sns, type, account) {
    return instance.post(`/accounts/login/${sns}?type=${type}`, account);
  },
  /** 문자 전송 */
  fetchSendSms(type, phoneNumber) {
    return instance.post(
      `/auth/phone/signup/send-code?type=${type}`,
      phoneNumber,
      {
        headers: {
          "x-device-id": phoneNumber.device,
        },
      }
    );
  },

  /**  인증번호 확인 > 휴대폰 번호 등록 */
  fetchVerifyAuthCode(type, authNumber) {
    return instance.post(
      `/auth/phone/signup/verify-code?type=${type}`,
      authNumber
    );
  },

  /**  general id 유효성 검사 */
  fetchCheckUserId(type, userId) {
    return instance.post(`/accounts/general/valid?type=${type}`, userId);
  },

  fetchChecknickname(nickname) {
    return instance.post("/account/valid/nickname", nickname);
  },

  /**  신규회원 설문조사 완료, 등록 */
  registerUser(type, userData) {
    return instance.post(`/accounts/general?type=${type}`, userData);
  },

  registerSnsUser(snsType, appType, userData) {
    return instance.post(`/accounts/${snsType}?type=${appType}`, userData);
  },
};
