import { cym702 } from "..";

const STEP_PATH = "/cares/step";

/**
 * 사용자의 걸음 데이터를 가져옵니다.
 * @param {number} subjectId - 사용자 ID
 * @param {string} startDate - 데이터 시작 날짜 (YYYY-MM-DD)
 * @param {string} endDate - 데이터 종료 날짜 (YYYY-MM-DD)
 * @param {number} utcOffset - UTC 오프셋 (예: 9 for KST)
 * @param {boolean} [monthly] - 월간 데이터 여부 (true면 평균 데이터 넘겨줌) (ex: true | false)
 *
 * @returns {Promise<object>} 걸음 데이터 응답 객체
 */
async function getStep(
  subjectId,
  startDate,
  endDate,
  utcOffset,
  monthly = false
) {
  const { data, status, config } = await cym702.get(
    `${STEP_PATH}/${subjectId}`,
    {
      params: {
        subjectId,
        start: startDate,
        end: endDate,
        utcOffset,
        monthly,
      },
    }
  );

  return { data, status, config };
}

export const step = {
  getStep,
};
