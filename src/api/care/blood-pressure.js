import { cym702 } from "..";

const BLOOD_PRESSURE_PATH = "/cares/blood-pressure";

export const bloodPressure = {
  getBloodPressure,
  addBloodPressureRecord,
  deleteBloodPressureRecord,
};

/**
 * 사용자의 혈압 데이터를 가져옵니다.
 *
 * @param {number | string} subjectId - 사용자 subject id
 * @param {string} start - 시작 날짜(형식: YYYY-MM-DD)
 * @param {string} end - 종료 날짜 (형식: YYYY-MM-DD)
 * @param {number} utcOffset - UTC 오프셋 (예: 9 for KST)
 * @param {boolean} [monthly] - 월간 데이터 여부 (true면 평균 데이터 넘겨줌) (ex: true | false)
 *
 *
 * @returns {Promise<Object>} 혈압 데이터 응답 객체
 */
async function getBloodPressure(
  subjectId,
  start,
  end,
  utcOffset,
  monthly = false
) {
  const response = await cym702.get(`${BLOOD_PRESSURE_PATH}/${subjectId}`, {
    params: {
      subjectId,
      start,
      end,
      utcOffset,
      monthly,
    },
  });

  return response;
}

/**
 * 사용자의 혈압 데이터를 추가합니다.
 *
 * @param {number | string} subjectId - 사용자 subject id
 *
 * @param {Object} req - 혈압 데이터
 * @param {string} req.date - 측정 날짜 (예: "2025-03-17")
 * @param {number} req.diastolic - 이완기 혈압 (mmHg)
 * @param {number} req.systolic - 수축기 혈압 (mmHg)
 * @param {number} req.pulse - 맥박 (bpm)
 * @param {number} req.mean - 평균 혈압
 * @param {string} req.comment - 사용자 메모
 * @param {string} req.resourceType - 리소스 타입 (예: "manual" 등)
 *
 */
async function addBloodPressureRecord(subjectId, req) {
  const response = await cym702.post(
    `${BLOOD_PRESSURE_PATH}/${subjectId}`,
    req
  );

  return response;
}

/**
 * 사용자의 혈압 데이터를 삭제합니다.
 *
 * @param {number | string} subjectId - 사용자 subject id
 * @param {number | string} id - 혈압 데이터 list-id
 */
async function deleteBloodPressureRecord(subjectId, id) {
  return await cym702.delete(`${BLOOD_PRESSURE_PATH}/${subjectId}`, {
    id,
  });
}
