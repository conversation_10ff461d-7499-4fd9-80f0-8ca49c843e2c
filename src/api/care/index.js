import { cym702 } from "../index";

export default {
  GetCareTargetData(subjectId) {
    return cym702.get(`/cares/target/${subjectId}`);
  },

  GetCareData(subjectId, type, start, end, utcOffset, periodType) {
    return cym702.get(
      `/cares/${type}/${subjectId}?start=${start}&end=${end}&utcOffset=${utcOffset}${periodType}`
    );
  },
  DeleteCareData(subjectId, type, id) {
    if (type === "step") {
      return cym702.delete(`/cares/step/${subjectId}`, { id: Number(id) });
    }

    return cym702.delete(`/cares/${type}/${subjectId}`, { data: id });
  },

  // care 데이터 저장, 업데이트
  FetchUpdateCareData(subjectId, type, value) {
    return cym702.post(`/cares/${type}/${subjectId}`, value);
  },

  GetPeriodData(subjectId) {
    return cym702.get(`/cares/menstruation?subjectId=${subjectId}`);
  },
  PostPeriodData(subjectId, selectedDates) {
    return cym702.post(
      `/cares/menstruation?subjectId=${subjectId}`,
      selectedDates
    );
  },
  PatchPeriodData(subjectId, selectedId, newEndDate) {
    return cym702.patch(
      `/cares/menstruation/${selectedId}?subjectId=${subjectId}`,
      newEndDate
    );
  },
  DeletePeriodData(subjectId, selectedId) {
    return cym702.delete(
      `/cares/menstruation/${selectedId}?subjectId=${subjectId}`
    );
  },
};

export * from "./step";
export * from "./blood-pressure";
