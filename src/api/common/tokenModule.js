import store from "@/store/index";
import base64 from "base-64";
import { refresh, logout } from "../user/index";

export const refreshToken = async () => {
  const token = localStorage.auth || "";
  try {
    const { data } = await refresh();
    // console.log(data);
    return data?.accessToken;
  } catch (error) {
    console.log(error);
    if (error.response?.status === 401) {
      store.commit("setLoginErrorMsg", "session");
      store.commit("setLoginError", true);
      const { status } = await logout();
      if (status === 204) {
        // TODO: error code 처리
        store.commit("LOGOUT");
      }
    }
    console.error("Failed to refresh token", error);
    throw error;
  }
};

export const isExpiringSoon = (expireAt) => {
  const expirationDate = new Date(expireAt * 1000);
  const currentDate = new Date();

  const expirationDateUTC = expirationDate.getTime();
  const currentDateUTC = currentDate.getTime();

  const secondsUntilExpiration = Math.floor((expirationDateUTC - currentDateUTC) / 1000);

  return secondsUntilExpiration < 30;
};

export const setExp = (token) => {
  const payload = token.substring(token.indexOf(".") + 1, token.lastIndexOf("."));
  const decoded = base64.decode(payload);
  const { exp } = JSON.parse(decoded);
  localStorage.exp = exp;
};
