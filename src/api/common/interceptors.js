import store from "@/store/index";
import base64 from "base-64";
import { refreshToken, isExpiringSoon } from "./tokenModule";

export function setInterceptors(instance) {
  instance.interceptors.request.use(
    async (config) => {
      const expiration = localStorage.exp;
      let token = localStorage.auth;
      // if (isExpiringSoon(expiration)) {
      //   console.log("refresh token");
      //   token = await refreshToken();
      //   const payload = token.substring(token.indexOf(".") + 1, token.lastIndexOf("."));
      //   const decoded = base64.decode(payload);
      //   const { exp } = JSON.parse(decoded);
      //   localStorage.auth = token;
      //   localStorage.exp = exp;
      // }
      config.headers["Authorization"] = `Bearer ${token}`;
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  instance.interceptors.response.use(
    (response) => {
      return response;
    },

    async (error) => {
      console.log(error.response.status);
      if (error.response.status === 401) {
        //   console.log("refresh token");
        //   const token = await refreshToken();
        //   const payload = token.substring(token.indexOf(".") + 1, token.lastIndexOf("."));
        //   const decoded = base64.decode(payload);
        //   const { exp } = JSON.parse(decoded);
        //   localStorage.auth = token;
        //   localStorage.exp = exp;
        //   location.reload();
        store.commit("setLoginErrorMsg", "session");
        store.commit("setLoginError", true);
        store.commit("LOGOUT");
        location.href = "/login";
      }

      return Promise.reject(error);
    }
  );

  return instance;
}

export function authInterceptors(instance) {
  instance.interceptors.request.use(
    (config) => {
      config.headers["Authorization"] = `Bearer ${store.state.accessToken}`;
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  instance.interceptors.response.use(
    (response) => {
      return response;
    },

    async (error) => {
      console.log(error.response.status);
      if (error.response.status === 401) {
        store.commit("setLoginErrorMsg", "session");
        store.commit("setLoginError", true);
        store.commit("LOGOUT");
        location.href = "/login";
      }

      return Promise.reject(error);
    }
  );

  return instance;
}
// export function setInterceptors(instance) {
//   instance.interceptors.request.use(
//     function(config) {
//       config.headers["Authorization"] = `Bearer ${store.state.accessToken}`;
//       return config;
//     },
//     function(error) {
//       return Promise.reject(error);
//     }
//   );

//   instance.interceptors.response.use(
//     function(response) {
//       return response;
//     },

//     function(error) {
//       if (error.response.status === 401) {
//         if (error.response.data.response_code === 205) {
//           localStorage.setItem("auth", error.response.data.accessToken);
//           location.reload();
//         } else {
//           store.commit("LOGOUT");
//           location.href = "/login";
//         }
//       }

//       return Promise.reject(error);
//     }
//   );

//   return instance;
// }
