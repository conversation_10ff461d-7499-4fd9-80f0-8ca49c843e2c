import { cym702 } from "../index";

export default {
  getSurveyStatus() {
    return cym702.get("/surveys");
  },
  /** path: surveyId */
  getSurveyData(id) {
    return cym702.get(`/surveys/${id}`);
  },
  /** query: surveyId & subjectId */
  getSurveyCheckedData(idParams) {
    return cym702.get(`/surveys/answers?${idParams}`);
  },
  getSurveyCheckedData1(idParams) {
    return cym702.get(`/surveys/answers1?${idParams}`);
  },
  /** body: subjectId: number, answer: [ { questionId: number, choiceAnswers: [number], textAnswer: string } ] */
  postSurveyData(survey) {
    return cym702.post("/surveys/answers", survey);
  },
  putSurveyData(survey) {
    return cym702.put("/surveys/answers", survey);
  },
  /** body: subjectId: number, choiceAnswerId: [number], textAnswerId: [number] */
  deleteSurveyData(survey) {
    return cym702.delete("/surveys/answers", { data: survey });
  },
  patchSurveyComplete() {
    return cym702.patch("/surveys/complete");
  },
};
