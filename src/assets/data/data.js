import i18n from "../../i18n.js";

const ketoneMode = JSON.parse(localStorage.getItem("ketoneMode"));
// detail info: modal contents -> _modal
// txt -> _level

const explainations = {
  blood: i18n.t("blood_info"),
  glucose: i18n.t("glucose_info"),
  protein: i18n.t("protein_info"),
  ph: i18n.t("ph_info"),
  ketone: i18n.t("ketone_info"),
};

// const bloodDetailInfo = {
//   negative: i18n.t("blood_good_level_modal"),
//   warning: i18n.t("blood_warning_level_modal"),
//   caution: i18n.t("blood_caution_level_modal"),
//   danger: i18n.t("blood_danger_level_modal"),
// };

// const glucoseDetailInfo = {
//   negative: i18n.t("glucose_negative_level_modal"),
//   warning: i18n.t("glucose_warning_level_modal"),
//   caution: i18n.t("glucose_caution_level_modal"),
//   danger: i18n.t("glucose_danger_level_modal"),
// };

// const proteinDetailInfo = {
//   negative: i18n.t("protein_negative_level_modal"),
//   warning: i18n.t("protein_warning_level_modal"),
//   caution: i18n.t("protein_caution_level_modal"),
//   danger: i18n.t("protein_danger_level_modal"),
// };

// /** 당뇨모드 */
// const ketonDetailInfo = !ketoneMode
//   ? {
//       negative: i18n.t("negative"),
//       caution_plus_minus: i18n.t("ketone_warning_level_plus_minus"),
//       caution_plus: i18n.t("ketone_warning_level_plus"),
//       warning: i18n.t("caution"),
//       danger: i18n.t("danger"),
//     }
//   : {
//       exertion: i18n.t("exertion"), // 분발
//       enter: i18n.t("enter"), // 진입
//       ketone_normal: i18n.t("ketone_normal"), // 적절
//       ketone_great_level: i18n.t("ketone_great_level"), // 좋은
//       warning: i18n.t("warning"), // 주의
//     };

/** 케톤모드 */
// const ketonDetailInfo = {
//   exertion: i18n.t("ketone_good_level_modal"),
//   normal: i18n.t("ketone_normal_level_modal"),
//   good: i18n.t("ketone_caution_level_modal"),
//   warning: i18n.t("ketone_warning_level_modal"),
// };

// const phDetailInfo = {
//   negative: i18n.t("ph_good_level_modal"),
//   positive: i18n.t("ph_warning_level_modal"),
// };

const bloodTxt = {
  negative: i18n.t("blood_good_level"),
  positive_plus: i18n.t("blood_warning_level"),
  positive_double: i18n.t("blood_caution_level"),
  positive_triple: i18n.t("blood_danger_level"),
};

const glucoseTxt = {
  negative: i18n.t("glucose_negative_level"),
  positive_plus: i18n.t("glucose_positive_plus_level"),
  positive_double: i18n.t("glucose_positive_double_level"),
  positive_triple: i18n.t("glucose_positive_triple_level"),
  positive_quadruple: i18n.t("glucose_positive_quadruple_level"),
};

const proteinTxt = {
  negative: i18n.t("protein_good_level"),
  positive_plus: i18n.t("protein_positive_plus_level"),
  positive_double: i18n.t("protein_positive_double_level"),
  positive_triple: i18n.t("protein_positive_triple_level"),
  positive_quadruple: i18n.t("protein_positive_quadruple_level"),
};

const phTxt = {
  negative: i18n.t("ph_normal_level"),
  positive: i18n.t("ph_warning_level"),
};

const ketoneTxt = !ketoneMode
  ? {
      /** 당뇨모드 */
      negative: i18n.t("ketone_negative_txt"), // 음성
      positive_plus_minus: i18n.t("ketone_warning_level_plus_minus_txt"), // 양성(+/-)
      positive_plus: i18n.t("ketone_warning_level_plus_txt"), // 양성(+)
      positive_double: i18n.t("ketone_caution_level_txt"), // 양성(++)
      positive_triple: i18n.t("ketone_danger_level_txt"), // 양성(+++)
    }
  : {
      /** 케토시스모드 */
      exertion: i18n.t("ketone_good_level"), // 적절
      enter: i18n.t("ketone_normal_level"), // 주의(+/-)
      ketone_normal: i18n.t("ketone_normal_explain"), // 주의(+)
      ketone_great_level: i18n.t("ketone_caution_level"), // 경고
      warning: i18n.t("ketone_warning_level"), // 위험
    };

const cymScoreResultTxt = (score, obj) => {
  // console.log(score);
  switch (score) {
    case "negative":
      return obj.negative;
    case "positive":
      return obj.positive;
    case "positive-plus":
      return obj.positive_plus;
    case "positive-double":
      return obj.positive_double;
    case "positive-triple":
      return obj.positive_triple;
    case "positive-quadruple":
      return obj.positive_quadruple;
    case "caution":
      return obj.caution;
    case "danger":
      return obj.danger;
    default:
      break;
  }
};

const ketoneResultTxt = (score, obj) => {
  if (!ketoneMode) {
    /** 당뇨 모드 */
    switch (score) {
      case "negative":
        return obj.negative;
      case "positive-plus-minus":
        return obj.positive_plus_minus;
      case "positive-plus":
        return obj.positive_plus;
      case "positive-double":
        return obj.positive_double;
      case "positive-triple":
        return obj.positive_triple;
      default:
        break;
    }
  } else {
    /** 케토시스 모드 */
    switch (score) {
      case "exertion":
        return obj.exertion;
      case "enter":
        return obj.enter;
      case "ketone_normal":
        return obj.ketone_normal;
      case "ketone_good":
        return obj.ketone_great_level;
      case "ketone_warning":
        return obj.warning;
      default:
        break;
    }
  }
};

const historyTypes = (type, score) => {
  switch (type) {
    case "blood":
      return cymScoreResultTxt(score, bloodTxt);
    case "glucose":
      return cymScoreResultTxt(score, glucoseTxt);
    case "protein":
      return cymScoreResultTxt(score, proteinTxt);
    case "ph":
      return cymScoreResultTxt(score, phTxt);
    case "ketone":
      return ketoneResultTxt(score, ketoneTxt);
    default:
      break;
  }
};

export {
  explainations,
  // bloodDetailInfo,
  // glucoseDetailInfo,
  // proteinDetailInfo,
  // ketonDetailInfo,
  // phDetailInfo,
  bloodTxt,
  glucoseTxt,
  proteinTxt,
  phTxt,
  ketoneTxt,
  historyTypes,
};
