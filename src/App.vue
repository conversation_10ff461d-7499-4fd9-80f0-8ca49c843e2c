<template>
  <v-app class="no-drag" :style="fontStyles">
    <v-main>
      <ErrorModal v-if="showErrorModal" :error="error" @isClicked="isClicked" />
      <router-view />
    </v-main>
  </v-app>
</template>

<script>
import ErrorModal from "@/components/Common/ErrorModal.vue";

export default {
  name: "App",
  components: { ErrorModal },
  data() {
    return { error: this.$i18n.t("over_request") };
  },
  computed: {
    fontStyles() {
      return {
        "font-family": this.fontFamily,
      };
    },

    fontFamily() {
      return this.$i18n.locale === "ko" ? "Noto Sans KR" : "GilroyMedium";
    },

    showErrorModal() {
      return this.$store.state.overReqModalOpen;
    },
  },
  methods: {
    isClicked() {
      this.$store.commit("setoverReqModal", false);
    },
  },
};
</script>

<style lang="scss">
@import "@/styles/global.scss";

body,
html {
  font-size: 18px;
  letter-spacing: -0.03em !important;
  accent-color: #41d8e6;
}

img,
a {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

img {
  width: 100%;
}

button,
select {
  color: #000000;
}

.no-scroll {
  width: 100vw;
  height: 100%;
  position: fixed;
}

.vc-container {
  border: none !important;
}

b,
strong {
  font-weight: 700 !important;
}

sub,
sup {
  font-size: 50% !important;
  top: -0.7em !important;
}
#app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  width: 100% !important;
  max-width: 450px !important;
  height: 100%;
  margin: auto;
}

@media screen and (min-width: 789px) {
  #app {
    // display: none !important;
  }
}
</style>
