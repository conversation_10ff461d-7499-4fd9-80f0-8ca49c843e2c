@font-face {
  font-family: "Noto Sans KR";
  font-style: normal;
  font-weight: 400;
  src: url(//fonts.gstatic.com/ea/notosanskr/v2/NotoSansKR-Regular.woff2) format("woff2"),
    url(//fonts.gstatic.com/ea/notosanskr/v2/NotoSansKR-Regular.woff) format("woff"),
    url(//fonts.gstatic.com/ea/notosanskr/v2/NotoSansKR-Regular.otf) format("opentype");
}

@font-face {
  font-family: "Noto Sans KR";
  font-style: normal;
  font-weight: 500;
  src: url(//fonts.gstatic.com/ea/notosanskr/v2/NotoSansKR-Medium.woff2) format("woff2"),
    url(//fonts.gstatic.com/ea/notosanskr/v2/NotoSansKR-Medium.woff) format("woff"),
    url(//fonts.gstatic.com/ea/notosanskr/v2/NotoSansKR-Medium.otf) format("opentype");
}

@font-face {
  font-family: "Noto Sans KR";
  font-style: normal;
  font-weight: 700;
  src: url(//fonts.gstatic.com/ea/notosanskr/v2/NotoSansKR-Bold.woff2) format("woff2"),
    url(//fonts.gstatic.com/ea/notosanskr/v2/NotoSansKR-Bold.woff) format("woff"),
    url(//fonts.gstatic.com/ea/notosanskr/v2/NotoSansKR-Bold.otf) format("opentype");
}

@font-face {
  font-family: "GilroyBold";
  src: url("/fonts/GilroyExtraBold.woff") format("woff");
}

@font-face {
  font-family: "GilroyMedium";
  src: url("/fonts/GilroyMedium.woff") format("woff");
}

$black: #000000;
$danger: #646464;
$green: #00bb00;
$yellow: #ffcc00;
$red: #ff6600;

$ketone-0: #00bb00;
$ketone-1: #00bb00;
$ketone-2: #41d8e6;
$ketone-3: #ffcc00;

$primary: #41d8e6;
$lightblue: #c9f4f8;
$background: linear-gradient(
  180deg,
  #c9f4f8 0%,
  #e0f9fb 20.31%,
  #f6fdfe 38.54%,
  #fcfeff 80.73%,
  #ffffff 100%
);

$logo: #dadada;
$disable: #ededed;
$date-color: #a7a7a7;
$link-color: #060505;
$yellosis-gray-5: #323232;
$yellosis-gray-3: #858585;

html {
  font-size: calc(100vw / 22) !important;
}

body {
  // background: linear-gradient(
  //   180deg,
  //   #9cebf2 0%,
  //   #9cebf2 0.01%,
  //   #b2eff5 0.02%,
  //   #c9f4f8 19.27%,
  //   #fcfeff 75.62%,
  //   #ffffff 100%
  // );
}

/*
padding-top : pt- 
padding-bottom : pb-
padding-left : pl-
padding-right : pr-
padding-top bottom : p-
padding-left right : pr-
margin-top ; mt-
margin-bottom : mb-
margin-left : ml-
margin-right : mr-
margin-right left : mr-ml-9
default-padding: dp-
flex-end : f-e
flex-start : f-s
*/
.v-application a {
  color: #000;
}
a {
  text-decoration: none;
}
.dp-30 {
  padding: 0px 1.875rem;
}

// padding-top
.pt-10 {
  padding-top: calc(100vh / 75);
}

.pt-21 {
  padding-top: calc(100vh / 35);
}

.pt-30 {
  padding-top: calc(100vh / 25);
}

.pt-40 {
  padding-top: calc(100vh / 18);
}

.pt-45 {
  padding-top: calc(100vh / 16.2);
}

.pt-50 {
  padding-top: calc(100vh / 15);
}

.pt-55 {
  padding-top: calc(100vh / 13.5);
}

.pt-60 {
  padding-top: calc(100vh / 12);
}

.pt-72 {
  padding-top: calc(100vh / 10.2) !important;
}

.pt-75 {
  padding-top: calc(100vh / 10);
}

// padding-bottom
.pb-10 {
  padding-bottom: calc(100vh / 70);
}

.pd-20 {
  padding-bottom: calc(100vh / 35);
}

.pd-30 {
  padding-bottom: calc(100vh / 25);
}

.pd-40 {
  padding-bottom: calc(100vh / 18);
}

.pb-49 {
  padding-bottom: calc(100vh / 15);
}

.pb-60 {
  padding-bottom: calc(100vh / 12);
}

.pb-100 {
  padding-bottom: calc(100vh / 7.8);
}

.pb-120 {
  padding-bottom: 120px;
}

.p-25 {
  padding: calc(100vh / 29) 0px;
}

.p-15 {
  padding: calc(100vh / 49) 0px;
}

.mr-ml-9 {
  margin: 0px 0.552rem;
}

.mt-38 {
  margin-top: 2.331rem;
}

.en-title {
  font-weight: 400 !important;
  font-family: GilroyBold !important;
}

.v-input--selection-controls__ripple {
  display: none !important;
}

.apexcharts-line {
  filter: none !important;
}

// snackbar custom
.snackbar {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 10px);
  z-index: 10000;
}

::v-deep .v-snack__wrapper {
  min-width: 250px;
  width: 230px !important;
  height: 55px;
  max-width: 230px;
  white-space: nowrap;
}
::v-deep .v-sheet.v-snack__wrapper:not(.v-sheet--outlined) {
  box-shadow: none;
}

::v-deep .v-snack__wrapper.theme--dark {
  background-color: #000000;
  opacity: 0.8 !important;
  margin: 0;
}

::v-deep .v-sheet.v-snack__wrapper {
  border-radius: 6px;
}

::v-deep .v-snack__content {
  padding: 0 0 3px 10px !important;
  // width: 210px !important;
  line-height: 29px;
  display: flex;
  justify-content: center;
  text-align: center;
  font-size: 20px;
  font-weight: 500;
  letter-spacing: -0.03em;
}
