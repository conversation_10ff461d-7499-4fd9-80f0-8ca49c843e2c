.mystate-indicator {
  display: flex;
  border-radius: 5px;
  height: 30px;
  background-color: $disable;
  margin: 10px 0px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

.indicator__item {
  flex: 1;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
  color: $date-color;
}

/* cym 702 state bar */
.cym702-good {
  @include cymStateBar($primary);
  border-radius: 5px 0px 0px 5px;
}

.cym702-normal {
  @include cymStateBar($green);
  border-radius: 0px;
}

.cym702-warning {
  @include cymStateBar($yellow);
}

.cym702-caution {
  @include cymStateBar($red);
  border-radius: 0px 5px 5px 0px;
}

/* blood, protein, glucose state bar */
.cym-normal {
  @include cymStateBar($green);
  border-radius: 5px 0px 0px 5px;
}

.cym-caution {
  @include cymStateBar($red);
}

.cym-warning {
  @include cymStateBar($yellow);
}

.cym-danger {
  @include cymStateBar($danger);
  border-radius: 0px 5px 5px 0px;
}

/* ph state bar */
.ph-warning {
  @include cymStateBar($yellow);

  border-radius: 0px 5px 5px 0px;
}

.ph-good {
  @include cymStateBar($green);
  border-radius: 5px 0px 0px 5px;
}

/* ketone state bar */
.exertion {
  @include ketoneStateBar($ketone-0);
  border-radius: 5px 0px 0px 5px;
}

.ketone-normal {
  @include ketoneStateBar($ketone-1);
}

.ketone-good {
  @include ketoneStateBar($ketone-2);
}

.ketone-warning {
  border-radius: 0px 5px 5px 0px;
  @include ketoneStateBar($ketone-3);
}
