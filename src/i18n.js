import Vue from "vue";
import VueI18n from "vue-i18n";
import { notice, common, faq, bloodPressure } from "@/locales";
import { common_en, bloodPressure as bloodPressure_en } from "@/locales";

Vue.use(VueI18n);

function loadLocaleMessages() {
  const locales = require.context(
    "./locales",
    true,
    /[A-Za-z0-9-_,\s]+\.json$/i
  );
  const messages = {
    ko: { notice, ...common, ...faq, ...bloodPressure },
    en: { ...common_en, ...bloodPressure_en },
  };
  locales.keys().forEach((key) => {
    const matched = key.match(/([A-Za-z0-9-_]+)\./i);
    if (matched && matched.length > 1) {
      const locale = matched[1];
      messages[locale] = locales(key);
    }
  });
  return messages;
}

// get the URL query string parameters
const params = new URLSearchParams(window.location.search);
let lang = "";

// check if the lang parameter is present
if (params.has("lang")) {
  // set the locale to the language code specified in the URL
  lang = params.get("lang");
}
// console.log("params lang:", lang);

const userLanguage = localStorage.getItem("locale");
const language = navigator.language.split(/-|_/);
// console.log(language);

export default new VueI18n({
  locale: lang || language[0] || userLanguage || "ko",
  // locale: "en",
  fallbackLocale: "en",
  messages: loadLocaleMessages(),
});
