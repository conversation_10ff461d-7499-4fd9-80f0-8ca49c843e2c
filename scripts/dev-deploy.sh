#!/bin/bash

# 개발 환경 배포 스크립트
echo "🚀 개발 환경 배포를 시작합니다..."

# Node 버전 맞추기 (예: .nvmrc 파일이 있으면 자동 적용)
echo "Node 버전 맞추기 (nvm use)"
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm use

# node 버전 확인
echo "Node 버전 확인"
node -v

# 1. Build 실행
echo "📦 빌드를 시작합니다..."
npm run build:dev

# 빌드 실패 시 스크립트 종료
if [ $? -ne 0 ]; then
    echo "❌ 빌드에 실패했습니다. 배포를 중단합니다."
    exit 1
fi

echo "✅ 빌드가 완료되었습니다."

# 2. 배포 실행
echo "🚀 배포를 시작합니다..."
npm run deploy-dev

# 배포 실패 시 스크립트 종료
if [ $? -ne 0 ]; then
    echo "❌ 배포에 실패했습니다. 캐시 무효화를 건너뜁니다."
    exit 1
fi

echo "✅ 배포가 완료되었습니다."

# 3. 캐시 무효화 실행
echo "🔄 캐시 무효화를 시작합니다..."
npm run invalidate-dev

clear

# 캐시 무효화 실패 시 경고만 표시 (배포는 성공했으므로)
if [ $? -ne 0 ]; then
    echo "⚠️  캐시 무효화에 실패했습니다. 수동으로 확인해주세요."
else
    echo "✅ 캐시 무효화가 완료되었습니다."
fi

echo "🎉 개발 환경 배포 프로세스가 완료되었습니다!"